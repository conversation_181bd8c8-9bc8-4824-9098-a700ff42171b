<template>
  <div class="doc-container">
    <Title :title="'新建公文'"></Title>
    <div class="m_t_20">
      <el-form :model="listQuery" :rules="rules" ref="formData">
        <el-form-item label="标题" prop="noticeTitle">
          <el-input placeholder="请输入标题"
                    v-model="listQuery.noticeTitle"></el-input>
        </el-form-item>
        <el-form-item label="上传附件">
          <upload-file @uploadSuccess="uploadSuccess"></upload-file>
        </el-form-item>
        <div class="flex just_between">
          <el-form-item style="flex:1" prop="noticeType" label="公文类型">
            <el-select v-model="listQuery.noticeType"
                       style="width:100%"
                       clearable>
              <el-option v-for="item in dict.type['notice_type']"
                         :label="item.label"
                         :value="item.value"
                         :key="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item style="flex:1" class="m_l_20"
                        prop="selectPeoples"
                        label="接收单位">
            <el-select v-model="listQuery.selectPeoples"
                       @click.native="showSelect"
                       style="width:100%">
              <el-option :label="userInfo" :value="listQuery.selectPeoples"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="文号">
          <div class="flex aligin_center">
            <div>
              <el-switch class="define-switch"
                         v-model="showNum"
                         :active-value="true"
                         :inactive-value="false"
                         active-text="开"
                         inactive-text="关"
                         active-color="#3B8989"
                         inactive-color="#666666"
                         @change="changeSwitch()"
              ></el-switch>
            </div>
            <el-select v-show="showNum" class="m_l_10"
                       v-model="listQuery.numberAgency"
                       style="width:42%"
                       @change="changeBeforeNo"
            >
              <el-option v-for="item in beforeList"
                         :label="item.before"
                         :value="item.id"
                         :key="item.id"></el-option>
            </el-select>
            <div v-show="showNum&&listQuery.numberAgency"
                 class="m_l_20" style="width:25%">
              <el-input-number v-model="listQuery.numberYear"
                               :min="1" :max="99999">
              </el-input-number>&nbsp;年
            </div>
            <div v-show="showNum&&listQuery.numberAgency" style="width:25%">
              <el-input-number v-model="listQuery.numberRef"
                               :min="1" :max="99999">

              </el-input-number>&nbsp;号
            </div>
            <!--            <div style="height:1px;width:1px;"></div>-->
          </div>
        </el-form-item>
        <el-form-item label="办理意见">
          <el-input v-model="listQuery.advice" type="textarea" :rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button class="btn-back" @click="goBack">返回</el-button>&nbsp;&nbsp;
        <el-button class="btn-submit" @click="submit">发送</el-button>
      </div>
    </div>
    <select-dialog ref="selectRef"
                   :title="title"
                   :dialog-width="840"
                   @confirm="saveConfirm"></select-dialog>
  </div>
</template>
<script>
import Title from '@/components/Title.vue'
import UploadFile from "@/views/OA/component/uploadFile.vue";
import {addOa, beforeList, getDocNo} from "@/api/OA";
import {before} from "lodash";
import SelectDialog from "@/views/OA/component/selectDialog.vue";
import {mapGetters} from 'vuex'
export default {
  components: {
    SelectDialog,
    UploadFile,
    Title
  },
  dicts: ['notice_type'],
  data() {
    return {
      userInfo: '',
      listQuery: {
        advice: '',
        noticeTitle: '',
        noticeType: '',
        numberAgency: '',
        numberFlag: false,
        numberRef: '',
        numberYear: '',
        status: '0',
        files: [],
        urgentLevel: '0',
        autoRemind: '0',
        receivePeoples: '',
        selectPeoples:'',
        agencyId: '',
        deptId: '',
      },
      showNum: false,
      rules: {
        noticeTitle: [
          {
            required: true, message: '请输入标题', trigger: 'blur'
          }
        ],
        noticeType: [
          {
            required: true, message: '请选择公文类型', trigger: 'change'
          }
        ],
        selectPeoples: [
          {
            required: true, message: '请选择接收单位', trigger: 'change'
          }
        ]
      },
      beforeList: [],
      title: ''
    }
  },
  computed:{
    ...mapGetters(['role'])
  },
  methods: {
    before,
    goBack() {
      this.$router.push({
        path: "/oa"
      })
    },
    uploadSuccess(info) {
      this.listQuery.files.push(info)
    },
    changeSwitch() {
      this.listQuery.numberFlag= this.showNum ? 1 : 0;
      beforeList().then(res => {
        this.beforeList = res.rows
      })
    },
    // 文号选择触发
    async changeBeforeNo() {
      this.listQuery.numberYear = new Date().getFullYear();
      const result = await getDocNo({
        beforeId: this.listQuery.numberAgency,
        year: this.listQuery.numberYear,
      });
      this.listQuery.numberRef = result.data;
    },
    // 接收单位
    showSelect() {
      this.title = '接收单位'
      this.$refs.selectRef.showDialog = true
    },
    // 选择框回调
    saveConfirm(data) {
      let userList = _.clone(data).slice(0, 6);
      if (data.length > 6) {
        this.userInfo =
            userList
                .map((item) => {
                  return item.userName;
                })
                .toString() +
            '等' +
            data.length +
            '人';
      } else {
        this.userInfo = userList
            .map((item) => {
              return item.userName;
            })
            .toString();
      }
      this.listQuery.selectPeoples=data[0].agencyId
      this.listQuery.receivePeoples = data.map(item => {
        return {
          signBy: item.userId,
          deptId: item.deptId,
          agencyId: item.agencyId,
        };
      })
    },
    submit() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.listQuery.deptId = this.role.dept_id ? this.role.dept_id : '';
          this.listQuery.agencyId = this.role.agency_id;
          let params=this.listQuery
          delete params.selectPeoples
          if(this.showNum&&this.listQuery.numberAgency){
            addOa(params).then((res) => {
              if (res.code === 200) {
                this.$message({
                  message:'发送成功',
                  type:'success'
                })
                setTimeout(() => {
                  this.$router.push({
                    path:'/oa'
                  })
                }, 1000);
              }
            });
          }else{
            this.$message({
              message:'请选择文号',
              type:'warning'
            })
          }
          console.log(this.listQuery)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.doc-container {
  font-size: 16px;
  padding: 20px;
  border-radius: 5px;
  background: #fff;

  .btn-back {

  }

  .btn-submit {
    color: #fff !important;
    background: #3B8989 !important;
  }
}

//开关样式
::v-deep.define-switch {
  .el-switch__core {
    width: 48px !important;
  }

  .el-switch__label--left {
    position: absolute;
    left: 24px;
    color: #fff;
    z-index: -1111;
    cursor: default;
    user-select: none;
  }

  .el-switch__label--right {
    position: absolute;
    right: 24px;
    color: #fff;
    z-index: -1111;
    cursor: default;
    user-select: none;
  }

  .el-switch__label--right.is-active {
    z-index: 1111;
    color: #fff !important;
  }

  .el-switch__label--left.is-active {
    z-index: 1111;
    color: #9c9c9c !important;
  }
}
</style>
