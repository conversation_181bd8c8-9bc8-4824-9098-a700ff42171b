<template>
  <div class="item-container">
    <el-card class="box-card card-box" :body-style="{padding:'10px'}">
      <div slot="header" class="clearfix">
        <div class="fs_18 fw_700">{{ info.noticeTitle }}</div>
        <div class="title-class flex_center">
          <div>发文单位:&nbsp;</div>
          <div style="width:65%" class="pointer">
            <tooltip :title="info.agencyName"></tooltip>
          </div>
        </div>
        <div class="title-class">发文时间: {{ dayjs()(info.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
      </div>
      <div class="btn-click">
        <span
              class="pointer"
              @click="btnClick">查看</span>
<!--        <span v-if="btnType===0"-->
<!--              class="pointer"-->
<!--              @click="btnClick">{{ info.signFlag==='0'?'待签收':'转发' }}</span>-->
<!--        <span v-if="btnType===1"-->
<!--              class="pointer"-->
<!--              @click="btnClick">{{ info.status==='0'?'待审核':info.status==='1'?'撤销':'已撤销' }}</span>-->
<!--        <span v-if="btnType===2"-->
<!--              class="pointer"-->
<!--              @click="btnClick">{{ info.status==='0'?'待审核':'已审核'}}</span>-->
      </div>
      <div class="tag">
        <dict-tag
            :options="dict.type.notice_type"
            :value="info.noticeType"/>
      </div>
    </el-card>
  </div>
</template>
<script>
import dayjs from "dayjs";
import Tooltip from "@/components/Tooltip.vue";

export default {
  components: {
    Tooltip
  },
  dicts: ['notice_type'],
  props: {
    btnType: {
      type: Number,
      default: 0
    },
    info: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    dayjs() {
      return dayjs
    },
    btnClick() {
      if (this.btnType === 0) {
        //   转发
        this.$router.push({
          path: '/oa/audit',
          query: {
            type: 0,
            id: this.info.contentId
          }
        })
      } else if (this.btnType === 1) {
        //   撤销
        this.$router.push({
          path: '/oa/audit',
          query: {
            type: 1,
            id: this.info.contentId
          }
        })

      } else if (this.btnType === 2) {
        //   审核
        this.$router.push({
          path: '/oa/audit',
          query: {
            type: 2,
            id: this.info.contentId
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.item-container {
  width: 270px;

  .title-class {
    color: rgb(217, 220, 225);
    font-size: 15px;
    margin-top: 5px;
  }

  .btn-click {
    text-align: center;
    //color: #0F4444;
    font-size: 15px;
    //font-weight: 550;
    //font-weight: bold;
  }

  .card-box {
    position: relative;
  }

  .tag {
    position: absolute;
    font-size: 14px;
    color: #fff;
    padding: 5px 15px;
    right: 0;
    top: 0;
    background: #6A94FF;
    border-radius: 0px 4px 4px 4px;
  }
}

</style>
