<template>
  <el-dialog class="reset_dialog"
             :visible.sync="showDialog"
             append-to-body
             :width="dialogWidth+'px'"
             :show-close="false"
             @open="handleOpen"
             @close="dialogClosed"
  >
    <template slot="title">
      <div class="flex_space">
        <div class="fs_bold">{{ title }}</div>
        <div class="closeIcon pointer" @click="dialogClosed">
          <i class="el-icon-error icon fs_18"></i>
        </div>
      </div>
    </template>

    <div v-if="false" class="flex_space">
      <div class="left-box">
        <div class="left-content" v-if="show===1">
          <div class=" flex_center pointer"
               :class="current.agencyId === item.agencyId ? 'list active' : 'list'"
               v-for="(item,index) in agency"
               @click="clickFirst(item)"
               :key="index">
            <img src="@/assets/images/2470.png">&nbsp;
            <div>{{ item.agencyName }}</div>
          </div>
        </div>

        <div v-if="show===2">
          <div>{{ current.agencyName }}</div>
          <div class="left-content">
            <div class=" m_t_10 flex_center pointer list" v-for="item in agencyLists"
                 :key="item.agencyId"
                 @click="agencyCurrent = item">
              <img style="width:16px;height:16px;"
                   :src="agencyCurrent.agencyId === item.agencyId ? icon : selectIcon">
              <div>{{ item.agencyName }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-box">
        <div class="right-content">
          <div v-if="show===1">
            <div style="text-align: left">已选择:</div>
            <div v-if="current" class="flex_center pointer m_t_10 list">
              <img src="@/assets/images/2470.png">&nbsp;
              <div>{{ current.agencyName }}</div>
            </div>
          </div>

          <div v-if="show===2">
            <div style="text-align: left">已选择:</div>
            <div v-if="agencyCurrent" class=" m_t_10 flex_center pointer list">
              <img style="width:16px;height:16px;"
                   :src="icon">
              <div>{{ agencyCurrent.agencyName }}</div>
            </div>
          </div>
        </div>
        <div class="btn-box" style="text-align: center">
          <el-button
              class="cancel"
              @click="cancelClick">取消
          </el-button>&nbsp;&nbsp;
          <el-button
              class="submit"
              @click="submitClick">确定
          </el-button>
        </div>
      </div>
    </div>

    <group v-if="show===0" @cancel="handleCancel" @save="handleSave"></group>
    <!-- 机构内用户-->
    <organization v-if="show===1.1" :type="1" @cancel="handleCancel" @save="handleSave" @handleBack="handleBack"></organization>
    <!-- 机构列表（跨机构用户）-->
    <organization v-if="show===1.2" :type="2" @cancel="handleCancel" @save="handleSave"></organization>
    <!-- 常用分组(教研组用户)-->
    <organization v-if="show===1.3" :type="3" @cancel="handleCancel" @save="handleSave"></organization>

    <!--    -->
    <department v-if="show === 2" @cancel="handleCancel" @save="handleSave"></department>
    <!--    -->
    <personal v-if="show===3" @cancel="handleCancel" @save="handleSave" @deptInfo="deptInfo"></personal>
  </el-dialog>
</template>
<script>
import group from "@/views/OA/component/groups/group.vue";
import {mapGetters} from 'vuex';
import {agencyList, getDeptTree, groupList} from "@/api/OA";
import organization from "@/views/OA/component/groups/organization.vue";
import department from "@/views/OA/component/groups/department.vue";
import Personal from "@/views/OA/component/groups/personal.vue";

export default {
  components: {
    Personal,
    organization,
    department,
    group
  },
  props: {
    dialogWidth: {
      type: Number,
      default: 0
    },
    title: {
      type: String,
      default: ''
    },
    info: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  data() {
    return {
      agency: [
        {
          agencyName: '机构内用户',
          agencyId: 1,
          type: 1
        },
        {
          agencyName: '跨机构用户',
          agencyId: 2,
          type: 2
        },
        {
          agencyName: '常用分组',
          agencyId: 3,
          type: 3
        },
      ],
      show: 0,
      current: '',
      showDialog: false,
      agencyLists: [],
      selectIcon:
          'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCI+PHBhdGggZD0iTTUxMiA4NTQuMDE2cTEzOS45OSAwIDI0MC45ODEtMTAwLjk5MnQxMDAuOTkyLTI0MC45ODFUNzUyLjk4MSAyNzEuMDYgNTEyIDE3MC4wNyAyNzEuMDE5IDI3MS4wNjEgMTcwLjAyNyA1MTIuMDQzdDEwMC45OTIgMjQwLjk4MVQ1MTIgODU0LjAxNnptMC03NjhxMTc2IDAgMzAxLjAxMyAxMjUuMDEzdDEyNS4wMTQgMzAxLjAxNC0xMjUuMDE0IDMwMS4wMTNUNTEyIDkzOC4wNjkgMjEwLjk4NyA4MTMuMDU2IDg1Ljk3MyA1MTIuMDQzdDEyNS4wMTQtMzAxLjAxNFQ1MTIgODYuMDE2eiIgZmlsbD0iIzhhOGE4YSIvPjwvc3ZnPg==',
      icon: require('@/assets/images/dian.png'),
      agencyCurrent: '',//机构列表
    }
  },
  computed: {
    ...mapGetters(['role']),
  },
  methods: {
    handleOpen() {
    },
    dialogClosed() {
      this.current = ''
      this.agencyCurrent = ''
      this.show = 0
      this.showDialog = false
    },
    handleCancel() {
      this.showDialog = false
    },
    handleSave(e) {
      this.show = e
    },
    handleBack(){
      this.show=0
    },
    clickFirst(item) {
      this.current = item
    },
    cancelClick() {
      this.dialogClosed()
    },
    // 提交
    // submitClick() {
    //   if (this.show === 1) {
    //     this.show = 2
    //     if (this.current.type === 1) {
    //       getDeptTree({agencyId: this.role.agency_id}).then(res => {
    //         this.agencyLists = res.data
    //       })
    //     } else if (this.current.type === 2) {
    //       agencyList().then(res => {
    //         this.agencyLists = res.data
    //       })
    //     } else if (this.current.type === 3) {
    //       groupList().then(res => {
    //         this.agencyLists = res.data
    //       })
    //     }
    //     let params = [
    //       {
    //         leave: 0,
    //         name: '接收单位',
    //         fullName: this.current.agencyName,
    //         type: this.current.agencyId
    //       },
    //     ];
    //     this.$store.dispatch('SetRouter', params);
    //   } else if (this.show === 2) {
    //     this.show = 3
    //     let params = [
    //       {
    //         leave: 1,
    //         name: '接收机构',
    //         fullName: this.agencyCurrent.agencyName,
    //         id: this.agencyCurrent.agencyId,
    //         type: this.current.type
    //       },
    //     ];
    //     this.$store.dispatch('SetRouter', params);
    //   }
    // },
    //   选中用户
    deptInfo(info) {
      this.$emit('confirm',info)
      this.dialogClosed()
    }
  }
}
</script>
<style lang="scss" scoped>
.reset_dialog {
  font-size: 16px;

  .closeIcon {
    font-size: 20px;
    cursor: pointer;
    opacity: .8;
    color: rgb(112, 112, 112);
    top: 5px;
  }

  .list {
    height: 40px;
    background: #f6fafb;
    border-radius: 7px;
    margin-bottom: 12px;
    align-items: center;
    display: flex;
    padding: 0 12px;
    color: #000000;

    image {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
  }

  .left-box {
    width: 48%;
    height: 400px;
    padding: 0 10px;
    border-right: 1px solid #EAEAEA;

    .left-content {
      overflow-y: auto;
      height: 390px;
    }

    .active {
      background: #a2c8c8 !important;
    }
  }

  .left-box::-webkit-scrollbar {
    display: none;
  }

  .right-box {
    width: 50%;
    overflow-y: auto;
    padding: 0 10px;
    height: 400px;

    .right-content {
      height: 85%;
    }

    .btn-box {
      .cancel {
        background: #fff !important;
      }

      .submit {
        background: #3B8989 !important;
        color: #fff !important;
      }
    }
  }
}

::v-deep .el-dialog {
  border-radius: 10px !important;
}

::v-deep {
  .el-dialog__body {
    padding: 10px 20px 20px 20px !important;
  }
}
</style>
