<template>
  <div class="m_b_20">
    <el-collapse style="border:none;background: #F6FAFB">
      <el-collapse-item style="border:none">
        <template slot="title">
          <div class="default" style="width:94%;" @click.stop="showCollpase()">
            <div class="top flex_space aligin_center">
              <div class="flex_center">
                <div style="color:#0F4444" class="fs_18 fs_bold">{{collpaseInfo.deptName}}</div>
                <div class="m_l_10 tag" :class="collpaseInfo.signTime?'is-tag':'is-no'">{{ collpaseInfo.signTime? '已签收' : '未签收' }}</div>
                <div v-if="false" class="m_l_10 is-read tag"></div>
                <div v-if="false" class="m_l_10 is-no tag">未签收</div>
                <div v-if="false" class="m_l_10 is-no tag">未阅读</div>
              </div>
              <div class="fs_14 fs_bold pointer" @click="clickRemind">提醒</div>
            </div>
            <div class="top-content  m_t_10">
<!--              <div>成都市武侯区第十四幼儿园（成都市地三十三幼儿园太平园校区）</div>-->
              <div>阅读时间：{{ collpaseInfo.readTime }}</div>
              <div>签收时间：{{ collpaseInfo.signTime }}</div>
            </div>
          </div>
        </template>
        <div class="contents">
          <div v-for="(item,index) in userList" :key="index" class="collpaseItem flex_space m_b_10">
            <div>{{ item.username }}</div>
            <div class="flex_center">
              <div :class="item.signFlag === 0?'no-color':'is-color'">{{ item.signFlag === 0 ? '未签收' : '已签收' }}</div>&nbsp;&nbsp;
              <div class="no-color" v-if="false">未阅读</div>
              <div class="is-color" v-if="false">已签收</div>
              <div class="no-color" v-if="false">未签收</div>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script>
import {getReceiveUserList} from "@/api/OA";

export default {
  props:{
    collpaseInfo:{
      type:Object,
      default:function(){
        return{}
      }
    },
    type:{
      type:Number,
      default:0
    }
  },
  data(){
    return{
      userList:[],
      parmas: {
        pageNum: 1,
        pageSize: 1000,
      },
    }
  },
  mounted(){
    this.getReceiveUserLists()
  },
  methods:{
    //获取人员
    async getReceiveUserLists() {
      const { data } = await getReceiveUserList({
        id: this.collpaseInfo.deptId,
        ...this.params,
      });
      this.userList = data.rows;
      this.total = data.total;
    },
    showCollpase(){},
    clickRemind(){},
  }

}
</script>
<style lang="scss">
.top{
  .tag{
    border-radius: 4px;
    line-height:10px;
    font-size: 12px;
    text-align: center;
    padding:5px;
    width: 40px;
    height: 10px;
  }
  .is-tag{
    color:#FFFFFF;
    background: #6a94ff;
  }
  .is-read{
    color:#3B8989;
    background: rgb(209,227,228);
  }
  .is-no{
    color:#949494;
    background: #E2E2E2;
  }
}
.top-content{
  color:rgb(151,154,154);
}
.contents{
  .collpaseItem{
    background: #fff;
    padding:10px 20px;
    border-radius: 6px;
    .is-color{
      color:#6A94FF;
    }
    .no-color{
      color:#999999
    }
  }
}
.el-collapse-item__header{
  border-top-left-radius: 10px;
  border-bottom: 0;
  height:auto;
  line-height: 20px;
  align-items: initial;
}
.el-collapse-item__wrap{
  border-bottom: 0;

}
.el-collapse-item__header,.el-collapse-item__wrap{
  padding:10px;
  background-color: #F6FAFB
}
.el-collapse-item__arrow {
  height:20px;
  line-height: 20px;
  font-size: 14px;
  &:before {
    content:'展开'+' \e790';
  }
}
.el-collapse-item__arrow.is-active{
  transform: rotate(0);
}
.el-collapse-item__arrow.is-active{
  &:before {
    content:'收起'+' \e78f';
    transform: rotate(0);
  }
}
</style>
