<template>
  <div>
    <div v-if="type===1" class="flex_space">
      <div class="left-box">
        <div class="pointer" style="color:#3B8989" @click="$emit('save',0)">{{router[0].name}}</div>
        <div class="left-content m_t_10">
          <el-tree highlight-current
                   show-checkbox
                   @check-change="select"
                   class="left-content" :data="list" :expand-on-click-node="false">
            <div slot-scope="{ node, data }" style="color:red;width:100%">
              <div class="flex_center pointer list1">
                <img v-if="false" style="width:16px;height:16px;"
                     :src="data.checked ? icon : selectIcon">&nbsp;
                <div>{{ node.label }}</div>
              </div>
            </div>
          </el-tree>
        </div>
      </div>
      <div class="right-box">
        <div class="right-content">
          <div>
            <div style="text-align: left">已选择:</div>
            <div v-if="false" class=" m_t_10 flex_center pointer list">
              <img style="width:16px;height:16px;"
                   :src="icon">
              <div>{{ agencyCurrent.department.deptName  }}</div>
            </div>
          </div>
        </div>
        <div class="btn-box" style="text-align: center">
          <el-button
              class="cancel"
              @click="cancelClick">取消
          </el-button>&nbsp;&nbsp;
          <el-button
              class="submit"
              @click="submitClick">确定
          </el-button>
        </div>
      </div>
    </div>
    <div v-if="type===2||type===3" class="flex_space">
      <div style="width:100%;height:400px;padding:0 10px;">
        <div class="pointer" style="color:#3B8989" @click="$emit('save',0)">{{router[0].name}}</div>
        <div class="left-content">
          <div class=" m_t_10 flex_center pointer list" v-for="item in agencyLists"
               :key="item.agencyId"
               @click="handleClick(item)">
            <img style="width:16px;height:16px;"
                 :src="agencyCurrent.agencyId === item.agencyId ? icon : selectIcon">&nbsp;
            <div>{{ item.agencyName }}</div>
          </div>
        </div>
      </div>
      <div v-if="false" class="right-box">
        <div class="right-content">
          <div>
            <div style="text-align: left">已选择:</div>
            <div v-if="false" class=" m_t_10 flex_center pointer list">
              <img style="width:16px;height:16px;"
                   :src="icon">
              <div>{{ agencyCurrent.agencyName  }}</div>
            </div>
          </div>
        </div>
        <div class="btn-box" style="text-align: center">
          <el-button
              class="cancel"
              @click="cancelClick">取消
          </el-button>&nbsp;&nbsp;
          <el-button
              class="submit"
              @click="submitClick">确定
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {agencyList, getDeptTree, groupList} from "@/api/OA";
import {mapGetters} from 'vuex'
export default {
  props:{
    type:Number,
    default:function(){
      return 0
    }
  },
  data(){
    return{
      agencyLists:'',
      agencyCurrent:'',
      list:[],
      current:[],
      selectIcon:
          'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCI+PHBhdGggZD0iTTUxMiA4NTQuMDE2cTEzOS45OSAwIDI0MC45ODEtMTAwLjk5MnQxMDAuOTkyLTI0MC45ODFUNzUyLjk4MSAyNzEuMDYgNTEyIDE3MC4wNyAyNzEuMDE5IDI3MS4wNjEgMTcwLjAyNyA1MTIuMDQzdDEwMC45OTIgMjQwLjk4MVQ1MTIgODU0LjAxNnptMC03NjhxMTc2IDAgMzAxLjAxMyAxMjUuMDEzdDEyNS4wMTQgMzAxLjAxNC0xMjUuMDE0IDMwMS4wMTNUNTEyIDkzOC4wNjkgMjEwLjk4NyA4MTMuMDU2IDg1Ljk3MyA1MTIuMDQzdDEyNS4wMTQtMzAxLjAxNFQ1MTIgODYuMDE2eiIgZmlsbD0iIzhhOGE4YSIvPjwvc3ZnPg==',
      icon: require('@/assets/images/dian.png'),
    }
  },
  computed:{
    ...mapGetters(['router','role','routers'])
  },
  mounted(){
    this.loadData()
  },
  methods:{
    select(e) {
      let has = false;
      this.current.map((item, index) => {
        console.log(item,e)
        if (item.id ===e.id) {
          console.log(2)
          // if (!e.curItem.checked) {
          this.current.splice(index, 1);
          // }
          has = true;
        }
      });
      if (!has) {
        this.current.push(e);
      }
      console.log(this.current)
    },
    traversalTree(arrs, that) {
      arrs.map((item, index) => {
        that.push({
          id: item.department.deptId,
          label: item.department.deptName,
          checked:false,
          children: [],
        });
        if (item.children.length !== 0) {
          this.traversalTree(item.children, that[index].children);
        }
      });
    },
    async loadData(){
      //获取机构内用户
      if(this.type===1){
        const { data } = await getDeptTree({agencyId:this.role.agency_id});
        let obj = [];
        this.traversalTree(data, obj);
        this.list = obj;

        // this.agencyLists = data;
      }
      //机构列表
      if(this.type===2){
        const { data } = await agencyList();
        this.agencyLists = data;
      }
      //教研组
      if(this.type===3){
        const { data } = await groupList();
        this.agencyLists = data.map(item=>{
          return {agencyId:item.groupId,agencyName:item.groupName}
        });
      }
    },
    cancelClick(){
      this.agencyCurrent=''
      this.$emit('cancel')
    },
    submitClick(){
      if(this.type===1){
        if(this.current.length!==0){
          this.router[1] = {
            leave: 2,
            name: '部门',
            //to: this.$route.path,
            id: this.current,
          };
          this.$store.dispatch('SetRouter', this.router);
          this.$store.dispatch('setAlways', true);
          this.$emit('save', 3);
        }else{
          this.$message({
            message:'请选择单位',
            type:'warning'
          })
        }
      }
      if(this.type===2||this.type===3) {
        if(this.agencyCurrent){
          let params = [
            {
              leave: 1,
              name: '接收机构',
              fullName: this.agencyCurrent.agencyName ,
              id: this.agencyCurrent.agencyId ,
              type:this.type
            },
          ];
          this.$store.dispatch('SetRouter', params);

          this.$emit('save', 2);
        }else{
          this.$message({
            message:'请选择单位1',
            type:'warning'
          })
        }
      }
    },
    handleClick(item){
      this.agencyCurrent=item
      this.submitClick()
    }
  }
}
</script>
<style lang="scss" scoped>
.left-box {
  width: 48%;
  height: 400px;
  padding: 0 10px;
  border-right: 1px solid #EAEAEA;

}

.left-content {
  overflow-y: auto;
  height: 390px;
}

.active {
  background: #a2c8c8 !important;
}
.list1 {
  height: 40px;
  color: #000000;
  padding: 0 5px;

  image {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}
.list {
  height: 40px;
  background: #f6fafb;
  border-radius: 7px;
  margin-bottom: 12px;
  align-items: center;
  display: flex;
  padding: 0 12px;
  color: #000000;

  image {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}
.left-box::-webkit-scrollbar {
  display: none;
}

.right-box {
  width: 50%;
  overflow-y: auto;
  padding: 0 10px;
  height: 400px;

  .right-content {
    height: 85%;
  }

  .btn-box {
    .cancel {
      background: #fff !important;
      border-color: #3888F7 !important;
      color: #3888F7 !important;
      font-family: PingFangSC;
    }

    .submit {
      background: #3888F7 !important;
      border-color: #3888F7 !important;
      color: #fff !important;
      font-family: PingFangSC;
    }
  }
}
::v-deep .el-tree-node__content {
  height: 50px;
  margin-bottom: 10px;
  padding: 0 10px !important;
  background: #f6fafb;
  border-radius: 7px;
  font-size: 15px;
  color: #0f4444;
}

::v-deep.el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__content {
  margin-left: 15px !important;
  color: red
  //.el-tree-node__content {
  //  margin-left: 15px !important;
  //}
}

.el-tree-node {
  margin-left: 15px !important;
}

// 图标样式
::v-deep .el-tree-node__expand-icon {
  color: #0f4444;
  font-size: 20px;
  position: absolute;
  padding: 0 3px 2px 0;
  right: 2%;
}

::v-deep.el-tree-node__expand-icon.expanded {
  transform: rotate(90deg)
}

::v-deep .tree_input .el-input .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;

}

// 无子节点图标隐藏
::v-deep .el-tree-node__expand-icon.is-leaf {
  color: transparent
}
</style>
