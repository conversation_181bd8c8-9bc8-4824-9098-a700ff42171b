<template>
  <div class="flex_space">
    <div class="left-box">
      <div class="left-content">
        <div class=" flex_center pointer"
             :class="current.agencyId === item.agencyId ? 'list active' : 'list'"
             v-for="(item,index) in agency"
             @click="handleClick(item)"
             :key="index">
          <img src="@/assets/images/2470.png">&nbsp;
          <div>{{ item.agencyName }}</div>
        </div>
      </div>
    </div>
    <div class="right-box" v-if="false">
      <div class="right-content">
        <div>
          <div style="text-align: left">已选择:</div>
          <div v-if="false" class="flex_center pointer m_t_10 list">
            <img src="@/assets/images/2470.png">&nbsp;
            <div>{{ current.agencyName }}</div>
          </div>
        </div>
      </div>
      <div class="btn-box" style="text-align: center">
        <el-button
            class="cancel"
            @click="cancelClick">取消
        </el-button>&nbsp;&nbsp;
        <el-button
            class="submit"
            @click="submitClick">确定
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      current: '',
      agency: [
        {
          agencyName: '机构内用户',
          agencyId: 1,
          type: 1
        },
        {
          agencyName: '跨机构用户',
          agencyId: 2,
          type: 2
        },
        {
          agencyName: '常用分组',
          agencyId: 3,
          type: 3
        },
      ],
    }
  },
  methods: {
    cancelClick() {
      this.current=''
      this.$emit('cancel')
    },
    submitClick() {
      if(this.current){
        let params = [
          {
            leave: 0,
            name: '接收单位',
            fullName: this.current.agencyName,
            type: this.current.agencyId,
          },
        ];
        this.$store.dispatch('SetRouter', params);
        if (this.current.agencyId === 1) {
          this.$emit('save', 1.1);
        }
        if (this.current.agencyId === 2) {
          this.$emit('save', 1.2);
        }
        if (this.current.agencyId === 3) {
          this.$emit('save', 1.3);
        }
      }else{
        this.$message({
          message:'请选择接受单位',
          type:'warning'
        })
      }
    },
    handleClick(item){
      this.current=item
      this.submitClick()
    }
  }
}
</script>
<style lang="scss" scoped>
.list {
  height: 40px;
  background: #f6fafb;
  border-radius: 7px;
  margin-bottom: 12px;
  align-items: center;
  display: flex;
  padding: 0 12px;
  color: #000000;

  image {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}
.left-box {
  width: 100%;
  height: 400px;
  padding: 0 10px;
  //border-right: 1px solid #EAEAEA;

  .left-content {
    overflow-y: auto;
    height: 390px;
  }

  .active {
    background: #a2c8c8 !important;
  }
}

.left-box::-webkit-scrollbar {
  display: none;
}

.right-box {
  width: 50%;
  overflow-y: auto;
  padding: 0 10px;
  height: 400px;

  .right-content {
    height: 85%;
  }

  .btn-box {
    .cancel {
      background: #fff !important;
    }

    .submit {
      background: #3B8989 !important;
      color: #fff !important;
    }
  }
}
</style>
