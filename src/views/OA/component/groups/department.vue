<template>
  <div class="flex_space">
    <div class="left-box">
      <div class="pointer" style="color:#3B8989" @click="handleEmit">{{router[0].name}}</div>
      <el-tree highlight-current
               show-checkbox
               @current-change="currentChange"
               @check-change="select"
               :default-checked-keys.sync="defaultKeys"
               class="left-content m_t_10" :data="list" :expand-on-click-node="false">
        <div slot-scope="{ node, data }" style="color:red;width:100%">
<!--            @click.stop="handleClick(node,data)"-->
          <div class="flex_center pointer list">
            <img v-if="false" style="width:16px;height:16px;"
                 :src="data.checked ? icon : selectIcon">&nbsp;
            <div>{{ node.label }}</div>
          </div>
        </div>
      </el-tree>
    </div>
    <div class="right-box">
      <div class="right-content">
        <div>
          <div style="text-align: left">已选择:</div>
          <div v-if="current">
            <div v-for="item in current"  class=" m_t_10 aligin_center flex_space  list"
                 style="background: #f6fafb;border-radius: 5px">
              <div class="flex_center">
                <img style="width:16px;height:16px;"
                     :src="icon">&nbsp;
                <div>{{ item.label }}</div>
              </div>
              <div v-if="false" style="color:red" class="pointer" @click="deleteItem(item)">删除</div>
            </div>

          </div>
        </div>
      </div>
      <div class="btn-box" style="text-align: center">
        <el-button
            class="cancel"
            @click="cancelClick">取消
        </el-button>&nbsp;&nbsp;
        <el-button
            class="submit"
            @click="submitClick">确定
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
import {mapGetters} from 'vuex'
import {getDeptTree} from "@/api/OA";

export default {
  data() {
    return {
      list: [],
      agencyCurrent: '',
      selectIcon:
          'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCI+PHBhdGggZD0iTTUxMiA4NTQuMDE2cTEzOS45OSAwIDI0MC45ODEtMTAwLjk5MnQxMDAuOTkyLTI0MC45ODFUNzUyLjk4MSAyNzEuMDYgNTEyIDE3MC4wNyAyNzEuMDE5IDI3MS4wNjEgMTcwLjAyNyA1MTIuMDQzdDEwMC45OTIgMjQwLjk4MVQ1MTIgODU0LjAxNnptMC03NjhxMTc2IDAgMzAxLjAxMyAxMjUuMDEzdDEyNS4wMTQgMzAxLjAxNC0xMjUuMDE0IDMwMS4wMTNUNTEyIDkzOC4wNjkgMjEwLjk4NyA4MTMuMDU2IDg1Ljk3MyA1MTIuMDQzdDEyNS4wMTQtMzAxLjAxNFQ1MTIgODYuMDE2eiIgZmlsbD0iIzhhOGE4YSIvPjwvc3ZnPg==',
      icon: require('@/assets/images/dian.png'),
      defaultKeys: [],
      current: []
    }
  },
  computed: {
    ...mapGetters(['router','routers'])
  },
  mounted() {
    this.loadData()
  },
  methods: {
    currentChange(node, node1) {
    },
    select(e) {
      let has = false;
      this.current.map((item, index) => {
        console.log(item,e)
        if (item.id ===e.id) {
          console.log(2)
          // if (!e.curItem.checked) {
            this.current.splice(index, 1);
          // }
          has = true;
        }
      });
      if (!has) {
        this.current.push(e);
      }
      console.log(this.current)
    },
    deleteItem(item){
      for(let i=0;i<this.current.length;i++){
        if(item.id===this.current[i].id){
          this.current.splice(i,1)
        }
      }
    },
    cancelClick() {
      this.$emit('cancel')
    },
    submitClick() {
      if (this.current.length === 0) {
        this.$message({
          message: '请选择部门！',
          type: 'warning',
        });
        return false;
      }
      this.router[1] = {
        leave: 2,
        name: '部门',
        //to: this.$route.path,
        id: this.current,
      };
      this.$store.dispatch('SetRouter', this.router);
      this.$emit('save', 3);
    },
    async loadData() {
      //获取部门列表
      const {data} = await getDeptTree({agencyId: this.router[0].id});
      let obj = [];
      this.traversalTree(data, obj);
      this.list = obj;
    },
    traversalTree(arrs, that) {
      arrs.map((item, index) => {
        that.push({
          id: item.department.deptId,
          label: item.department.deptName,
          checked:false,
          children: [],
        });
        if (item.children.length !== 0) {
          this.traversalTree(item.children, that[index].children);
        }
      });
    },
    handleClick(node, data) {
      let has = false;
      this.current.map((item, index) => {
        if (item.id ===data.id) {
          if(data.checked){
            this.current.splice(index, 1);
            for(let i=0;i<this.list.length;i++){
              if(this.list[i].id===item.id){
                this.$nextTick(()=>{
                  this.list[i].checked=false
                })
              }
            }
          }
          has = true;
        }
      });
      if (!has&&!data.checked) {
        this.current.push(data);
      }
      for(let i=0;i<this.list.length;i++){
        if(this.list[i].id===data.id){
          this.list[i].checked=true
        }
      }
    },
    handleEmit(){
      console.log(this.router[0])
      if(this.router[0].type===2){
        this.$emit('save',1.2)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.left-box {
  width: 48%;
  height: 400px;
  padding: 0 10px;
  border-right: 1px solid #EAEAEA;

  .left-content {
    overflow-y: auto;
    height: 390px;
  }

  .active {
    background: #a2c8c8 !important;
  }
}

.list {
  height: 40px;
  color: #000000;
  padding: 0 5px;

  image {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}

.left-box::-webkit-scrollbar {
  display: none;
}

::v-deep .el-tree-node__content {
  height: 50px;
  margin-bottom: 10px;
  padding: 0 10px !important;
  background: #f6fafb;
  border-radius: 7px;
  font-size: 15px;
  color: #0f4444;
}

::v-deep.el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__content {
  margin-left: 15px !important;
  color: red
  //.el-tree-node__content {
  //  margin-left: 15px !important;
  //}
}

.el-tree-node {
  margin-left: 15px !important;
}

// 图标样式
::v-deep .el-tree-node__expand-icon {
  color: #0f4444;
  font-size: 20px;
  position: absolute;
  padding: 0 3px 2px 0;
  right: 2%;
}

::v-deep.el-tree-node__expand-icon.expanded {
  transform: rotate(90deg)
}

::v-deep .tree_input .el-input .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;

}

// 无子节点图标隐藏
::v-deep .el-tree-node__expand-icon.is-leaf {
  color: transparent
}

.right-box {
  width: 50%;
  overflow-y: auto;
  padding: 0 10px;
  height: 400px;

  .right-content {
    height: 85%;
  }

  .btn-box {
    .cancel {
      background: #fff !important;
      border-color: #3888F7 !important;
      color: #3888F7 !important;
      font-family: PingFangSC;
    }

    .submit {
      border-color: #3888F7 !important;
      background: #3888F7 !important;
      color: #fff !important;
      font-family: PingFangSC;
    }
  }
}
</style>
