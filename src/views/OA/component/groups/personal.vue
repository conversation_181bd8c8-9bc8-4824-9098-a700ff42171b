<template>
  <div class="flex_space">
    <div class="left-box">
      <div v-if="!always">
        <div class="flex_center" >
          <div v-for="(item,index) in router" :key="index">
            <div class="pointer" style="color:#3B8989;margin-right:10px;"
                 @click="tabs(index+1)">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="pointer" style="color:#3B8989" @click="$emit('save',1.1)">{{router[0].name}}</div>
      </div>


      <div class="list m_b_20 m_t_10 pointer" @click="selectAll">
        <img style="width:16px;height:16px;"
             :src="checked ? icon : selectIcon">&nbsp;
        <div>全选</div>
      </div>
      <div class=" m_t_10 flex_center pointer list" v-for="item in list"
           :key="item.agencyId"
           @click.stop="handleClick(item)">
        <img style="width:16px;height:16px;"
             :src="item.checked ? icon : selectIcon">&nbsp;
        <div>{{ item.userName }}</div>
      </div>
    </div>
    <div class="right-box">
      <div class="right-content">
        <div>
          <div style="text-align: left">已选择用户:</div>
          <div v-if="current">
            <div v-for="item in current" :key="item.agencyId" class=" m_t_10 aligin_center flex_space  list"
                 style="background: #f6fafb;border-radius: 5px">
              <div class="flex_center">
                <img style="width:16px;height:16px;"
                     :src="icon">&nbsp;
                <div>{{ item.userName }}</div>
              </div>
              <div v-if="false" style="color:red" class="pointer" @click="deleteItem(item)">删除</div>
            </div>

          </div>
        </div>
      </div>
      <div class="btn-box" style="text-align: center">
        <el-button
            class="cancel"
            @click="cancelClick">取消
        </el-button>&nbsp;&nbsp;
        <el-button
            class="submit"
            @click="submitClick">确定
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
import {deptUserList} from "@/api/OA";
import {mapGetters} from 'vuex'

export default {
  data() {
    return {
      list: [],
      agencyCurrent: '',
      selectIcon:
          'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCI+PHBhdGggZD0iTTUxMiA4NTQuMDE2cTEzOS45OSAwIDI0MC45ODEtMTAwLjk5MnQxMDAuOTkyLTI0MC45ODFUNzUyLjk4MSAyNzEuMDYgNTEyIDE3MC4wNyAyNzEuMDE5IDI3MS4wNjEgMTcwLjAyNyA1MTIuMDQzdDEwMC45OTIgMjQwLjk4MVQ1MTIgODU0LjAxNnptMC03NjhxMTc2IDAgMzAxLjAxMyAxMjUuMDEzdDEyNS4wMTQgMzAxLjAxNC0xMjUuMDE0IDMwMS4wMTNUNTEyIDkzOC4wNjkgMjEwLjk4NyA4MTMuMDU2IDg1Ljk3MyA1MTIuMDQzdDEyNS4wMTQtMzAxLjAxNFQ1MTIgODYuMDE2eiIgZmlsbD0iIzhhOGE4YSIvPjwvc3ZnPg==',
      icon: require('@/assets/images/dian.png'),
      current: [],
      checked: false,
    }
  },
  mounted() {
    this.loadData()
  },
  computed: {
    ...mapGetters(['router','always']),
  },
  methods: {
    async loadData() {
      this.routes = this.router;
      if (this.router[0].type === 2) {
        const {data} = await deptUserList(
            this.router[1].id.map((item) => {
              return item.id;
            })
        );
        this.list = data;
      } else {
        const {data} = await deptUserList(
            this.router[1].id.map((item) => {
              return item.id;
            })
        );
        this.list = data;
      }
      for (let i = 0; i < this.list.length; i++) {
        this.list[i].checked = false
      }
    },
    cancelClick() {
      this.$emit('cancel')
    },
    submitClick() {
      this.$emit('deptInfo', this.current);
    },
    handleClick(item) {
      let has = false;
      this.current.map((item, index) => {
        if (item.id === item.id) {
          if (item.checked) {
            this.current.splice(index, 1);
            for (let i = 0; i < this.list.length; i++) {
              if (this.list[i].id === item.id) {
                this.$nextTick(() => {
                  this.list[i].checked = false
                })
              }
            }
          }
          has = true;
        }
      });
      if (!has && !item.checked) {
        this.current.push(item);
      }
      for (let i = 0; i < this.list.length; i++) {
        if (this.list[i].id === item.id) {
          this.list[i].checked = true
        }
      }
      this.$forceUpdate()
      console.log(this.current)
    },
    tabs(e) {
      if (e === 1) {
        if (this.router[0].type === 1) {
          this.$emit('save', 1.1);
        }
        if (this.router[0].type === 2) {
          this.$emit('save', 1.2);
        }
        if (this.router[0].type === 3) {
          this.$emit('save', 1.3);
        }
      } else {
        this.$emit('save', e);
      }
    },
    selectAll() {
      if (this.checked) {
        for (let i = 0; i < this.list.length; i++) {
          this.list[i].checked = false
        }
        this.$forceUpdate()
        this.checked = false
        this.current = []
      } else {
        for (let i = 0; i < this.list.length; i++) {
          this.list[i].checked = true
        }
        this.$forceUpdate()
        this.checked = true
        this.current = this.list
      }
      console.log(this.list)
    }
  }
}
</script>
<style lang="scss" scoped>
.left-box {
  width: 48%;
  height: 400px;
  padding: 0 10px;
  border-right: 1px solid #EAEAEA;

  .left-content {
    overflow-y: auto;
    height: 390px;
  }

  .active {
    background: #a2c8c8 !important;
  }
}

.list {
  height: 40px;
  background: #f6fafb;
  border-radius: 7px;
  margin-bottom: 12px;
  align-items: center;
  display: flex;
  padding: 0 12px;
  color: #000000;

  image {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}

.left-box::-webkit-scrollbar {
  display: none;
}

.right-box {
  width: 50%;
  overflow-y: auto;
  padding: 0 10px;
  height: 400px;

  .right-content {
    height: 85%;
  }

  .btn-box {
    .cancel {
      background: #fff !important;
      border-color: #3888F7 !important;
      color: #3888F7 !important;
      font-family: PingFangSC;
    }

    .submit {
      background: #3888F7 !important;
      border-color: #3888F7 !important;
      color: #fff !important;
      font-family: PingFangSC;
    }
  }
}
</style>
