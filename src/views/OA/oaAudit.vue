<template>
  <div>
    <div class="fs_16 m_b_10">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/oa' }"><span style="color:black;">OA首发</span></el-breadcrumb-item>
        <el-breadcrumb-item>OA详情</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="top-container">
      <div class="title">{{ infoDetail.noticeTitle }}</div>
      <div class="tip-box m_t_20">
        <div class="flex_center">
          <div class="flex_center">
            <div class="c_tip w_80">发文单位：</div>
            <div>{{ infoDetail.agencyName }}</div>
          </div>
          <div class="flex_center m_l_40">
            <div class="c_tip w_80">发文时间：</div>
            <div>{{ dayjs()(infoDetail.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
        </div>
        <div class="flex_center m_t_10">
          <div class="flex_center">
            <div class="c_tip w_80">发起人：</div>
            <div>{{ infoDetail.createBy }}</div>
          </div>
          <div class="flex_center m_l_40">
            <div class="c_tip w_80">公文类型：</div>
            <div>
              <dict-tag
                  :options="dict.type.notice_type"
                  :value="infoDetail.noticeType"/>
            </div>
          </div>
        </div>
        <div class="flex_center m_t_10">
          <div class="flex_center ">
            <div class="c_tip w_80">文号：</div>
            <div>
              {{ infoDetail.numberAgency }} 【{{ infoDetail.numberYear }}】{{ infoDetail.numberRef }}号
            </div>
          </div>
        </div>
      </div>
      <!--      <div class="tag">进行中</div>-->
      <div class="m_t_20">
        <div class="c_tip">附件：</div>
        <div v-for="item in infoDetail.fileList" :key="item.fileId" class="file-item">
          <div>
            {{ item.fileName }}
          </div>
          <div class="flex_center">
            <div class="pointer" @click="viewFile(item.fileUrl)">预览</div>&nbsp;&nbsp;
            <div class="pointer" @click="downloadFile(item.fileUrl)">下载</div>
          </div>
        </div>
        <div v-if="type==='0'" class="m_t_20" style="text-align: center">
          <el-button class="get" @click="handleGet">签收</el-button>&nbsp;&nbsp;
          <el-button class="pass" @click="handleRelay">转发</el-button>
        </div>
        <div v-if="type==='2'" class="m_t_20" style="text-align: center">
          <el-button class="reject" @click="handleReject">驳回</el-button>&nbsp;&nbsp;
          <el-button class="pass" @click="handlePass">通过</el-button>
        </div>
        <div v-if="type==='1'" class="m_t_20" style="text-align: center">
          <el-button class="pass" v-if="infoDetail.status==='1'" @click="handleCancel">撤销</el-button>
          <el-button  class="disabled" v-if="infoDetail.status==='3'" @click="handleCancel">已撤销</el-button>
        </div>
      </div>
    </div>
    <div class="center-container m_t_20">
      <Title :title="'接收单位'"></Title>
      <div class="flex m_t_10 aligin_center m_b_20">
        <div class="m_r_20 pointer" style="height:25px;"
             :class="currentInx===index?'is-click':'un-click'"
             v-for="(item,index) in tagArr"
             :key="index"
             @click="changItem(index)"
        >{{ item.title }}
        </div>
      </div>

      <div v-if="currentInx===0">
        <div v-for="item in receiveUnitList" :key="item.deptId">
          <collapse :collpaseInfo="item"></collapse>
        </div>
      </div>
      <div v-if="currentInx===1">
        <div v-for="item in receiveUnitList" :key="item.deptId">
          <collapse :collpaseInfo="item"></collapse>
        </div>
      </div>
      <div v-if="currentInx===2">
        <div v-for="item in receiveUnitList" :key="item.deptId">
          <collapse :collpaseInfo="item"></collapse>
        </div>
      </div>
    </div>
    <sign-dialog ref="signDialogRef" :info="signInfo" @success="success"></sign-dialog>
    <audit-dialog ref="auditDialogRef"
                  @success="success"
                  :approve-type="approveType"
                  :title="title" :info="dialogInfo"></audit-dialog>
    <select-dialog ref="selectRef"
                   :title="title1"
                   :dialog-width="840"
                   @confirm="saveConfirm"></select-dialog>
  </div>
</template>
<script>
import signDialog from "@/views/OA/component/signDialog.vue";
import Title from '@/components/Title.vue'
import Collapse from "@/views/OA/component/collapse.vue";
import {approve, forward, getContent, getReceiveUnitList, putContent} from "@/api/OA";
import dayjs from "dayjs";
import AuditDialog from "@/views/OA/component/auditDialog.vue";
import SelectDialog from "@/views/OA/component/selectDialog.vue";

export default {
  dicts: ['notice_type'],
  components: {
    SelectDialog,
    AuditDialog,
    Collapse,
    Title,
    signDialog
  },
  data() {
    return {
      tagArr: [
        {
          title: '全部'
        },
        {
          title: '已签收'
        },
        {
          title: '未签收'
        }
      ],
      currentInx: 0,
      type: '',
      infoDetail: {},
      info: {},
      receiveUnitList: [],//接收单位
      title: '',
      dialogInfo: {},
      approveType: 0,
      signInfo:{},
      title1:'',
      receivePeoples:[],//转发人员
      userInfo:''
    }
  },
  async mounted() {
    this.type = this.$route.query.type
    this.loadData()
    const {data} = await getContent(this.$route.query.id);
    this.info = data;
    await this.getReceiveUnitLists()
  },
  methods: {
    approve,
    dayjs() {
      return dayjs
    },
    loadData() {
      getContent(this.$route.query.id).then(res => {
        this.infoDetail = res.data
      })
    },
    // 接收单位
    async getReceiveUnitLists() {
      const {data} = await getReceiveUnitList({
        contentId: this.info.contentId,
      });
      this.receiveUnitList = data;
    },
    // 预览
    viewFile(url) {
    },
    // 下载
    downloadFile(url) {
    },
    changItem(inx) {
      this.currentInx = inx
    },
    // 撤销
    handleCancel() {
      let that=this
      this.$confirm('确定撤销公文吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let infos = that.info;
        infos.status = 3;
        putContent(infos).then((res) => {
          if (res.code === 200) {
            this.$message({
              message: '撤销成功',
              type: 'warning',
            });
          }
          this.$router.push({
            path:'/oa'
          })
        });
      }).catch(() => {
      })
    },
    // 通过
    handlePass() {
      this.dialogInfo = this.info
      this.title = '通过'
      this.approveType = 0
      this.$refs.auditDialogRef.showDialog = true
    },
    // 驳回
    handleReject() {
      this.dialogInfo = this.info
      this.title = '驳回'
      this.approveType = 1
      this.$refs.auditDialogRef.showDialog = true
    },
    success() {
      this.$router.push({
        path: '/oa'
      })
    },
    // 转发
    handleRelay() {
      this.title1 = '接收单位'
      this.$refs.selectRef.showDialog = true
    },
    saveConfirm(data){
      let userList = _.clone(data).slice(0, 6);
      if (data.length > 6) {
        this.userInfo =
            userList
                .map((item) => {
                  return item.userName;
                })
                .toString() +
            '等' +
            data.length +
            '人';
      } else {
        this.userInfo = userList
            .map((item) => {
              return item.userName;
            })
            .toString();
      }
      this.receivePeoples = data.map(item => {
        return {
          signBy: item.userId,
          deptId: item.deptId,
          agencyId: item.agencyId,
        };
      })
      this.handleZf()
    },
    handleZf(){
      let that=this
      this.$confirm('是否转发给'+this.userInfo+'?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(()=>{
        let params={
          contentId:that.info.contentId,
          receivePeoples:that.receivePeoples
        }
        forward(params).then(res=>{
          if(res.code===200){
            this.$message({
              message: '转发成功',
              type: 'success',
            });
          }
        })
      }).catch(()=>{

      })
    },
    // 签收
    handleGet() {
      this.signInfo=this.info
      this.$refs.signDialogRef.showDialog = true
    }
  }
}
</script>
<style lang="scss" scoped>
.top-container {
  padding: 20px;
  background: #fff;
  font-size: 16px;
  border-radius: 5px;
  position: relative;

  .title {
    color: #0F4444;
    font-size: 18px;
    font-weight: bold;
  }

  .tip-box {
    border-radius: 6px;
    background: #F6FAFB;
    padding: 20px;
    z-index: 100;
  }

  .tag {
    position: absolute;
    font-size: 14px;
    color: #fff;
    padding: 5px 15px;
    right: 0;
    top: 0;
    background: #6A94FF;
    border-radius: 0px 4px 4px 4px;
  }

  .file-item {
    margin-top: 10px;
    border-radius: 6px;
    background: #F6FAFB;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .reject {
    background: #fff !important;
    border-color: #FD8080 !important;
    color: #FD8080 !important;
    font-family: PingFangSC;
  }

  .get {
    background: #fff !important;
    border-color: #3888F7 !important;
    color: #3888F7 !important;
    font-family: PingFangSC;
  }

  .pass {
    border-color: #3888F7 !important;
    background: #3888F7 !important;
    color: #fff !important;
    font-family: PingFangSC;
  }
}

.center-container {
  font-size: 16px;
  background: #fff;
  border-radius: 6px;
  padding: 20px;

  .un-click {
    color: #6F8F8F
  }

  .is-click {
    position: relative;
    font-weight: bold;
    color: #0F4444;
  }

  .is-click:before {
    content: "";
    position: absolute;
    left: 0;
    bottom: -5px;
    width: 50%;
    border-bottom: 3px solid #0F4444;
    box-sizing: border-box;
    opacity: .9;
  }
}
.disabled{
  border-color: rgb(169,169,169) !important;
  background: rgb(169,169,169) !important;
  color: #fff !important;
  font-family: PingFangSC;
  cursor: default;
}

</style>
