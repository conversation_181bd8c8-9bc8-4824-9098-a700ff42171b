<template>
  <div class="oa-container">
    <div class="header-top flex just_between">
      <div class="flex aligin_center">
        <div class="m_r_20 pointer" style="height:25px;"
             :class="currentInx===index?'is-click':'un-click'"
             v-for="(item,index) in itemArr"
             :key="index"
             @click="changItem(index)"
        >{{ item.title }}
        </div>
      </div>
      <div>
        <el-button class="edit_btn family_Pf" @click="addDocument">新建公文</el-button>
      </div>
    </div>
    <div v-if="currentInx===0" class="content">
      <div v-loading="vLoading">
        <div class="itembox">
          <div v-for="item in oaList" :key="item.contentId">
            <oa-item :info="item" :btn-type="currentInx"></oa-item>
          </div>
        </div>
        <div class="paging" v-if="oaList.length!==0">
          <el-pagination
              :page-size="params.pageSize"
              background
              :total="total"
              :current-page="params.pageNum"
              @current-change="handleCurrentChange"></el-pagination>
        </div>
      </div>
      <el-empty v-if="oaList.length===0"></el-empty>
    </div>
    <div v-if="currentInx===1" class="content">
      <div v-loading="vLoading">
        <div class="itembox">
          <div v-for="item in oaList" :key="item.contentId">
            <oa-item :info="item" :btn-type="currentInx"></oa-item>
          </div>
        </div>
        <div class="paging" v-if="oaList.length!==0">
          <el-pagination
              :page-size="params.pageSize"
              background
              :total="total"
              :current-page="params.pageNum"
              @current-change="handleCurrentChange"></el-pagination>
        </div>
      </div>
      <el-empty v-if="oaList.length===0"></el-empty>
    </div>
    <div v-if="currentInx===2" class="content">
      <div v-loading="vLoading">
        <div class="itembox">
          <div v-for="item in oaList" :key="item.contentId">
            <oa-item :info="item" :btn-type="currentInx"></oa-item>
          </div>
        </div>
        <div class="paging" v-if="oaList.length!==0">
          <el-pagination
              :page-size="params.pageSize"
              background
              :total="total"
              :current-page="params.pageNum"
              @current-change="handleCurrentChange"></el-pagination>
        </div>
      </div>
      <el-empty v-if="oaList.length===0"></el-empty>

    </div>
  </div>
</template>
<script>
import {getOaList} from "@/api/OA";
import oaItem from "@/views/OA/component/oaItem.vue";
import {mapGetters} from 'vuex'

export default {
  components: {
    oaItem
  },
  data() {
    return {
      currentInx: 0,
      itemArr: [
        {
          title: '我收到的'
        },
        {
          title: '我发起的'
        },
        {
          title: '我审核的'
        }
      ],
      params: {
        pageNum: 1,
        pageSize: 10,
      },
      type: 0,
      oaList: [],
      total: 0,
      vLoading:false
    }
  },
  computed: {
    ...mapGetters(['role'])
  },
  mounted() {
    this.loadData(this.type)
  },
  methods: {
    changItem(inx) {
      this.currentInx = inx
      this.type = inx
      this.loadData(inx)
    },
    loadData(e) {
      this.vLoading=true
      getOaList({type: e === 0 ? '' : e, ...this.params, agencyId: this.role.agency_id}).then(res => {
        if (res.code === 200) {
          this.oaList = res.rows
          this.total = res.total
        }
      }).finally(()=>{
        this.vLoading=false
      })
    },
    handleCurrentChange(page){
      this.params.pageNum=page
      this.loadData(this.type)
    },
    addDocument() {
      this.$router.push({
        path: '/oa/addDocument'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.oa-container {
  .header-top {
    border-radius: 5px;
    font-size: 16px;
    padding: 15px;
    background: #fff;

    .un-click {
      color: #6F8F8F
    }

    .is-click {
      position: relative;
      font-weight: bold;
      color: #0F4444;
    }

    .is-click:before {
      content: "";
      position: absolute;
      left: 0;
      bottom: -5px;
      width: 50%;
      border-bottom: 3px solid #0F4444;
      box-sizing: border-box;
      opacity: .9;
    }
  }

  .content {
    border-radius: 5px;
    font-size: 16px;
    padding: 20px;
    background: #fff;
    margin-top: 20px;

    .itembox {
      display: flex;
      gap: 20px;
      flex-direction: row;
      flex-wrap: wrap;
    }
  }
}
.paging{
  margin-top:20px;
  text-align: center;

  ::v-deep .el-pager li:not(.disabled).active {
    background-color: #3b8989 !important;
  }
}
.page {
}
</style>
