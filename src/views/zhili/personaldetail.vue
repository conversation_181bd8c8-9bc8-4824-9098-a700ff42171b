<template>
  <div class="container">
    <div class="box">
      <Title title="基础信息"></Title>
      <el-button class="edit_btn" @click="personalEdit">编辑</el-button>
      <div class="content">
        <Forms :columns="personalInfo"></Forms>
      </div>

    </div>
    <!--    弹窗-->
    <MyDialog title="基础信息" v-if="showPersonalDailog"
              @close="showPersonalDailog=!showPersonalDailog"
              @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms :columns="personalInfo" :editable="true" ref="forms" @formsDatas="editInfo" labelWidth="10rem"></Forms>
      </div>
    </MyDialog>
  </div>
</template>

<script>
import Title from "@/components/Title.vue";
import MyDialog from "@/components/MyDialog.vue";
import Forms from "@/components/Forms.vue";
import {getStaffDetail, modifyStaff} from "@/api/zhili";
export default {
  name:'PersonalDetail',
  computed: {
  },
  components:{
    MyDialog,
    Title,
    Forms
  },
  data(){
    return{
      showPersonalDailog:false,
      personalInfo:[
        {
          prop: 'headIcon',
          label: '头像',
          placeholder: '',
          type: 'photo',
          default: '',
          required: false,
        },
        {
          prop: 'userName',
          label: '姓名',
          placeholder: '请输入姓名',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'sex',
          label: '性别',
          placeholder: '请选择性别',
          type: 'dict',
          dictType:'sys_user_sex',
          multiple: false,
          default: '',
          options: [],
          required: true,
        },
        {
          prop: 'idcardType',
          label: '证件类型',
          placeholder: '请输入类型',
          type: 'dict',
          default: '',
          dictType:'idcard_type',
          multiple: false,
          options: [],
          required: true,
        },
        {
          prop: 'idcard',
          label: '证件号',
          placeholder: '请输入证件号',
          type: 'input',
          default: '12584161894861',
          required: '',
        },
        {
          prop: 'date',
          label: '出生日期',
          placeholder: '请输入出生日期',
          type: 'input',
          default: '',
          required: '',
        },
        {
          prop: 'phone',
          label: '手机号',
          placeholder: '请输入手机号',
          type: 'input',
          default: '',
          required: '',
        },
        // {
        //   prop: 'QQnumber',
        //   label: 'QQ号',
        //   placeholder: '请输入QQ号',
        //   type: 'input',
        //   default: '1956569955',
        //   required: '',
        // },
        {
          prop: 'wechat',
          label: '微信号',
          placeholder: '请输入微信号',
          type: 'input',
          default: '',
          required: '',
        },
        // {
        //   prop: 'email',
        //   label: '邮箱号码',
        //   placeholder: '请输入邮箱号码',
        //   type: 'input',
        //   default: '',
        //   required: '',
        // },
        {
          prop: 'deptName',
          label: '所属部门',
          placeholder: '请输入所属部门',
          type: 'input',
          default: '',
          required: '',
        },
        {
          prop: 'email',
          label: '职务',
          placeholder: '请输入职务',
          type: 'input',
          default: '',
          required: '',
        },
        {
          prop: 'duties',
          label: '职称',
          placeholder: '请输入职称',
          type: 'input',
          default: '',
          required: '',
        }
      ],
      loading:false,
      commitInfo:{
        userId:'',
        staffId:this.$route.query.id
      }
    }
  },
  methods:{
    // 获取职员详情
    async getStaffDetail(){
      await getStaffDetail(this.$route.query.id).then((res)=>{
        let item=res.data;
        this.commitInfo.userId=item.userId;
        this.personalInfo.forEach(its=>{
          its.default=item[its.prop]
        })
      }).catch(()=>{})
    },
    personalEdit(){
      this.showPersonalDailog=true
    },
    // 修改职员个人信息
    editInfo(res){
      // console.log(res)
      let finalInfo=Object.assign(this.commitInfo,res)
      modifyStaff(finalInfo).then(()=>{
        this.loading=true;
        this.$message.success('修改成功!')
        this.getStaffDetail();
        this.showPersonalDailog=false;
        this.loading=false;
      })

    },
  },
  created(){
    console.log(this.$route.query.id,'职员StaffId');
    this.getStaffDetail();
  }
}
</script>

<style lang="scss" scoped>
.container{
  width:1200px;
  margin:0 auto;
  display: flex;
  flex-direction: column;
  .box{
    border-radius:0.6rem;
    margin:3rem 0;
    padding:2rem;
    background-color:#fff;
    display:flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .content{
      display: flex;
      justify-content: space-around;
      width: 100%;
      padding: 2rem;
    }
  }
}
</style>