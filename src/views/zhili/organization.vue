<template>
  <div class="container" v-loading="loading">
    <div class="content-box box" style="background-color:#fff">
      <button class="btn_anniu" @click="change(0)" :class="{newStyle:0===number}">机构设置</button>
      <button class="btn_anniu" @click="change(1)" :class="{newStyle:1===number}">组织机构设置</button>
      <button class="btn_anniu" @click="change(2)" :class="{newStyle:2===number}">老师任教学校</button>
      <button class="btn_anniu" @click="change(3)" :class="{newStyle:3===number}">学科管理</button>
    </div>

    <div class="content box">

      <!--   机构设置   -->
      <div class="" v-show="0===number">
        <div class="head">
          <el-select v-model="parameter.schoolType" placeholder="全部" style="width:110px"  clearable @clear="clearSelect">
            <el-option @click.native="search" v-for="dict in dict.type['school_type']"
                       :key="dict.value"
                       :label="dict.label"
                       :value="dict.value"></el-option>
          </el-select>
          <div>
            <el-input @keyup.enter.native="search" placeholder="输入名称进行搜索" style="width:200px;padding-right:2rem;" v-model="parameter.fullName"></el-input>
            <el-button class="edit_btn" @click="newOrgan">新增</el-button>
          </div>

        </div>
        <div class="contents flex">
          <div style="margin-bottom:10px;" v-for="(item,index) in contents.rows" :key="index">
            <SchoolList :info="item"></SchoolList>
          </div>
          <div class="paging">
            <el-pagination :page-size="parameter.pageSize"
                           background
                           :total="contents.total"
                           :current-page="parameter.pageNum"
                           @current-change="handleCurrentChange"></el-pagination>
          </div>
        </div>
      </div>

<!--      // 组织结构设置 树结构  -->
<!--      <div v-show="1===number" >-->
<!--        <div style="display: flex">-->
<!--          <div style="width:400px;">-->
<!--            <Title style="margin-top:2rem;" title="组织架构"></Title>-->
<!--            <Tree ref="customtree" :treeData='treeData'-->
<!--                  :tree-node-key="treeNodeKey"-->
<!--                  :isForm='true'-->
<!--                  @AddDepartment="showAddDepartment"-->
<!--                  @Delete="Delete"-->
<!--                  @Edit="Edit"-->
<!--                  @AddUnit="showAddUnits"-->
<!--                  @AddCampus="showAddCampus"-->
<!--                  @AddBranchCampus="showAddBranchCampus"-->
<!--                  @AddMembers="showAddMembers"-->
<!--                  @deleteOrgan="deleteOrgan"></Tree>-->
<!--          </div>-->
<!--          <div>3333</div>-->
<!--        </div>-->
<!--      </div>-->
      <!-- 老师任教学校-->
      <div v-show="2===number">
        <div class="head">
          <el-select v-model="teacherParamter" placeholder="全部" style="width:110px" clearable @clear.native="clearSelect">
            <el-option @click.native="searchTeacher" v-for="dict in info" :key="dict.value" :label="dict.label" :value="dict.value" ></el-option>
          </el-select>
          <div class="flex">
            <el-input @keyup.enter.native="searchTeacher" placeholder="输入名称进行搜索" style="width:200px;padding-right:2rem;" v-model="teacherParamter.userName">
            </el-input>
            <el-button class="edit_btn" @click="showAddTeacherDialog=true">新增</el-button>
          </div>
        </div>
        <div class="contents flex">
          <div style="margin:0 0 10px 15px" v-for="(item,index) in teacherContents.rows" :key="index">
            <TeacherList :info="item" @SchoolEdit="SchoolEdit" :schoolForms="schoolOption"></TeacherList>
          </div>
          <div class="paging">
            <el-pagination :page-size="teacherParamter.pageSize"
                           background
                           :total="teacherContents.total"
                           :current-page.sync="teacherParamter.pageNum"
                           @current-change="handleTeacherChange"></el-pagination>
          </div>
        </div>
      </div>

      <!--学科管理-->
      <div v-show="3===number">
        <div class="head">
          <Title title="学科列表"></Title>
          <div>
            <el-input @keyup.enter.native="searchCourse"
                      placeholder="输入名称进行搜索"
                      style="width:200px;padding-right:2rem;" v-model="CourseParameter.courseName">
            </el-input>
            <el-button class="edit_btn" @click="AddCourse">新增</el-button>
          </div>
        </div>
        <div v-for="(item,index) in CourseList.rows" :key="index">
          <CourseList :info="item"></CourseList>
        </div>
        <div class="course-paging">
          <el-pagination :page-size="CourseParameter.pageSize"
                         background
                         :total="CourseList.total"
                         :current-page="CourseParameter.pageNum"
                         @current-change="handleCourseChange"></el-pagination>
        </div>
      </div>
    </div>

    <div style="">
  <!--  组织结构设置 树结构  -->
    <div v-show="1===number" >
      <div style="display: flex;margin-bottom: 2rem">
        <div style="width:400px;background-color:#fff;border-radius: 8px;padding:0 20px;">
          <Title style="margin-top:2rem;" title="组织架构"></Title>
          <Tree ref="customtree" :treeData='treeData'
              :tree-node-key="treeNodeKey"
              :isForm='true'
              @AddDepartment="showAddDepartment"
              @Delete="Delete"
              @Edit="Edit"
              @AddUnit="showAddUnits"
              @AddCampus="showAddCampus"
              @AddBranchCampus="showAddBranchCampus"
              @AddMembers="showAddMembers"
              @deleteOrgan="deleteOrgan"
              @handleNodeClick="handleNodeClick"></Tree>
        </div>
        <div style="width:78rem;margin-left:2rem;background-color:#fff;border-radius: 8px;padding:0 20px;">
          <div style="margin-top:2rem; font-size: 1.8rem;opacity: 0.8;font-weight: 800;display: flex;color: #0f4444;align-items: center;">职员信息</div>
          <div style="margin-top:2rem">
            <div style="margin-bottom:2rem;" v-for="(item,index) in staffList" :key="index">
              <my-staff :info="item"></my-staff>
            </div>
<!--            <div class="paging">-->
<!--              <el-pagination :page-size="parameter.pageSize"-->
<!--                             background-->
<!--                             :total="contents.total"-->
<!--                             :current-page="parameter.pageNum"-->
<!--                             @current-change="handleCurrentChange"></el-pagination>-->
<!--            </div>-->
          </div>
        </div>
      </div>
    </div>
  </div>
    <!--------弹窗处理---------->
<!--    // 机构设置页面新增机构-->
    <MyDialog title="新增机构" v-if="showNewOrgan"
              @close="showNewOrgan=!showNewOrgan"
              @confirm="$refs.forms.getFormsDatas()">
        <Forms :columns="newOrganForm"
               :editable="true"
               ref="forms"
               @formsDatas="addNewOrgan"
               labelWidth="12rem"></Forms>
    </MyDialog>

    <MyDialog title="新增科室" v-if="showAddOragns"
              @close="showAddOragns=!showAddOragns"
              @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms :columns="baseDeptsInfo"
               :editable="true"
               ref="forms"
               @formsDatas="AddDepartment"
               labelWidth="15rem"></Forms>
      </div>
    </MyDialog>

    <MyDialog title="新增直属单位" v-if="showAddUnit"
              @close="showAddUnit=!showAddUnit"
              @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms :columns="schoolInfoForms"
               :editable="true"
               ref="forms"
               @formsDatas="AddUnit"
               labelWidth="15rem"></Forms>
      </div>
    </MyDialog>

    <MyDialog title="新增学校" v-if="showAddSchool"
              @close="showAddSchool=!showAddSchool"
              @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms :columns="schoolInfoForms"
               :editable="true"
               ref="forms"
               @formsDatas="AddCampus"
               labelWidth="15rem"></Forms>
      </div>
    </MyDialog>

    <MyDialog title="新增成员" v-if="showAddmember"
              @close="showAddmember=!showAddmember"
              @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms :editable="true" ref="forms" labelWidth="15rem"></Forms>
      </div>
    </MyDialog>

    <MyDialog title="新增分校" v-if="showAddBranchSchool"
              @close="showAddBranchSchool=!showAddBranchSchool"
              @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms :columns="schoolInfoForms"
               :editable="true"
               ref="forms"
               @formsDatas="AddBranchSchool"
               labelWidth="15rem"></Forms>
      </div>
    </MyDialog>

<!--    新增学科弹窗-->
    <MyDialog title="新增学科" v-if="showAddCourse"
              dialogWidth="380px"
              @close="showAddCourse=!showAddCourse"
              @confirm="Courseconfirm">
      <div style="display:flex;justify-content: space-between;height:40px;line-height: 40px">
        <div>学科名称:</div>
        <el-input placeholder="请输入学科名称" style="width:233px" v-model="infos.courseName"></el-input>
      </div>
      <div style="display:flex;justify-content: space-between;height:40px;line-height: 40px;margin:2rem 0;">
        <div>学段:</div>
          <el-select style="width:233px;" v-model="infos.gradeLevel">
          <el-option v-for="item in stageOptions"
                     :key="item.gradeLevel"
                     :label="item.label"
                     :value="item.gradeLevel"></el-option>
        </el-select>
      </div>
    </MyDialog>

<!--  老师任教学校 新增教师-->
    <MyDialog title="新增教师" v-if="showAddTeacherDialog"
              @close="showAddTeacherDialog=false"
              dialogWidth="550px"
              @confirm="$refs.teacher.getFormsDatas()">
      <div class="schoolDialog">
        <MyForms :columns="TeacherDatas"
                 editable
                 labelWidth="90px"
                 ref="teacher"
                 @formsDatas="addTeacher"
        ></MyForms>
      </div>
    </MyDialog>
  </div>
</template>

<script>
import SchoolList from "@/components/SchoolList.vue";
import Tree from "@/components/Tree.vue";
import {
  addAgency,
  deleteAgency,
  getBaseAgencyList,
  getOrganController,
  addBaseDept,
  addBaseSatge,
  deleteBaseStage,
  deleteBaseDept,
  getTeacherList,
  getTeacher,
  getSchooList,
  addTeacher,
  getStaffList, getCourseList, addCourse
} from '@/api/zhili/index'
import MyDialog from "@/components/MyDialog.vue";
import Forms from "@/components/Forms.vue";
import Title from "@/components/Title.vue";
import TeacherList from '@/components/TeacherList.vue'
import CourseList from "@/components/CourseList.vue";
import School from "@/views/school/index.vue";
import TeachingSetting from "@/components/TeachingSetting.vue";
import MyForms from "@/components/MyForms.vue";
import MyStaff from "@/components/Staff.vue";
// 新增学校信息表单
export function schoolInfo(){
  return [
    {
      prop: 'agencyName',
      label: '机构简称',
      placeholder: '请输入机构简称',
      type: 'input',
      default: '',
      required: true,
    },
    {
      prop: 'fullName',
      label: '机构全称',
      placeholder: '请输入机构全称',
      type: 'input',
      default: '',
      required: true,
    },
    {
      prop: 'address',
      label: '地址',
      placeholder: '请输入地址',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsFountschoolDate',
      label: '成立时间',
      placeholder: '请输入时间',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'introduce',
      label: '简介',
      placeholder: '请输入简介',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLegalPerson',
      label: '法人姓名',
      placeholder: '请输入法人姓名',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLegalpersonIdcard',
      label: '法人身份证号',
      placeholder: '请输入法人身份证号',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalName',
      label: '负责人姓名',
      placeholder: '请输入负责人姓名',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalIdcard',
      label: '负责人身份证号',
      placeholder: '请输入负责人身份证号',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalPhone',
      label: '负责人联系电话',
      placeholder: '请输入负责人联系电话',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'agencyLogo',
      label: 'logo地址',
      placeholder: '请输入logo地址',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLongitude',
      label: '经纬度',
      placeholder: '请输入经纬度',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'subtopic',
      label: '子主题',
      placeholder: '请输入子主题',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsSiteArea',
      label: '用地面积',
      placeholder: '请输入用地面积',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsCoveredArea',
      label: '建筑面积',
      placeholder: '请输入建筑面积',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsUsableArea',
      label: '使用面积',
      placeholder: '请输入使用面积',
      type: 'input',
      default: '',
      required: false,
    }
  ]
}
export function baseDeptInfo(){
  return [
    {
      prop: 'deptName',
      label: '部门名称',
      placeholder: '请输入部门名称',
      type: 'input',
      default: '',
      required: true,
    }
  ]
}
export default {
  name: "OrganiZation",
  computed: {
  },
  dicts:['school_type','school_setup','stage_name'],
  components:{
    MyStaff,
    MyForms,
    CourseList,
    Title,
    Forms,
    MyDialog,
    Tree,
    SchoolList,
    TeacherList
  },
  data(){
    return{
      info:[],
      number:3,
      user:this.$store.getters.role.manager_id,
      searchForm:{
        agencyName:'',
      },
      showEdite:false,
      value:'',
      options:[{
        value:'1',
        label:'公立'
      }],
      treeData:[],
      data: '',
      node: '',
      contents:'',//学校数据
      tearcherContents:'',
      searchValue:'',
      loading:false,
      parameter:{
        pageSize:9,
        pageNum:1,
        fullName:'',
        schoolType:''
      },
      // 老师数据
      teacherContents:'',
      // 任教学校
      teacherParamter:{
        pageNum:1,
        pageSize:12,
        userName:''
      },
      //   新增弹窗
      showNewOrgan:false,
      showAddOragns:false,
      showAddUnit:false,
      showAddSchool:false,
      showAddmember:false,
      showAddBranchSchool:false,
      showAddCourse:false,
      teachingDialog:false,
      showAddTeacherDialog:false,
      //   弹窗数据
      newOrganForm:[],
      schoolInfoForms:[],
      baseDeptsInfo:[],
      // TeacherDatas:[],
      schoolOption:[],
      //   新增部门的表单存储
      newUnitForms:{
        parentId:'',
        stageId:''
      },
      //  提交信息
      campus:{
        agencyType:'',
      },
      campusId:'',
      infos:{
        courseName:'',
        gradeLevel:''
      },
      option: this.schoolOption,
      TeacherDatas: [
        {
          prop: 'headIcon',
          label: '教师头像',
          placeholder: '请上传教师头像',
          type: 'photo',
          default: '',
          required: false,
        },
        {
          prop: 'userName',
          label: '教师姓名',
          placeholder: '请输入教师姓名',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'sex',
          label: '教师性别',
          placeholder: '请选择教师性别',
          type: 'dict',
          dictType: 'sys_user_sex',
          multiple: false,
          default: '',
          options: [],
          required: true,
        },
        {
          prop:'stageId',
          label:'任职学校',
          placeholder: '请选择任职学校',
          type:'select',
          options:[],
          default: '',
          required: true,
        },
        {
          prop: 'idcardType',
          label: '证件类型',
          placeholder: '请选择证件类型',
          type: 'dict',
          dictType: 'idcard_type',
          options: [],
          default: '',
          required: true,
        },
        {
          prop: 'idcard',
          label: '证件号码',
          placeholder: '请输入证件号码',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'phone',
          label: '手机号码',
          placeholder: '请输入手机号码',
          type: 'input',
          default: '',
          required: false,
        },
        {
          prop: 'qq',
          label: 'qq号码',
          placeholder: '请输入qq号码',
          type: 'input',
          default: '',
          required: false,
        },
        {
          prop: 'wx',
          label: '微信号码',
          placeholder: '请输入微信号码',
          type: 'input',
          default: '',
          required: false,
        },
        {
          prop: 'email',
          label: '邮箱号码',
          placeholder: '请输入邮箱号码',
          type: 'input',
          default: '',
          required: false,
        },
      ],
      // 树节点相关
      treeNodeKey:'id',//树组件的节点id key
      nodeParentId:'',
      // 当前部门id
      CurrentDeptId:'',
      staffList:[],//员工列表
      CourseList:[],//学科列表
      CourseParameter:{
        pageSize:10,
        pageNum:1,
        courseName:'',//学科名称
      },
      stageOptions:[{
        label:'幼儿园',
        gradeLevel:'01'
      },
        {
          label:'小学',
          gradeLevel: '02'
        },
        {
          label:'初中',
          gradeLevel: '03'
        },
        {
          label:'高中',
          gradeLevel: '04'
        }]
    }
  },
  methods:{
    change(index){
      this.number=index;
      this.searchForm.agencyName='';
      this.getList();
    },
    //  获取机构信息
    async getList(){
      this.loading = true;
      const data=await getBaseAgencyList(this.parameter);
      this.contents=data;
      console.log(data,'机构信息');
      this.loading=false;
    },
    // 获取组织架构管理 20460WER5F884BB989367D3452ADC3F2
    async getOrganControllList(){
      this.loading = true;
      const {data}=await getOrganController(this.user);
      this.treeData=data;
      console.log(data[0].children[0],'组织架构');
      this.loading = false;
    },
    // 获取教师列表
    async getTeacherLists(parameter){
      this.loading = true;
      if(parameter==null){
        this.teacherContents=await getTeacherList(this.teacherParamter);
      }else{
        this.teacherParamter=parameter;
        this.teacherContents=await getTeacherList(this.teacherParamter);
      }
      this.loading = false;
      // this.loading = true;
      // const data= await getTeacherList(this.teacherParamter);
      // this.teacherContents=data;
      // console.log(data,'教师列表')
      // this.loading = false;
    },
    // 获取学科列表
    async getCourseLists(){
      this.loading=true;
      const data= await getCourseList(this.CourseParameter);
      console.log(data.rows,'学科列表');
      this.CourseList=data;
      this.loading=false;
    },
    // 获取员工
    // getStaffLists(){
    //   console.log(this.CurrentDeptId)
    // },
    // 选择器清空刷新
    clearSelect(){
      this.parameter.schoolType='';
      this.getList();
    },
    // 搜索
    search(){
      this.getList();
    },
    // 教师搜索
    searchTeacher(){
      this.getTeacherLists();
    },
    // 学科搜索
    searchCourse(){
      this.getCourseLists()
    },
    // 页数跳转
    handleCurrentChange(page){
      // sessionStorage.setItem('SchoolListPage',page);
      this.parameter.pageNum=page;
      this.getList();
    },
    // 教师列表跳转
    handleTeacherChange(page){
      sessionStorage.setItem('goto',true);// 记录点击页面跳转操作
      sessionStorage.setItem('TeacherPage',page);// 存储跳转page
      this.teacherParamter.pageNum=page;
      this.getTeacherLists();
    },
    // 学科列表跳转
    handleCourseChange(page){
      this.CourseParameter.pageNum=page;
      this.getCourseLists();
    },
    // 机构设置页面新增机构弹窗
    newOrgan(){
      this.newOrganForm=[
        {
          prop: 'agencyType',
          label: '机构类型',
          placeholder: '请选择机构类型',
          type: 'select',
          multiple: false,
          default: '',
          options: [
            { value: 2, label: '学校' },
            { value: 3, label: '培训机构' },
          ],
          required: true,
        },{
          prop: 'schoolType',
          label: '学校类型',
          placeholder: '请选择学校类型',
          type: 'dict',
          dictType:'school_type',
          default: '',
          options: [],
          required: true,
        },
        {
          prop: 'schoolSetup',
          label: '办学类型',
          placeholder: '请选择办学类型',
          type: 'dict',
          dictType: 'school_setup',
          default: '',
          options: [],
          required: true,
        }].concat(schoolInfo())
      this.showNewOrgan=true;
    },
    // 机构设置页面新增机构实现
    addNewOrgan(res){
      addAgency(res).then(()=>{
        this.$message.success('机构添加成功!');
        this.getList()
      })
      this.showNewOrgan=false;
    },
    // 新增科室弹窗
    showAddDepartment(data){
      this.newUnitForms.parentId='';
      console.log(data.id);
      this.nodeParentId=data.id;// 新增节点的父级id
      this.newUnitForms.agencyId=data.agencyId;
      if(data.stageId){
        this.newUnitForms.stageId=data.stageId;
      }else{
        this.newUnitForms.stageId='';
      }
      this.showAddOragns=true;
      if(data.id==='dept'){
        this.newUnitForms.parentId=null;
      }else{
        this.newUnitForms.parentId=data.deptId;
      }
    },
    // 新增科室实现
    AddDepartment(data){
      let res=Object.assign(this.newUnitForms,data);
      console.log(res);
      let formData={
        addDept:true,
        addUser:true,
        type:'dept',
        label:res.deptName,
        parentId:this.nodeParentId,
        // id:'' //需要添加成功后返回的id
      }
      this.$refs.customtree.treeAddNode(formData);
      addBaseDept(res).then(()=>{
        this.$message.success('添加成功!');
        // this.getOrganControllList();
      }).catch(()=>{});
      this.showAddOragns=false;
      this.nodeParentId='';
    },
    // 新增直属单位弹窗
    showAddUnits(node,data){
      this.campus.agencyType='1';//直属单位
      this.nodeParentId=data.id;
      this.showAddUnit=true;
    },
    // 新增直属单位实现
    AddUnit(res){
      res=Object.assign(res,this.campus);
      console.log(res)
      let formData={
        label:res.agencyName,
        parentId:this.nodeParentId
      };
      this.$refs.customtree.treeAddNode(formData);
      addAgency(res).then(()=>{
        // this.getOrganControllList();
        this.$message.success('添加成功!');
      }).catch(()=>{});
      this.showAddUnit=false;
      this.nodeParentId='';
    },
    // 新增学校弹窗
    showAddCampus(node,data){
      this.campus.agencyType='2';//学校
      this.nodeParentId=data.id;
      if(data.id==='shcool'){
        this.schoolInfoForms=[ {
          prop: 'schoolType',
          label: '学校类型',
          placeholder: '请选择学校类型',
          type: 'dict',
          dictType:'school_type',
          default: '',
          options: [],
          required: true,
        },{
            prop: 'schoolSetup',
            label: '办学类型',
            placeholder: '请选择办学类型',
            type: 'dict',
            dictType: 'school_setup',
            default: '',
            options: [],
            required: true,
          },].concat(schoolInfo())
      }else if(data.id==="kindergarten"){
        this.campus.schoolType='01';//幼儿园
        this.schoolInfoForms=[ {
          prop: 'schoolSetup',
          label: '办学类型',
          placeholder: '请选择办学类型',
          type: 'dict',
          dictType: 'school_setup',
          default: '',
          options: [],
          required: true,
        },].concat(schoolInfo())
      }
      this.showAddSchool=true;
    },
    // 新增学校实现
    AddCampus(res){
      res=Object.assign(res,this.campus);
      let formData={
        label:res.fullName,
        parentId:this.nodeParentId,
        addDept: null,
        // id:"03137AFB29814DD5B60CE7D6321C88FF"
      }
      this.$refs.customtree.treeAddNode(formData)
      console.log(res)
      addAgency(res).then(()=>{
        this.$message.success('添加成功！');
        // const data=this.getOrganControllList();
        // console.log(data,'添加后')
      }).catch(()=>{})
      this.showAddSchool=false;
      this.campus.agencyType='';
    },
    // 新增分校
    showAddBranchCampus(node,data){
      console.log(node,data)
      this.campus.agencyType='2';//学校
      this.campus.agencyId=data.agencyId;
      this.schoolInfoForms=[{
        prop: 'schoolType',
        label: '学校类型',
        placeholder: '请选择学校类型',
        type: 'dict',
        dictType:'school_type',
        default: '',
        options: [],
        required: true,
      },
        {
          prop: 'schoolSetup',
          label: '办学类型',
          placeholder: '请选择办学类型',
          type: 'dict',
          dictType:'school_setup',
          default: '',
          options: [],
          required: true,
        }].concat(schoolInfo())
      this.showAddBranchSchool=true;
    },
    // 新增分校实现
    AddBranchSchool(res){
      res=Object.assign(res,this.campus);
      console.log(res);
      addBaseSatge(res).then(()=>{
          this.$message.success('添加成功！');
          this.getOrganControllList();
        }).catch(()=>{});
      this.showAddBranchSchool=false;
    },
    // 新增成员
    showAddMembers(node,data){
      this.showAddmember=true;
    },
    // 删除机构
    Delete(node, data) {
      if(node.parent.data.id==='stage'){
        this.$modal.confirm('确定删除吗？').then(()=>{
          this.$refs.customtree.treeDeleteNode(data);// 删除节点
          deleteBaseStage(data.id).then(()=>{
          this.$modal.msgSuccess('删除成功！');
          // this.getOrganControllList();
          })
        }).catch(()=>{})
      }
      if(node.parent.data.id==='shcool'|| node.parent.data.id==='kindergarten'||node.parent.data.id==='agen'){
        this.$modal.confirm('确定删除吗？').then(()=>{
          this.$refs.customtree.treeDeleteNode(data);//删除树节点 避免全局刷新
          deleteAgency(data.id).then(()=>{
          this.$modal.msgSuccess('删除成功')
          // this.getOrganControllList();
          })
        }).catch(()=>{});
      }
    },
    // 删除部门单独实现
    deleteOrgan(data){
      this.$modal.confirm('确定删除该部门吗？').then(()=>{
        this.$refs.customtree.treeDeleteNode(data);
        deleteBaseDept(data.id).then(()=>{
        this.$modal.msgSuccess('删除成功！');
        // this.getOrganControllList();
        })
      }).catch(()=>{});
    },
    // 编辑
    Edit(node,data){
      // 如果是学校跳转至详情/detail
      console.log(data)
      this.$router.push({
        path:'/zhili/organization/detail',
        query:{
          id:data.id
        }
      });
      // 如果是职员 跳转至/personaldetial
    },

    // 新增学科弹窗
    AddCourse(){
      this.showAddCourse=true;
    },
    // 新增学科弹窗确认
    Courseconfirm(){
      console.log(this.infos);
      addCourse(this.infos).then(()=>{
        this.$message.success('添加成功!');
        this.getCourseLists();
      })
      this.showAddCourse=false;
    },
    // 任职学校弹窗
    SchoolEdit(){
      this.teachingDialog=true;
    },
    // 新增老师
    addTeacher(res){
      addTeacher(res).then(()=>{
        this.$message.success('添加成功!');
        this.getTeacherList();
      })
      this.showAddTeacherDialog=false;
    },
    // 点击树节点
    async handleNodeClick(data,node){
      // console.log(data);
      console.log(data.deptId,'部门id');
      // console.log(data.label);
      console.log(this.CurrentDeptId,'00')
      if(node.data.deptId){
        this.CurrentDeptId=data.deptId;// 点击节点后获取部门id deptId
        const res=await getStaffList(data.deptId);
        this.staffList=res.rows;
        console.log(this.CurrentDeptId,'11')

      }
      // console.log(this.staffList)
      this.CurrentDeptId='';// 清空部门id
      console.log(this.CurrentDeptId,'22')

    }
  },
  created(){
    // this.selected=(this.user=='tom'?this.fal=true:this.fal=false)?'first':'second';
    this.getList();// 获取机构
    this.getOrganControllList();//获取组织架构
    this.getCourseLists();//获取学科列表
    this.schoolInfoForms=schoolInfo();
    this.baseDeptsInfo= baseDeptInfo();
    this.getTeacherLists();
    // 处理学校列表 保留fullName stageId
    getSchooList().then((res)=>{
      res.forEach(item=>{
        let obj={
          label:'',
          value:''
        }
        obj.label=item.fullName;
        obj.value=item.stageId;
        this.schoolOption.push(obj);
      })
    });
    getStaffList('9FF25E1BCAF645118320B22A59D6B730')
  },
  // 处理任教老师中添加老师学校选择数据
  mounted() {
    this.TeacherDatas.forEach(item=>{
      if(item.prop==='stageId'){
        item.options=this.schoolOption;
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.container{
  width:1200px;
  margin:0 auto;
  display: flex;
  flex-direction: column;
  .box{
    border-radius: 8px;
    padding:0 20px;
    background-color: #fff;
    display: flex;
  }
  .content-box{
    margin:3rem 0;
    height:50px;
    align-items: center;
    .btn_anniu{
      margin:0 1rem;
      height:3.7rem ;
      font-size: 1.8rem;
      font-weight: bold;
      border: 0 solid #fff;
      color: #0f4440;
      opacity: .5;
      cursor:pointer;
      outline: none;
      background: #fff;
    }
    .newStyle{
      position:relative;
      color: #0f4440;
      font-size: 1.8rem;
      font-weight: bold;
      opacity: .9;
    }
    .newStyle:before{
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 50%;
      height: 1px;
      border-bottom: .4rem solid #0f4440;
      box-sizing: border-box;
      opacity: .9;
    }
  }
  .content{
    //background-color:#fff;
    margin-bottom:3rem;
    .head{
      width:1160px;
      margin:2rem 0;
      wdith:50px;
      display:flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .contents{
      display:flex;
      flex-direction: row;
      //justify-content: space-between;
      flex-wrap: wrap;
      .paging{
        margin-top: 30px;
        width:100%;
        text-align: center;
        margin-bottom: 20px;
      }
    }
  }
}
.course-paging{
  margin-top: 30px;
  width:100%;
  text-align: center;
  margin-bottom: 20px;
}
.schoolDialog {
  .el-select {
    display: flex;
    flex: 1;
  }
}
::v-deep .head .el-select .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;
  font-size: 16px;
  font-weight: 700;

  &::placeholder {
    color: #0f4444;
  }
}

::v-deep .head .el-select .el-icon-arrow-up:before {
  content: '\e78f';
  color:#0f4444;
}

::v-deep .head .el-select .el-input.is-focus .el-input__inner {
  border-color: #0f4444;
}

::v-deep .head .el-input .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;

}
::v-deep .body .el-tree-node__label {
  font-size:1.6rem;
  color:#0f4444;
  opacity: .8;
  font-weight: 600;
}
::v-deep .head .el-select-dropdown__item.selected{
  color:#0f4444;
}
::v-deep .el-collapse-item__arrow{
  display:none;
}
::v-deep .el-dialog__body{
  padding:10px 20px;
}
</style>
