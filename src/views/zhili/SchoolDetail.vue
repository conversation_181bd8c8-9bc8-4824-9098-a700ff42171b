<template>
  <div class="container">
    <!--    基础信息-->
    <div class="basic">
      <div class="flex" style="justify-content: space-between;width: 100%">
        <Title title="基础信息"></Title>
        <el-button class="edit_btn" @click="showBase">编辑</el-button>
      </div>
      <div class="baseInfo">
        <div style="width: 100%">
          <div  style="font-size: 20px;margin-left:40rem;font-weight:600"><span>{{schoolName}}</span></div>
          <Forms :columns="formColumns" labelWidth="8rem"></Forms>
        </div>
      </div>
    </div>
    <!--    法人信息-->
    <div class="basic">
      <Title title="法人及负责人信息"></Title>
      <el-button class="edit_btn" @click="showPersonInfomation">编辑</el-button>
      <div class="baseInfo">
        <Forms :columns="legalpersonInfo" labelWidth="12rem"></Forms>
      </div>
    </div>
    <!--    其他信息-->
    <div class="basic">
      <Title title="其他信息"></Title>
      <el-button class="edit_btn" @click="showOther">编辑</el-button>
      <div class="baseInfo">
        <Forms :columns="otherInfo"  labelWidth="8rem"></Forms>
      </div>
    </div>

    <!--    弹窗-->
    <!--    1.基础信息-->
    <MyDialog title="基础信息" v-if="showBaseDialog"
              @close="showBaseDialog=!showBaseDialog"
              @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms :columns="formColumns" :editable="true" ref="forms" @formsDatas="editBaseInfo" labelWidth="8rem"></Forms>
      </div>
    </MyDialog>

    <!--    2.法人-->
    <MyDialog title="法人及负责人信息" v-if="showPersonInfo"
              @close="showPersonInfo=!showPersonInfo"
              @confirm="$refs.forms.getFormsDatas()">
      <div><Forms :columns="legalpersonInfo" :editable="true" ref="forms" @formsDatas="editLegalInfo" labelWidth="12rem"></Forms></div>
    </MyDialog>

    <!--    3.其他-->
    <MyDialog title="其他信息" v-if="showOthers"
              @close="showOthers=!showOthers"
              @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms :columns="otherInfo" :editable="true" ref="forms" @formsDatas="editOtherInfo" labelWidth="10rem"></Forms>
      </div>
    </MyDialog>

  </div>
</template>

<script>
import Title from "@/components/Title.vue";
import Forms from "@/components/Forms.vue";
import MyDialog from "@/components/MyDialog.vue";
import {getBaseAgency,updatabaseAgency,deleteAgency} from "@/api/zhili/index";
import {getDicts} from "@/api/system/dict/data";
export function formColumn(){
  return [
    {
      prop: 'agencyLogo',
      label: '头像',
      placeholder: '',
      type: 'photo',
      default: '',
      required: false,
    },
    {
      prop: 'agencyCode',
      label: '编码',
      placeholder: '请输入编码',
      type: 'input',
      default: '',
      required: true,
    },
    {
      prop: 'agencyType',
      label: '机构类型',
      placeholder: '请选择机构类型',
      type: 'dict',
      dictType:'agency_type',
      multiple: false,
      default: '',
      options: [],
      required: true,
    },
    {
      prop: 'address',
      label: '地址',
      placeholder: '请输入地址',
      type: 'input',
      default: '',
      required: true,
    },
    {
      prop: 'cdsFountschoolDate',
      label: '成立时间',
      placeholder: '请输入时间',
      type: 'input',
      default: '',
      required: true,
    },
    {
      prop: 'introduce',
      label: '简介',
      placeholder: '请输入简介',
      type: 'input',
      default: '',
      required: false,
    },
  ]
}
export default {
  name: "OrganiZationDetail",
  components:{
    MyDialog,
    Title,
    Forms
  },
  data(){
    return{
      showBaseDialog:false,
      showPersonInfo:false,
      showOthers:false,
      formColumns:[],
      legalpersonInfo:[
        {
          prop: 'cdsLegalPerson',
          label: '法人姓名',
          placeholder: '请输入法人姓名',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'cdsLegalpersonIdcard',
          label: '法人身份证号',
          placeholder: '请输入法人身份证号',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'cdsPrincipalName',
          label: '负责人姓名',
          placeholder: '请输入负责人姓名',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'cdsPrincipalIdcard',
          label: '负责人身份证号',
          placeholder: '请输入负责人身份证号',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'cdsPrincipalPhone',
          label: '负责人联系电话',
          placeholder: '请输入负责人联系电话',
          type: 'input',
          default: '',
          required: true,
        }
      ],
      otherInfo:[
        {
          prop: 'agencyLogo',
          label: 'logo地址',
          placeholder: '请输入logo地址',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'cdsLongitude',
          label: '经纬度',
          placeholder: '请输入经纬度',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'subtopic',
          label: '子主题',
          placeholder: '请输入子主题',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'cdsSiteArea',
          label: '用地面积',
          placeholder: '请输入用地面积',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'cdsCoveredArea',
          label: '建筑面积',
          placeholder: '请输入建筑面积',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'cdsUsableArea',
          label: '使用面积',
          placeholder: '请输入使用面积',
          type: 'input',
          default: '',
          required: true,
        }
      ],
      schoolName:'',
      list: {
        item:[]
      },
      finalInfo:{
        agencyId:this.$route.query.id
      }
    }
  },
  methods:{
    //获取表单信息
    async getFormList(){
      await getBaseAgency(this.$route.query.id).then(res=>{
        let item=res.data;
        let Form=[{
          prop: 'schoolType',
          label: '学校类型',
          placeholder: '请选择学校类型',
          type: 'dict',
          dictType:'school_type',
          default: '',
          options: [],
          required: true,
        },
          {
            prop: 'schoolSetup',
            label: '办学类型',
            placeholder: '请选择办学类型',
            type: 'dict',
            dictType:'school_setup',
            default: '',
            options: [],
            required: false,
          }]
        this.FormColumns=Form.concat(formColumn());
        if(item.agencyType==='2'||item.agencyType==='3'){
          this.formColumns=this.FormColumns;//针对学校
        }else{
          this.formColumns=formColumn();//针对直属单位
        }
        this.schoolName=item.fullName;
        this.formColumns.forEach(items=>{
          items.default=item[items.prop];
        });
        this.legalpersonInfo.forEach(its=>{
          its.default=item[its.prop]
        });
        this.otherInfo.forEach(it=>{
          it.default=item[it.prop]
        });
      }).catch(()=>{})
    },
    //   弹窗处理
    showBase(){
      this.showBaseDialog=true;
    },
    showPersonInfomation(){
      this.showPersonInfo=true;
    },
    showOther(){
      this.showOthers=true;
    },
    // 修改基础信息
    editBaseInfo(res){
      let finalInfo=Object.assign(this.finalInfo,res)
      updatabaseAgency(finalInfo).then(()=>{}).catch(()=>{})
      this.getFormList();
      this.showBaseDialog=false;
    },
    // 修改法人信息
    editLegalInfo(res){
      let finalInfo=Object.assign(this.finalInfo,res)
      updatabaseAgency(finalInfo).then(()=>{}).catch(()=>{})
      this.getFormList();
      this.showPersonInfo=false;
    },
    // 修改其他信息
    editOtherInfo(res){
      let finalInfo=Object.assign(this.finalInfo,res)
      updatabaseAgency(finalInfo).then(()=>{}).catch(()=>{})
      this.getFormList();
      this.showOthers=false;
    }
  },
  created(){
    this.getFormList();
  }
}
</script>

<style lang="scss" scoped>
.container{
  width:1200px;
  margin:0 auto;
  display: flex;
  flex-direction: column;
  .content{
    margin:2rem 0;
    display: flex
  }
  .basic{
    margin-top:3rem;
    padding:2rem;
    display:flex;
    justify-content: space-between;
    flex-wrap: wrap;
    border-radius: 8px;
    padding:2rem;
    margin-bottom: 3rem;
    background-color: #fff;
    .baseInfo {
      display: flex;
      justify-content: space-around;
      width: 100%;
      //padding: 2rem;
    }
  }
  .person{
    margin-bottom:3rem;
  }
}
.el-button--success{
  background-color:#3b8989;
  color:#fff !important;
}
.el-button--success:hover{
  color:#3b8989 !important;
}
.el-button--delete{
  color:red !important;
  border-color:red !important;
  background-color:#fff !important;
}
.el-button--delete:hover{
  background-color: #f5caca !important;
}
</style>
