<!-- 年级班级设置 -->
<template>
  <scale-box
      :width="1920"
      :height="1080"
      bgc="transparent"
      :delay="100">
    <div class="container">
      <div class="header">
        <div class="roles">
          <RolesItem color="#004B97"></RolesItem>
        </div>
      </div>
      <div class="box">
        <div class="home">
          <span @click="$router.push('/')">公共空间</span>
          <!--          <img style="max-width: 30px;" src="../../assets/huawei_logo.png">-->
        </div>


        <div class="warp">
          <div class="b_t">
            <div class="t_l">
              <div class="person">
                <div class="userInfo flex">
                  <div
                      class="pointer"
                      @click="$router.push('/personal')">
                    <img
                        :src="
                        userInfo.headIcon
                          ? userInfo.headIcon
                          : userInfo.sex == 0
                          ? male
                          : female
                      "/>
                  </div>
                  <div
                      class="m_l_10"
                      @click="$router.push('/personal')">
                    <span
                        class="fs_22 pointer"
                        style="color: #2f4576">
                      {{ role.role_name }}
                    </span>
                    <div class="name pointer">
                      {{ role.full_name }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="yygn">
                <div class="title fs_20">应用功能</div>
                <div class="content">
                  <div class="item"
                       @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/officCirculation/manage')">
                    <img src="../../assets/zhili/Group 319.png"/>
                    <div class="title2 fs_18">OA收发</div>
                  </div>
                  <div class="item"
                       @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/notice/office_list')">
                    <img src="../../assets/zhili/Group 315.png"/>
                    <div class="title2 fs_18">通知公告</div>
                  </div>
                  <div
                      class="item"
                      @click="teacherPortrait">
                    <img src="../../assets/zhili/Group 715.png"/>
                    <div class="title2 fs_18">教师画像</div>
                  </div>
                  <div class="item"
                       @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/dock/commonJump/13')">
                    <img src="../../assets/zhili/Group 1022.png"/>
                    <div class="title2 fs_18">装备管理</div>
                  </div>
                  <div class="item"
                       @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/studentLeaveCheck/checkrecords')">
                    <img src="../../assets/zhili/Group 987.png"/>
                    <div class="title2 fs_18">健康晨午检</div>
                  </div>
                  <div
                      class="item"
                      @click="
                      $router.push({
                        path: '/tools',
                        query: { name: '应用功能' },
                      })
                    ">
                    <img src="../../assets/zhili/Group 317.png"/>
                    <div class="title2 fs_18">更多</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="t_r">
              <div class="r_t">
                <div class="content">
                  <div class="item">
                    <div class="fs_20 line">学校机构数</div>
                    <div
                        class="title fs_40"
                        style="color: #2f4576">
                      5656
                    </div>
                  </div>
                  <div class="item">
                    <div class="fs_20 line">在校学生数</div>
                    <div
                        class="title fs_40"
                        style="color: #2f4576">
                      5656
                    </div>
                  </div>
                  <div class="item">
                    <div class="fs_20">教师数量</div>
                    <div
                        class="title fs_40"
                        style="color: #2f4576">
                      5656
                    </div>
                  </div>
                  <div class="item line">
                    <div class="fs_20">传染病数量</div>
                    <div
                        class="title fs_40"
                        style="color: #2f4576">
                      5656
                    </div>
                  </div>
                  <div class="item">
                    <div class="fs_20">请假数量</div>
                    <div
                        class="title fs_40"
                        style="color: #2f4576">
                      5656
                    </div>
                  </div>
                </div>
              </div>
              <div class="r_b">
                <div class="title fs_20">体育数据</div>
                <div class="content">
                  <div class="item">
                    <a @click="getDetail('https://www.wuhousmartedu.com/#/resource/region')">
                      <img src="../../assets/zhili/Group 2434.png"/>
                      <div class="title2 fs_24">德育</div>
                    </a>
                  </div>
                  <div class="item">
                    <a @click="getDetail('https://www.wuhousmartedu.com/#/resource/region')">
                      <img src="../../assets/zhili/Group 2435.png"/>
                      <div class="title2 fs_24">智育</div>
                    </a>
                  </div>
                  <div class="item">
                    <a @click="getDetail('https://www.wuhousmartedu.com/#/resource/region')">
                      <img src="../../assets/zhili/Group 2436.png"/>
                      <div class="title2 fs_24">美育</div>
                    </a>
                  </div>
                  <div class="item">
                    <a @click="getDetail('https://www.wuhousmartedu.com/#/resource/region')">
                      <img src="../../assets/zhili/Group 2437.png"/>
                      <div class="title2 fs_24">体育</div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="b_b">
            <div class="b_box">
              <div
                  @click="getDetail('https://www.wuhousmartedu.com/#/resource/myCenter?scene=noSearch&ticket=ST-39402-0QsvoiiwXi5TG1MKhzwX-localhost')"
                  class="item">我的网盘
              </div>
              <div class="item">
                <a
                    href="https://www.smartedu.cn"
                    target="_blank">
                  国家中小学智慧教育平台
                </a>
              </div>
              <div @click="getDetail('https://www.wuhousmartedu.com/#/resource/region')" class="item">智慧云资源库</div>
              <div @click="getDetail('http://**************:8879/index.html')" class="item">安全平台</div>
            </div>
          </div>
          <div class="custom">
            <div
                class="text"
                @click="$router.push('/setting')">
              <div>功能</div>
              <div>设置</div>
            </div>
          </div>
        </div>
      </div>
      <div class="tzgg">
        <div class="warp">
          <div class="title">通知公告</div>
          <div class="contant">
            <div
                class="item"
                v-for="i in 4">
              <div class="fs_15 flex aligin_center">
                <i class="icon"></i>
                通知名称
              </div>
              <div class="opacity_6 fs_12">2023-02-02</div>
            </div>
            <div class="page">
              <el-pagination
                  :total="90"
                  :page-size="4"
                  :pager-count="4"
                  background
                  :hide-on-single-page="true"
                  layout=" pager"></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </scale-box>
</template>

<script>
import ScaleBox from 'vue2-scale-box';
import {mapGetters} from 'vuex';
import RolesItem from '@/components/RolesItem';
import male from '@/assets/avatar/maleTeacher.png';
import female from '@/assets/avatar/femaleTeacher.png';
import {getToken} from '@/api/app';
import axios from 'axios';

export default {
  name: 'youjiaoHome',
  data: () => {
    return {
      female,
      male,
    };
  },
  components: {
    RolesItem,
    ScaleBox,
  },
  computed: {
    ...mapGetters(['roles', 'role', 'userInfo']),
  },
  methods: {
    getDetail(url) {
      window.open(url);
    },
    //教师画像跳转
    async teacherPortrait() {
      //本地测试代码
      // const { data } = await getToken({ phone: this.userInfo.phone });
      // window.open(
      //   ' http://wh.21spt.com:8000/login.aspx?BaseToken=?BaseToken=' +
      //     data.BaseToken
      // );
      //线上打包代码
      axios({
        method: 'post',
        url: ' https://whkj.wuhousmartedu.com:8443/aouth/sys/portalLogin',
        headers: {
          'Content-type': 'application/x-www-form-urlencoded',
        },
        params: {
          phone: this.userInfo.phone,
        },
      })
          .then((response) => {
            window.open(
                ' http://wh.21spt.com:8000/login.aspx?BaseToken=' +
                response.data.data.BaseToken
            );
          })
          .catch((error) => {
            console.log(error);
          });
    },
  },
};
</script>

<style scoped lang="scss">
.title {
  font-family: YouSheBiaoTiHei;
  color: #fff;
}

.title2 {
  font-family: OPPOSans-M;
  color: #fff;
}

.container {
  height: 1080px;
  width: 1920px;
  margin: 0 auto;
  background-image: url('../../assets/zhili/Mask group.png');
  background-size: 100% 100%;

  .header {
    position: absolute;

    right: 20px;
    top: 14px;
  }

  .tzgg {
    width: 348px;
    height: 400px;
    position: absolute;
    right: 45px;
    top: 130px;
    background-image: url('../../assets/zhili/Group 2473.png');
    background-repeat: no-repeat;

    .warp {
      padding: 0 20px;

      margin-top: 110px;
    }

    .title {
      font-size: 2.4rem;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      padding: 12px;
      color: #20223d;
    }

    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      color: #20223d;
      cursor: pointer;
      margin-bottom: 12px;

      .icon {
        height: 24px;
        width: 24px;
        display: inline-block;
        margin-right: 4px;
        background-image: url('../../assets/zhili/Group 1588.png');
      }
    }

    .page {
      margin-top: -3px;
      text-align: center;

      ::v-deep .el-pager li:not(.disabled).active {
        background-color: #004b97 !important;
      }
    }
  }

  .box {
    .custom {
      position: absolute;
      bottom: 0;
      right: 40px;
      height: 118px;
      width: 118px;
      background-repeat: no-repeat;
      background-position: bottom;
      background-image: url('../../assets/zhili/Mask group_1.png');

      font-family: YouSheBiaoTiHei;
      font-size: 2rem;
      color: #fff;
      text-shadow: 2px 2px 1px rgba(0, 0, 0, 1);
      text-align: center;
      display: flex;
      align-items: end;

      .text {
        cursor: pointer;
        margin-left: 42px;
        margin-bottom: 8px;
        line-height: 22px;
      }
    }

    width: 1257px;
    height: 637px;
    margin-left: 203px;
    margin-top: 204px;
    position: absolute;

    .home {
      display: flex;

      span {
        background-color: #ccdbea;
        width: 84px;
        border-radius: 16px;
        font-size: 1.5rem;
        height: 28px;
        font-weight: 700;
        color: #263b67;
        text-align: center;
        line-height: 26px;
        margin-left: 50px;
        margin-top: 20px;
        cursor: pointer;
      }

      img {
        margin-left: 10px;
        margin-top: 20px;
      }
    }


    .warp {
      height: 578px;
      margin-top: 10px;
      display: flex;
      flex-direction: column;

      .b_t {
        height: 450px;
        display: flex;
        padding: 0 40px;

        .t_l {
          width: 335px;

          display: flex;
          flex-direction: column;
          padding: 0 5px;
          box-sizing: border-box;

          .person {
            height: 130px;

            .userInfo {
              padding: 25px;
              // cursor: pointer;
              color: #fff;
              display: flex;
              flex-wrap: wrap;

              .name {
                background-color: rgba(255, 255, 255, 0.3);
                color: #2f4576;
                border-radius: 11px;
                font-size: 12px;
                padding: 3px 8px;
                margin-top: 7px;
              }

              .box {
                height: 154px;
                width: 100%;
                cursor: default;
                margin-top: 30px;
                background-color: #7e3510;
                display: grid;
                grid-template-rows: repeat(2, 50%);
                grid-template-columns: repeat(2, 50%);
                justify-content: center;
                justify-items: center;
                align-items: center;

                .num {
                  font-family: BebasNeue;
                }
              }

              img {
                width: 39px;
                height: 39px;
                border-radius: 50%;
              }
            }
          }

          .yygn {
            flex: 1;
            background-color: #2f4576;
            border-radius: 10px;
            margin-top: 20px;
            padding: 30px;
            display: flex;
            flex-direction: column;

            .content {
              flex: 1;
              display: grid;
              grid-template-rows: repeat(2, 56%);
              grid-template-columns: repeat(3, 33.3%);

              margin-left: -27px;
              padding: 20px 0;
              justify-content: center;
              align-items: center;

              .item {
                cursor: pointer;
                display: flex;
                flex-direction: column;

                align-items: center;

                img {
                  width: 50px;
                  height: 50px;
                  padding-bottom: 10px;
                }
              }
            }
          }
        }

        .t_r {
          flex: 1;

          padding: 0px 10px;
          display: flex;
          flex-direction: column;

          .r_t {
            height: 240px;

            .content {
              padding: 40px 60px;
              display: grid;
              grid-template-rows: repeat(2, 90px);
              grid-template-columns: repeat(3, 33.3%);

              height: 100%;

              .item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                font-family: PingFangSC;
                color: #2f4576;
                position: relative;
              }

              .line::after {
                position: absolute;
                content: '';
                border-left: 2px #fff solid;
                opacity: 0.5;
                height: 50px;
                left: 220px;
                top: 25px;
              }
            }
          }

          .r_b {
            background-color: #2f4576;
            flex: 1;
            border-radius: 10px;
            margin-top: 20px;
            padding: 30px;

            .content {
              height: 100px;
              display: flex;
              justify-content: space-between;

              .item {
                margin-top: 20px;
                width: 170px;
                cursor: pointer;
                display: flex;
                background-color: #385d91;
                align-items: center;
                justify-content: center;
                border-radius: 10px;

                img {
                  width: 50px;
                  height: 50px;
                  padding-right: 20px;
                }
              }
            }
          }
        }
      }

      .b_b {
        padding: 20px 40px;
        flex: 1;

        .b_box {
          height: 100%;
          display: flex;
          padding: 0 5px;

          a {
            color: #fff;
            text-decoration: none;
          }

          .item {
            width: 326px;
            height: 80px;
            background-color: #2f4576;
            border-radius: 10px;
            cursor: pointer;
            font-size: 2.4rem;
            margin-right: 15px;
            font-family: OPPOSans-M;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
