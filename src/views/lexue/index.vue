<!-- 年级班级设置 -->
<template>
  <scale-box
      :width="1920"
      :height="1080"
      bgc="transparent"
      :delay="100">
    <div class="container">
      <div
          class="home"
          @click="$router.push('/')">
        <div class="text">公共空间</div>
      </div>
      <div class="box">
        <div class="b_l">
          <div class="huawei_logo">
            <!--            <img src="../../assets/huawei_logo.png">-->
          </div>
          <div class="userInfo">
            <div class="person">
              <div class="info flex">
                <div @click="$router.push('/personal')">
                  <img
                      :src="
                      userInfo.headIcon
                        ? userInfo.headIcon
                        : userInfo.sex == 0
                        ? male
                        : female
                    "/>
                </div>
                <div
                    class="m_l_10"
                    @click="$router.push('/personal')">
                  <span
                      class="fs_22"
                      style="color: #9f4111; font-weight: 700">
                    {{ userInfo.userName }}同学
                  </span>
                  <div class="name">{{ role.full_name }}</div>
                </div>
              </div>
            </div>
            <div class="warp">
              <div class="item">
                <div class="num fs_34">5</div>
                <div class="fs_15">获奖次数</div>
              </div>
              <div class="item">
                <div class="num fs_34">30</div>
                <div class="fs_15">我的红花</div>
              </div>
              <div class="item">
                <div class="num fs_34">5</div>
                <div class="fs_15">当日运动时长</div>
              </div>
              <div class="item">
                <div class="num fs_34">10</div>
                <div class="fs_15">上传消息次数</div>
              </div>
            </div>
          </div>
          <div class="tzgg">
            <div class="warp">
              <div class="title">通知公告</div>
              <div class="contant">
                <div
                    class="item"
                    v-for="i in 4">
                  <div class="fs_15 flex aligin_center">
                    <i class="icon"></i>
                    通知名称
                  </div>
                  <div class="opacity_6 fs_12">2023-02-02</div>
                </div>
                <div class="page">
                  <el-pagination
                      :total="90"
                      :page-size="4"
                      :pager-count="4"
                      background
                      :hide-on-single-page="true"
                      layout=" pager"></el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="b_r">
          <div
              class="tool"
              @click="
              $router.push({
                path: '/tools',
                query: { name: '应用功能' },
              })
            ">
            更多应用
          </div>
          <div class="content">
            <div class="flex">
              <div class="item">
                <div
                    class="w_gj"
                    style="margin-left: 1px"
                    @click="$router.push('/setting')">
                  <div><img src="../../assets/lexue/Group 2478.png"/></div>
                  <div>我的班级</div>
                </div>
              </div>
              <div class="item">
                <a href="https://www.wuhousmartedu.com/#/resource/myCenter?scene=noSearch&ticket=ST-39402-0QsvoiiwXi5TG1MKhzwX-localhost"
                   target="_blank">
                  <div
                      class="w_gj"
                      style="margin-left: 34px">
                    <div><img src="../../assets/lexue/Group 2451.png"/></div>
                    <div>我的网盘</div>
                  </div>
                </a>
              </div>
              <div class="item">
                <div
                    class="w_gj"
                    style="margin-left: 33px">
                  <div><img src="../../assets/lexue/folder.png"/></div>
                  <div>
                    国中小学
                    <br/>
                    智慧平台
                  </div>
                </div>
              </div>
              <div class="item">
                <a href="https://www.wuhousmartedu.com/#/resource/region" target="_blank">
                  <div
                      class="w_gj"
                      style="margin-left: 28px">
                    <div><img src="../../assets/lexue/folder.png"/></div>
                    <div>
                      智慧云资
                      <br/>
                      源库
                    </div>
                  </div>
                </a>
              </div>
            </div>
            <div class="warp">
              <div class="item">
                <a @click="getDetail('https://hdds.wuhousmartedu.com/index.html')">
                  <div
                      class="w_yy"
                      style="margin-left: 70px; margin-top: 150px">
                    精彩活动
                  </div>
                </a>
              </div>
              <div class="item">
                <a @click="getDetail('https://whqjyy.wuhousmartedu.com/staticOut/#/honor/honorQuiry')">
                  <div
                      class="w_yy"
                      style="margin-left: 88px; margin-top: 150px">
                    获奖查询
                  </div>
                </a>
              </div>
              <div class="item">
                <a @click="getDetail('https://edures.lexuestudy.com/#/index/index')">
                  <div
                      class="w_yy"
                      style="margin-left: 98px; margin-top: 150px">
                    乐学通
                  </div>
                </a>
              </div>
              <div class="item">
                <a @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/CourseSelect/html/stu_course_batch.html')">
                  <div
                      class="w_yy"
                      style="margin-left: 69px">
                    社团选课
                  </div>
                </a>
              </div>
              <div class="item">
                <a @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/wisdomTask/expandListStudent')">
                  <div
                      class="w_yy"
                      style="margin-left: 88px">
                    学习反馈
                  </div>
                </a>
              </div>
              <div class="item">
                <a @click="getDetail('https://iuppbl.wuhousmartedu.com/index.html?login=success#/home')">
                  <div
                      class="w_yy"
                      style="margin-left: 98px">
                    项目式学习
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </scale-box>
</template>

<script>
import ScaleBox from 'vue2-scale-box';
import {mapGetters} from 'vuex';
import RolesItem from '@/components/RolesItem';
import male from '@/assets/avatar/maleStudent.png';
import female from '@/assets/avatar/femaleStudent.png';

export default {
  name: 'lexueHome',
  data: () => {
    return {
      female,
      male,
    };
  },
  components: {
    RolesItem,
    ScaleBox,
  },
  computed: {
    ...mapGetters(['roles', 'role', 'userInfo']),
  },
  methods: {
    getDetail(url) {
      window.open(url)
    }
  },
};
</script>

<style scoped lang="scss">
a {
  color: initial;
  text-decoration: none;
}

.container {
  height: 1080px;
  width: 1920px;
  margin: 0 auto;
  background-image: url('../../assets/lexue/Mask group.png');
  background-size: 100% 100%;

  .home {
    position: absolute;
    height: 88px;
    width: 100px;
    top: 60px;
    left: 60px;
    background-image: url('../../assets/lexue/image 695.png');
    background-position: center;
    cursor: pointer;

    .text {
      height: 21px;
      margin-top: 21px;
      font-weight: 700;
      color: #391712;
      font-size: 1.5rem;
      text-align: center;
    }
  }

  .box {
    position: absolute;

    width: 1478px;
    height: 710px;
    margin-top: 102px;
    margin-left: 218px;

    display: flex;
    padding: 20px;
    box-sizing: border-box;

    .b_l {
      width: 400px;

      .huawei_logo {
        width: 40px;

        img {
          width: 100%;
        }
      }

      .userInfo {
        width: 374px;
        height: 366px;
        margin-left: 25px;
        margin-top: -20px;
        background-image: url('../../assets/lexue/Mask group_1.png');

        .person {
          height: 130px;
          padding-top: 25px;

          .info {
            padding: 45px 30px;
            cursor: pointer;
            color: #9f4111;
            display: flex;
            flex-wrap: wrap;

            .name {
              background-color: #e6cdb4;
              color: #9f4111;
              border-radius: 11px;
              font-size: 12px;
              padding: 3px 8px;
              margin-top: 7px;
            }

            img {
              width: 39px;
              height: 39px;
              border-radius: 50%;
            }
          }
        }

        .warp {
          height: 160px;
          color: #9f4111;
          display: grid;
          grid-template-rows: repeat(2, 40%);
          grid-template-columns: repeat(2, 45%);
          justify-content: center;
          justify-items: center;
          align-items: center;
          text-align: center;
          padding: 18px;

          .num {
            font-family: BebasNeue;
          }
        }
      }

      .tzgg {
        height: 266px;
        background-image: url('../../assets/lexue/Group 2469.png');
        background-repeat: no-repeat;

        .warp {
          padding: 0 30px;

          padding-top: 20px;
        }

        .title {
          font-size: 2.4rem;
          font-family: YouSheBiaoTiHei;
          font-weight: 400;
          padding: 12px;
          color: #9f4111;
        }

        .item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 10px;
          color: #9f4111;
          cursor: pointer;
          margin-bottom: 12px;

          .icon {
            height: 24px;
            width: 24px;
            display: inline-block;
            margin-right: 4px;
            background-image: url('../../assets/lexue/Group 1588.png');
          }
        }

        .page {
          margin-top: -3px;
          text-align: center;

          ::v-deep .el-pager li:not(.disabled).active {
            background-color: #9f4111 !important;
          }
        }
      }
    }

    .b_r {
      flex: 1;
      background-image: url('../../assets/lexue/Group 2470.png');
      background-repeat: no-repeat;
      background-position: top right 50px;

      .tool {
        width: 120px;
        height: 40px;
        opacity: 0.8;
        background: #ffffff;
        border: 1px solid #a1423a;
        border-radius: 21px;
        font-size: 2rem;
        font-family: YouSheBiaoTiHei;
        line-height: 40px;
        text-align: center;
        color: #b3655e;
        position: absolute;
        right: 10px;
        cursor: pointer;
      }

      .content {
        height: 580px;
        width: 900px;
        margin-left: 30px;
        margin-top: 82px;

        .warp {
          display: grid;
          grid-template-rows: 50% 40%;
          grid-template-columns: repeat(3, 33.3%);
          margin-top: 16px;
        }

        .item {
          .w_gj {
            display: flex;
            cursor: pointer;
            justify-content: center;
            align-items: center;
            width: 208px;
            height: 86px;

            margin-top: 12px;
            font-size: 2.4rem;
            font-family: OPPOSans-M;
            color: #fff;

            img {
              width: 30px;
              height: 30px;
              margin-right: 10px;
              margin-top: 5px;
            }
          }

          .w_yy {
            cursor: pointer;
            width: 169px;
            height: 40px;

            margin-top: 170px;
            color: #cb6c54;
            font-size: 2.4rem;
            font-family: OPPOSans-M;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }
  }
}
</style>
