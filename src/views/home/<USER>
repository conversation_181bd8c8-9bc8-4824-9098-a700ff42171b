<template>
  <div class="container">
    <div class="header">
      <div class="bg"></div>
      <!--      <img-->
      <!--        src="../../assets/images/image <EMAIL>"-->
      <!--        class="bg" />-->
      <div class="header-warp">
        <div class="line_one">
          <div
              class="item"
              @mouseenter="handleMouseEnter(1)"
              @mouseleave="showIndex = ''">
            <img src="../../assets/images/Group 2167.png"/>
            <div
                class="title"
                style="color: #2879ff">
              <span>优教</span>
              <span class="subhead">/Teaching</span>
            </div>
            <div
                class="shade"
                v-show="showIndex == 1">
              <div
                  class="menus"
                  style="color: #2879ff">
                <div
                    @click="toDetail(item.redirectUrl)"
                    class="btn"
                    v-for="(item, index) in apps.home_teaching"
                    :key="item.appName"
                    v-show="index < 7">
                  {{ item.appName }}
                </div>
                <div
                    class="more"
                    v-show="index > 6">
                  更多
                </div>
              </div>
            </div>
          </div>
          <div
              class="item"
              @mouseenter="handleMouseEnter(2)"
              @mouseleave="showIndex = ''">
            <img src="../../assets/images/Group 2168.png"/>
            <div
                class="title"
                style="color: #ff7801">
              <span>乐学</span>
              <span class="subhead">/Learning</span>
            </div>
            <div
                class="shade"
                v-show="showIndex == 2">
              <div
                  class="menus"
                  style="color: #ff7801">
                <div
                    @click="toDetail(item.redirectUrl)"
                    class="btn"
                    v-for="(item, index) in apps.home_learning"
                    :key="item.appName"
                    v-show="index < 7">
                  {{ item.appName }}
                </div>
                <div
                    class="more"
                    v-show="index > 6">
                  更多
                </div>
              </div>
            </div>
          </div>
          <div
              class="item"
              @mouseenter="handleMouseEnter(3)"
              @mouseleave="showIndex = ''">
            <img src="../../assets/images/Group 2169.png"/>
            <div
                class="title"
                style="color: #ff4f4e">
              <span>和家</span>
              <span class="subhead">/Family</span>
            </div>
            <div
                class="shade"
                v-show="showIndex == 3">
              <div
                  class="menus"
                  style="color: #ff4f4e">
                <div
                    @click="toDetail(item.redirectUrl)"
                    class="btn"
                    v-for="(item, index) in apps.home_family"
                    :key="item.appName"
                    v-show="index < 7">
                  {{ item.appName }}
                </div>
                <div
                    class="more"
                    v-show="index > 6">
                  更多
                </div>
              </div>
            </div>
          </div>
          <div
              class="item"
              @mouseenter="handleMouseEnter(4)"
              @mouseleave="showIndex = ''">
            <img src="../../assets/images/Group 2170.png"/>
            <div
                class="title"
                style="color: #10b27b">
              <span>学校</span>
              <span class="subhead">/School</span>
            </div>
            <div
                class="shade"
                v-show="showIndex == 4">
              <div
                  class="menus"
                  style="color: #10b27b">
                <div
                    @click="toDetail(item.redirectUrl)"
                    class="btn"
                    v-for="(item, index) in apps.home_school"
                    :key="item.appName"
                    v-show="index < 7">
                  {{ item.appName }}
                </div>
                <div
                    class="more"
                    v-show="index > 6">
                  更多
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="line_two">
          <div
              class="item"
              @click="toGuojiaJiaocai">
            <div class="title">国家资源</div>
            <img src="../../assets/images/Group 2172.png"/>
          </div>
          <div
              class="item"
              @mouseenter="handleMouseEnter(5)"
              @mouseleave="showIndex = ''"
              style="cursor: auto">
            <div class="title">智慧云资源</div>
            <img src="../../assets/images/Group 2173.png"/>
            <div
                class="shade"
                v-show="showIndex == 5">
              <div
                  class="menus"
                  style="color: #ff7801">
                <div
                    @click="toDetail(item.redirectUrl)"
                    class="btn"
                    v-for="(item, index) in apps.home_resource"
                    :key="item.appName"
                    v-show="index < 7">
                  {{ item.appName }}
                </div>
                <div
                    class="more"
                    v-show="index > 6">
                  更多
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="activity">
          <Title
              title="精彩活动"
              class="title"></Title>
          <div class="contents">
            <Activity
                v-for="i in 3"
                :cover="require('@/assets/images/image <EMAIL>')"
                title="活动名称"
                time="2022-22-24"></Activity>
          </div>
        </div>
        <div class="news">
          <Title
              title="最新资讯"
              class="title"></Title>
          <div class="box">
            <News
                v-for="item in news"
                :key="item.id"
                :data="item"></News>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getAppByspace, getNewsList, getThemes} from '@/api/home/<USER>';
import Title from '@/components/Title';
import Activity from '@/components/Activity';
import News from '@/components/News';

export default {
  name: 'HomeIndex',
  data: () => {
    return {
      index: '',
      showIndex: '', //需要显示的板块
      news: [],
      apps: [],
      themes: [],
    };
  },
  components: {
    Title,
    Activity,
    News,
  },
  async created() {
    //获取应用
    const {data: apps} = await getAppByspace('space_home');
    this.apps = apps;
    //获取新闻资讯
    const {rows: news} = await getNewsList({pageNum: 1, pageSize: 10});
    this.news = news;
    //获取活动
    const {rows: themes} = await getThemes();
    this.themes = themes;

  },
  methods: {
    handleMouseEnter(index) {
      this.showIndex = index;
    },
    //跳转应用
    toDetail(url) {
      window.open(url);
    },
    // 国家教材
    toGuojiaJiaocai() {
      window.open('https://basic.smartedu.cn/tchMaterial');
    },
  },
};
</script>

<style scoped lang="scss">
/* 首先，隐藏默认样式的滚动条 */
::-webkit-scrollbar {
  width: 4px;
  height: 0;
}

/* 然后，在滚动条的伪元素中定义样式 */
::-webkit-scrollbar-thumb {
  background-color: #3b8989; /* 滚动条的颜色 */
  border-radius: 10px; /* 滚动条的圆角 */
}

.container {
  //background-image: url('../../assets/images/Group <EMAIL>');
  .header {
    .bg {
      width: 100%;
      height: 67rem;
      object-fit: cover;
      position: relative;
      z-index: 888;
      margin-top: -60px;
    }

    .header-warp {
      //display: grid;
      // grid-template-columns: repeat(4, 285px);
      // grid-template-rows: 1fr 1fr;
      //justify-content: space-between;
      width: 1200px;
      z-index: 999;
      position: absolute;
      top: 110px;
      left: 50%;
      transform: translateX(-50%);

      .line_one {
        display: flex;
        justify-content: space-between;
        height: 360px;

        img {
          width: 285px;
        }

        .title {
          position: absolute;
          top: 33px;
          font-weight: 700;
          font-size: 3rem;
          display: flex;
          align-items: center;

          span:nth-child(1) {
            margin-left: 28px;
          }

          .subhead {
            font-size: 1.5rem;
          }
        }

        .title::before {
          content: '';
          border-left: 8px solid;
          height: 32px;
          border-radius: 0px 4px 4px 0px;
        }

        .shade {
          position: absolute;
          border-radius: 10px;
          top: 0px;

          width: 100%;
          height: 100%;
          background-color: rgba(255, 255, 255, 0.6);

          .menus {
            width: 75%;
            height: 200px;
            margin: 90px auto;
            display: grid;
            grid-template-columns: repeat(2, 100px);
            grid-auto-rows: 48px;
            align-items: center;
            justify-content: space-between;

            .btn {
              border: 1px solid;
              height: 34px;
              border-radius: 73px;
              justify-content: center;
              align-items: center;
              display: flex;
              background-color: rgba(255, 255, 255, 0.9);
              font-size: 1.6rem;
              font-weight: 500;
              cursor: pointer;
            }

            .more {
              height: 34px;
              border-radius: 73px;
              justify-content: center;
              align-items: center;
              display: flex;
              font-size: 1.6rem;
              font-weight: 500;
              cursor: pointer;
            }

            .more::after {
              content: '>';
              border: 1px solid;
              height: 14px;
              width: 14px;
              border-radius: 10px;
              display: flex;
              align-items: center;
              font-size: 1rem;
              line-height: 1.4rem;
              justify-content: center;
              margin-left: 5px;
            }
          }
        }
      }

      .line_two {
        margin-top: 30px;
        display: flex;
        justify-content: space-between;
        height: 120px;

        .title {
          position: absolute;
          top: 50%;
          color: #fff;
          font-size: 3rem;
          margin-left: 32px;
          transform: translateY(-50%);
        }

        .item {
          cursor: pointer;
        }

        .shade {
          position: absolute;
          border-radius: 10px;
          top: 0px;

          width: 100%;
          height: 100%;
          background-color: rgba(255, 255, 255, 0.6);

          .menus {
            width: 60%;
            margin-left: 33%;
            height: 100%;
            display: grid;
            grid-template-columns: repeat(3, 100px);
            grid-auto-rows: 58px;
            align-items: center;
            justify-content: space-between;

            .btn {
              border: 1px solid;
              height: 34px;
              border-radius: 73px;
              justify-content: center;
              align-items: center;
              display: flex;
              background-color: rgba(255, 255, 255, 0.9);
              font-size: 1.6rem;
              font-weight: 500;
              cursor: pointer;
            }

            .more {
              height: 34px;
              border-radius: 73px;
              justify-content: center;
              align-items: center;
              display: flex;
              font-size: 1.6rem;
              font-weight: 500;
              cursor: pointer;
            }

            .more::after {
              content: '>';
              border: 1px solid;
              height: 14px;
              width: 14px;
              border-radius: 10px;
              display: flex;
              align-items: center;
              font-size: 1rem;
              line-height: 1.4rem;
              justify-content: center;
              margin-left: 5px;
            }
          }
        }
      }

      .item {
        position: relative;
        // cursor: pointer;
      }
    }

    .content {
      width: 1200px;
      margin: 0 auto;
      padding-bottom: 30px;
      display: flex;
      justify-content: space-between;

      .activity {
        flex: 0.98;

        .contents {
          display: grid;
          grid-template-columns: repeat(3, 285px);
          justify-content: space-between;
        }
      }

      .news {
        width: 285px;

        .box {
          background-color: #fff;
          height: 244px;
          padding: 12px 0;
          border-radius: 10px;
          overflow-x: hidden;
          overflow-y: scroll;
        }
      }

      .title {
        margin: 32px 0 22px 0;
      }
    }
  }
}
</style>
