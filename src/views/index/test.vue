<template>
  <div>
    <div class="toolbar">
      <el-upload
        :before-upload="uploadSuccess"
        :action="uploadUrl">
        <el-button type="primary">点击上传</el-button>
      </el-upload>
      <img
        v-for="(item, index) in pics"
        :key="index"
        :src="item"
        v-drag
        @click="selected" />
      <!-- <img
        src="@/assets/images/ttt.png"
        @click="selected"
        v-drag />
      <img
        src="@/assets/images/ttt2.png"
        @click="selected"
        v-drag /> -->
    </div>
    <div ref="box">
      <div
        class="poster"
        ref="poster"
        :style="cssVars">
        <div
          v-drag
          style="
            border: 1px #000 solid;
            width: 80%;
            cursor: pointer;
            position: absolute;
          ">
          <p v-html="htmlStr"></p>
        </div>
        <img
          :src="img2"
          v-drag />
      </div>
    </div>
    <button @click="draw">生成</button>
  </div>
</template>
<style lang="scss" scoped>
.toolbar {
  border: 1px #000 solid;
  height: 100px;
  display: flex;
  justify-items: center;

  img {
    border: 1px #ccc solid;
    margin: 5px;
    width: 100px;
    cursor: pointer;
  }
}

.demo {
  width: 100%;
}

.poster {
  width: 860px;
  height: 600px;
  border: 2px #000 solid;
  margin: 0 auto;
  position: relative;
  background: var(--main-bg);
  background-size: 100% 100%;

  img {
    position: absolute;
    max-width: 100px;
    cursor: pointer;
  }
}

.test {
  position: absolute;
  left: 0;
  top: 0;
  cursor: pointer;
  width: 100px;
  height: 100px;
}
</style>
<script>
import html2canvas from 'html2canvas';
export default {
  name: 'TestHome',
  directives: {
    drag(el, binding, vnode) {
      console.log(el);
      let oDiv = el; // 当前元素 // 获得当前this
      let _this = vnode.context; // 禁止选择网页上的文字
      document.onselectstart = function () {
        return false;
      };
      oDiv.onmousedown = function (e) {
        console.log(oDiv);
        // 鼠标按下，计算当前元素距离可视区的距离
        let disX = e.clientX - oDiv.offsetLeft;
        let disY = e.clientY - oDiv.offsetTop;
        document.onmousemove = function (e) {
          console.log(e);
          let left = e.clientX - disX;
          let top = e.clientY - disY;
          let { offsetHeight: pondModelHeight, offsetWidth: pondModelWidth } =
            _this.$refs.poster;
          let { offsetHeight: sonNodeHeight, offsetWidth: sonNodeWidth } = oDiv; // 上下移动的判断
          if (top < sonNodeHeight / 2) {
            top = sonNodeHeight / 2;
          }
          if (top > pondModelHeight - sonNodeHeight / 2) {
            top = pondModelHeight - sonNodeHeight / 2;
          } // 左右移动的判断
          if (left < 0) {
            left = 0;
          }
          if (left > pondModelWidth - sonNodeWidth) {
            left = pondModelWidth - sonNodeWidth;
          } // 移动当前元素
          oDiv.style.left = left + 'px';
          oDiv.style.top = top + 'px';
          oDiv.style.zIndex = 999;
        };
        document.onmouseup = function (e) {
          document.onmousemove = null;
          document.onmouseup = null;
          oDiv.style.zIndex = 1;
        }; // return false不加的话可能导致黏连，就是拖到一个地方时div粘在鼠标上不下来，相当于onmouseup失效
        return false;
      };
    },
  },
  data() {
    return {
      pics: [], //上传图片集合
      img1: require('@/assets/images/view.jpg'),
      img2: require('@/assets/images/newWhcode2.png'),
      imgUrl: '',
      uploadUrl: '',
      htmlStr:
        '<p style="text-indent: 2em">张三同学，祝贺您取得第一名成绩<p><br/>特发此状以资鼓励。',
    };
  },
  computed: {
    cssVars() {
      return {
        '--main-bg': `url(${this.img1})`,
      };
    },
  },
  methods: {
    //选中组件
    selected(e) {
      this.$refs.poster.append(e.currentTarget);
      console.log(e.currentTarget);
    },
    imgToBase64(file) {
      return new Promise((resolve, reject) => {
        var reader = new FileReader();
        reader.onload = function (e) {
          resolve(e.target.result);
        };
        reader.readAsDataURL(file); // 读取完后会调用onload方法
      });
    },
    //图片上传
    uploadSuccess(e) {
      this.imgToBase64(e).then((res) => {
        this.pics.push(res);
      });
    },
    draw() {
      var that = this;
      html2canvas(that.$refs.poster, {
        useCORS: true, //这个为true代表允许跨域
        allowTaint: false,
        logging: false,
        letterRendering: true,
      }).then(function (canvas) {
        that.imgUrl = canvas.toDataURL('image/png'); //将canvas转为base64图片(eg. data:image/png;base64,ijskjlkj)
        let a = document.createElement('a'); // 生成一个a元素
        let event = new MouseEvent('click'); // 创建一个单击事件
        a.download = 'photo'; // 设置图片名称
        a.href = that.imgUrl; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      });
    },
    move() {
      // 获取鼠标在div上按下的位置
      var e = window.event; //接收事件对象
      // 获取鼠标在当前事件源的位置
      var x1 = e.offsetX;
      var y1 = e.offsetY;
      this.$refs.test;
      console.log(x1);
    },
  },
  mounted() {
    //this.draw();
  },
};
</script>
