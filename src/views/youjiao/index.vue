<!-- 年级班级设置 -->
<template>
  <scale-box
      :width="1920"
      :height="1080"
      bgc="transparent"
      :delay="100">
    <div class="container">
      <div class="header">
        <div
            class="home"
        >
          <img src="../../assets/youjiao/Group 2466.png"/>
          <div class="text" @click="$router.push('/')">
            公共
            <br/>
            空间
          </div>
<!--          <img src="../../assets/huawei_logo.png">-->
        </div>

        <div class="roles">
          <RolesItem color="#a1423a"></RolesItem>
        </div>
      </div>
      <div class="box">
        <div class="b_l">
          <div class="l_t">
            <div class="userInfo flex">
              <div @click="$router.push('/personal')">
                <img
                    :src="
                    userInfo.headIcon
                      ? userInfo.headIcon
                      : userInfo.sex == 0
                      ? male
                      : female
                  "/>
              </div>
              <div
                  class="m_l_10"
                  @click="$router.push('/personal')">
                <span class="fs_22">{{ name }}老师</span>
                <div class="name">{{ role.full_name }}</div>
              </div>
              <div class="box">
                <div class="flex aligin_center flex_column">
                  <div class="fs_34 num">5</div>
                  <div class="fs_18">任教班级数</div>
                </div>
                <div class="flex aligin_center flex_column">
                  <div class="fs_34 num">244</div>
                  <div class="fs_18">我的学生数</div>
                </div>
                <div class="flex aligin_center flex_column">
                  <div class="fs_34 num">5</div>
                  <div class="fs_18">获奖次数</div>
                </div>
                <div class="flex aligin_center flex_column">
                  <div class="fs_34 num">15</div>
                  <div class="fs_18">精彩活动数</div>
                </div>
              </div>
            </div>
          </div>
          <div class="l_b">
            <div class="box">
              <div @click="$router.push('/setting')">班级助手</div>
              <div
                  @click="getDetail('https://www.wuhousmartedu.com/#/resource/myCenter?scene=noSearch&ticket=ST-39402-0QsvoiiwXi5TG1MKhzwX-localhost')">
                我的网盘
              </div>
              <div @click="getDetail('https://www.wuhousmartedu.com/#/resource/region')">资源中心</div>
            </div>
          </div>
        </div>
        <div class="b_r">
          <div class="r_t">
            <div class="r_l">
              <div class="blackboard">
                <div
                    class="title"
                    style="margin-left: 333px">
                  我的教学区
                </div>
                <div
                    class="title"
                    style="margin-left: 414px">
                  我的工具箱
                </div>

                <div class="box_l">
                  <div class="more">
                    <button
                        class="kj_btn c_11471D"
                        @click="
                        $router.push({
                          path: '/tools',
                          query: { name: '我的教学区' },
                        })
                      ">
                      更多应用
                    </button>
                  </div>
                  <div>
                    <div>
                      <div class="sub_title">
                        <span class="fs_24">备课</span>
                        <span class="fs_16 m_l_10">Preparation</span>
                      </div>
                      <div class="items">
                        <div class="item">专题课件（101教育PPT）</div>
                        <div class="item">资源搜索</div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div>
                      <div class="sub_title">
                        <span class="fs_24">授课</span>
                        <span class="fs_16 m_l_10">Teaching</span>
                      </div>
                      <div class="items">
                        <div class="item">授课工具（101教育PPT）</div>
                        <div @click="getDetail('')" class="item">智慧课堂（畅言）</div>
                        <div @click="getDetail('https://iuppbl.wuhousmartedu.com/index.html?login=success#/home')"
                             class="item">项目式学习
                        </div>
                        <div @click="getDetail('https://study.wuhousmartedu.com/index.html?login=success#/home')"
                             class="item">探究式学习
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div>
                      <div class="sub_title">
                        <span class="fs_24">课后</span>
                        <span class="fs_16 m_l_10">After class</span>
                      </div>
                      <div class="items">
                        <div @click="getDetail('https://www.cnki.net/')" class="item">教学研究（知网）</div>
                        <div @click="getDetail('https://www.zhixue.com/htm-unionreport/#/unionReport/unionReportList')"
                             class="item">精准教学（智学网）
                        </div>
                        <div
                            @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/wisdomTask/expandListTeacher')"
                            class="item">学习反馈
                        </div>
                        <div
                            @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/punchCard/cardListTeacher')"
                            class="item">习惯清单
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="box_r">
                  <div class="items">
                    <div
                        @click="getDetail('https://osscdn.wuhousmartedu.com/web/tools/15-Acrobat_DC-2018-WIN-%E5%AE%89%E8%A3%85%E5%8C%85.rar?auth_key=1700578358-0-0-662437ab50ea7374d70b4b439d6898d2&response-content-type=application/octet-stream')"
                        class="item">PDF编辑器
                    </div>
                    <div @click="getDetail('https://pixabay.com/zh/')" class="item">高清图库</div>
                    <div
                        @click="getDetail('https://osscdn.wuhousmartedu.com/web/tools/oCam_v520.0.rar?auth_key=1700557316-0-0-21ef580675717391f6e7e40be2245c58&response-content-type=application/octet-stream')"
                        class="item">录屏工具
                    </div>
                    <div @click="getDetail('https://www.voidtools.com/zh-cn/')" class="item">everything</div>
                    <div @click="getDetail('https://www.zhixue.com/htm-unionreport/#/unionReport/unionReportList')"
                         class="item">继续教育平台
                    </div>
                    <div class="item">
                      <button
                          class="kj_btn c_11471D"
                          @click="
                          $router.push({
                            path: '/tools',
                            query: { name: '我的工具箱' },
                          })
                        ">
                        更多工具
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="r_r">
              <div class="tzgg">
                <div class="title">通知公告</div>
                <div class="contant">
                  <div
                      class="item"
                      v-for="i in 4">
                    <div class="fs_15 flex aligin_center">
                      <i class="icon"></i>
                      通知名称
                    </div>
                    <div class="opacity_6 fs_12">2023-02-02</div>
                  </div>
                  <div class="page">
                    <el-pagination
                        :total="90"
                        :page-size="4"
                        :pager-count="5"
                        background
                        :hide-on-single-page="true"
                        layout=" pager"></el-pagination>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="r_b">
            <div class="bgyy">
              <div class="title">办公应用</div>
              <div class="box">
                <button @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/notice/office_list')"
                        class="kj_btn c_fff">通知公告
                </button>
<!--                'https://whqjyy-school.wuhousmartedu.com/index.html#/officCirculation/manage'-->
                <button
                    @click="$router.push('/oa')"
                    class="kj_btn c_fff">OA收发
                </button>
                <button @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/infoCollect/fillTask')"
                        class="kj_btn c_fff">信息填报
                </button>
                <button
                    @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/quest/fillTask?module=question')"
                    class="kj_btn c_fff">极简问卷
                </button>
                <button
                    class="kj_btn c_fff"
                    @click="teacherPortrait">
                  教师画像
                </button>
                <button
                    class="kj_btn c_fff"
                    @click="
                    $router.push({
                      path: '/tools',
                      query: { name: '办公应用' },
                    })
                  ">
                  更多
                </button>
              </div>
            </div>
            <!-- <div
              class="zdygj"
              @click="$router.push('/setting')">
              班级
              <br />
              管理
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </scale-box>
</template>

<script>
import ScaleBox from 'vue2-scale-box';
import {mapGetters} from 'vuex';
import RolesItem from '@/components/RolesItem';
import male from '@/assets/avatar/maleTeacher.png';
import female from '@/assets/avatar/femaleTeacher.png';
import axios from 'axios';
import {getToken} from '@/api/app';

export default {
  name: 'youjiaoHome',
  data: () => {
    return {
      female,
      male,
    };
  },
  components: {
    RolesItem,
    ScaleBox,
  },
  computed: {
    ...mapGetters(['roles', 'role', 'userInfo', 'name']),
  },
  methods: {
    getDetail(url) {
      window.open(url);
    },
    //教师画像跳转
    async teacherPortrait() {
      //本地测试代码
      // const { data } = await getToken({ phone: this.userInfo.phone });
      // window.open(
      //   ' http://wh.21spt.com:8000/teacherlogin.aspx?BaseToken=' +
      //     data.BaseToken
      // );
      //线上打包代码
      console.log(url.replace('/api', '') + '/aouth/sys/portalLogin');
      axios({
        method: 'post',
        url: url.replace('/api', '') + '/aouth/sys/portalLogin',
        headers: {
          'Content-type': 'application/x-www-form-urlencoded',
        },
        params: {
          phone: this.userInfo.phone,
        },
      })
          .then((response) => {
            window.open(
                ' http://wh.21spt.com:8000/teacherlogin.aspx?BaseToken=' +
                response.data.data.BaseToken
            );
          })
          .catch((error) => {
            console.log(error);
          });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  // max-width: 1920px;
  // min-width: 1900px;
  // min-height: 1080px;
  // height: 100vh;
  height: 1080px;
  width: 1920px;
  margin: 0 auto;
  background-image: url('../../assets/youjiao/Mask <EMAIL>');
  background-size: 100% 100%;

  .header {
    height: 100px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 54px;

    .home {
      position: relative;

      div {
        cursor: pointer;
      }

      img {
        width: 75px;
        height: 75px;
        margin-right: 20px;
      }

      .text {
        position: absolute;
        top: 15px;
        left: 23px;
        color: #0f4444;
        font-size: 1.5rem;
        font-weight: 700;
      }
    }
  }

  .box {
    display: flex;
    height: calc(100% - 100px);

    .b_l {
      width: 380px;
      display: flex;
      flex-direction: column;

      .l_t {
        flex: 0.6;
        background-image: url('../../assets/youjiao/Group.png');
        background-repeat: no-repeat;
        background-position: top center;

        .userInfo {
          padding: 50px;
          cursor: pointer;
          color: #fff;
          display: flex;
          flex-wrap: wrap;

          .name {
            background-color: #ac6745;
            border-radius: 11px;
            font-size: 12px;
            padding: 3px 8px;
            margin-top: 7px;
          }

          .box {
            height: 154px;
            width: 100%;
            cursor: default;
            margin-top: 30px;
            background-color: #7e3510;
            display: grid;
            grid-template-rows: repeat(2, 50%);
            grid-template-columns: repeat(2, 50%);
            justify-content: center;
            justify-items: center;
            align-items: center;

            .num {
              font-family: BebasNeue;
            }
          }

          img {
            width: 39px;
            height: 39px;
            border-radius: 50%;
          }
        }
      }

      .l_b {
        flex: 1;
        background-image: url('../../assets/youjiao/Group 2463.png');
        background-repeat: no-repeat;
        background-position: bottom 88px center;

        .box {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-shadow: 2px 2px 1px rgba(255, 255, 255, 1);

          > div {
            flex: 1;
            display: flex;
            align-items: center;
            border: 10px transparent solid;
            cursor: pointer;
          }

          font-size: 2.8rem;
          font-family: YouSheBiaoTiHei !important;
          font-weight: 400;
          color: #532a04;
          width: 226px;
          margin-left: 120px;
          height: 280px;
          margin-top: 230px;
        }
      }
    }

    .b_r {
      .r_t {
        width: 100%;
        display: flex;
        height: 433px;

        .r_l {
          width: 1160px;
          background-image: url('../../assets/youjiao/Group 2465.png');
          background-repeat: no-repeat;
          background-position: top center;

          .blackboard {
            height: 100%;

            .title {
              font-family: STHupo;
              font-size: 2.4rem;
              color: #fec745;
              text-shadow: 2px 2px 1px rgba(107, 138, 97, 1);
              margin-top: 53px;
              display: inline-block;
              letter-spacing: 0.1em;
            }

            .box_l {
              width: 656px;
              margin-left: 70px;
              flex-direction: column;

              .more {
                position: absolute;
                right: 0;
                top: 10px;
              }

              > div {
                flex: 1;
                display: flex;
                padding: 0 20px;
                align-items: center;
              }

              .sub_title {
                color: #fec745;
              }

              .items {
                font-size: 1.8rem;
                color: #fff;
                font-family: OPPOSans-M;
                display: flex;

                .item {
                  margin-right: 15px;
                  cursor: pointer;
                }
              }
            }

            .box_r {
              width: 266px;
              margin-left: 810px;

              .items {
                font-size: 1.8rem;
                color: #fff;
                font-weight: 400;
                display: flex;
                width: 100%;
                font-family: OPPOSans-M;
                flex-direction: column;
                justify-content: center;

                .item {
                  width: 100%;
                  cursor: pointer;
                  text-align: center;
                  margin-bottom: 10px;
                }
              }
            }

            .box_l,
            .box_r {
              height: 260px;
              margin-top: 34px;
              position: absolute;
              display: flex;

              font-family: YouSheBiaoTiHei;
            }
          }
        }

        .r_r {
          flex: 1;
          background-image: url('../../assets/youjiao/Group 2464.png');
          background-repeat: no-repeat;
          background-position: top center;

          .tzgg {
            width: 292px;
            height: 248px;
            margin: 100px auto;

            .title {
              font-size: 2.4rem;
              font-family: YouSheBiaoTiHei;
              font-weight: 400;
              padding: 12px;
              color: #0f4444;
            }

            .item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 0 10px;
              color: #0f4444;
              cursor: pointer;
              margin-bottom: 15px;

              .icon {
                height: 24px;
                width: 24px;
                display: inline-block;
                margin-right: 4px;
                background-image: url('../../assets/youjiao/Group 1588.png');
              }
            }

            .page {
              margin-top: -3px;
              text-align: center;

              ::v-deep .el-pager li:not(.disabled).active {
                background-color: #3b8989 !important;
              }
            }
          }
        }
      }

      width: calc(100% - 380px);

      .r_b {
        width: 100%;
        height: calc(100% - 433px);
        background-image: url('../../assets/youjiao/Group 2462.png');
        background-repeat: no-repeat;
        background-position: bottom 13px right;
        position: relative;

        .bgyy {
          position: absolute;
          height: 200px;
          width: 430px;
          right: 232px;
          top: 32px;
          font-family: YouSheBiaoTiHei;
          color: #fff;

          .title {
            text-align: center;
            font-size: 2.6rem;

            line-height: 47px;
          }

          .box {
            height: 130px;
            margin-top: 10px;
            display: grid;
            grid-template-rows: repeat(2, 50%);
            grid-template-columns: repeat(3, 33.3%);
            justify-content: center;
            align-items: center;

            button {
              background: rgba(255, 255, 255, 0.1) !important;
              border: 1px #7fadd2 solid !important;
              width: 100px;
              margin: 0 auto;
              font-size: 18px !important;
            }
          }
        }

        .zdygj {
          position: absolute;
          height: 70px;
          width: 100px;
          right: 100px;
          top: 220px;
          cursor: pointer;
          font-family: YouSheBiaoTiHei;
          color: #63564e;

          font-size: 2rem;
          display: flex;
          justify-content: center;
          align-items: center;
          text-align: center;
        }
      }
    }
  }
}
</style>
