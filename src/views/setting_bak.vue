<template>
  <div class="schoolContainer">
    <div
      v-if="activeName == 0"
      class="p_20">
      <el-empty description="该角色暂无操作功能"></el-empty>
    </div>
    <div v-else>
      <el-tabs v-model="activeName">
        <el-tab-pane
                v-if="portal_student"
                label="学生管理"
                name="1">
          <StudentSeeting v-if="activeName == 1"></StudentSeeting>
        </el-tab-pane>
        <el-tab-pane
          v-if="portal_teacher"
          label="老师管理"
          name="2">
          <TeacherSeeting v-if="activeName == 2"></TeacherSeeting>
        </el-tab-pane>
        <el-tab-pane
                v-if="portal_grade"
                label="年级班级管理"
                name="3">
          <ClassSeeting v-if="activeName == 3"></ClassSeeting>
        </el-tab-pane>
        <el-tab-pane
          v-if="portal_organization"
          label="机构管理"
          name="4">
          <OrganizationSeeting v-if="activeName == 4"></OrganizationSeeting>
        </el-tab-pane>
        <el-tab-pane
          v-if="portal_institutional"
          label="部门管理"
          name="5">
          <InstitutionalSeeting v-if="activeName == 5"></InstitutionalSeeting>
        </el-tab-pane>
        <el-tab-pane
          v-if="portal_teacourse"
          label="老师任教学校"
          name="6">
          <TeacherSchool v-if="activeName == 6"></TeacherSchool>
        </el-tab-pane>
        <el-tab-pane
          v-if="portal_course"
          label="学科管理"
          name="7">
          <SubjectSeeting v-if="activeName == 7"></SubjectSeeting>
        </el-tab-pane>
        <el-tab-pane
          v-if="portal_class"
          label="班级管理"
          name="8">
          <TeacherClassSeeting v-if="activeName == 8"></TeacherClassSeeting>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import ClassSeeting from '@/components/TabPage/ClassSeeting';
import TeacherSeeting from '@/components/TabPage/TeacherSeeting';
import StudentSeeting from '@/components/TabPage/StudentSeeting';
import OrganizationSeeting from '@/components/TabPage/OrganizationSeeting';
import InstitutionalSeeting from '@/components/TabPage/InstitutionalSeeting';
import TeacherSchool from '@/components/TabPage/TeacherSchool';
import SubjectSeeting from '@/components/TabPage/SubjectSeeting';
import TeacherClassSeeting from '@/components/TabPage/TeacherClassSeeting';
export default {
  name: 'SchoolPersonal',
  components: {
    ClassSeeting,
    TeacherSeeting,
    StudentSeeting,
    OrganizationSeeting,
    InstitutionalSeeting,
    TeacherSchool,
    SubjectSeeting,
    TeacherClassSeeting,
  },
  data: () => {
    return {
      activeName: '0',
      portal_grade: false,
      portal_teacher: false,
      portal_student: false,
      portal_organization: false,
      portal_institutional: false,
      portal_teacourse: false,
      portal_course: false,
      portal_class: false,
    };
  },
  computed: {
    ...mapGetters(['menus']),
  },
  watch: {
    menus: {
      immediate: true,
      handler(menus) {
        this.portal_grade = false;
        this.portal_teacher = false;
        this.portal_student = false;
        this.portal_organization = false;
        this.portal_institutional = false;
        this.portal_teacourse = false;
        this.portal_course = false;
        this.portal_class = false;
        this.activeName = 0;
        let act = '0';
        menus.forEach((item) => {

          if (item === 'portal:student') {
            this.portal_student = true;
            if (act == '0' || act > '1') {
              act = '1';
            }
          }
          if (item === 'portal:teacher') {
            this.portal_teacher = true;
            if (act == '0' || act > '2') {
              act = '2';
            }
          }
          if (item === 'portal:grade') {
            this.portal_grade = true;
            if (act == '0' || act > '3') {
              act = '3';
            }
          }
          if (item === 'portal:organization') {
            this.portal_organization = true;
            if (act == '0' || act > '4') {
              act = '4';
            }
          }
          if (item === 'portal:institutional') {
            this.portal_institutional = true;
            if (act == '0' || act > '5') {
              act = '5';
            }
          }
          if (item === 'portal:teacourse') {
            this.portal_teacourse = true;
            if (act == '0' || act > '6') {
              act = '6';
            }
          }
          if (item === 'portal:course') {
            this.portal_course = true;
            if (act == '0' || act > '7') {
              act = '7';
            }
          }
          if (item === 'portal:class') {
            this.portal_class = true;
            if (act == '0') {
              act = '8';
            }
          }
        });

        this.$nextTick(() => {
          this.activeName = act;
        });
      },
    },
  },
  created() {},
  methods: {},
};
</script>

<style lang="scss" scoped></style>
