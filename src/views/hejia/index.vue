<!-- 年级班级设置 -->
<template>
  <scale-box
      :width="1920"
      :height="1080"
      bgc="transparent"
      :delay="100">
    <div class="container">
      <div class="header">
        <div>
          <div
              class="home"
              @click="$router.push('/')" style="display: inline-block">
            公共空间
          </div>
          <!--          <img src="../../assets/huawei_logo.png" width="30" height="30" style="display: inline-block;margin-left: 20px;">-->
        </div>

        <div class="roles">
          <RolesItem color="#4A4A64"></RolesItem>
        </div>
      </div>

      <div class="info_box">
        <div
            class="name"
            @click="$router.push('/personal')">
          <div>
            <img
                :src="
                userInfo.headIcon
                  ? userInfo.headIcon
                  : userInfo.sex == 0
                  ? male
                  : female
              "/>
          </div>
          <div>{{ role.child_name }}{{ role.role_name }}</div>
        </div>
        <div class="datas">
          <div class="title title2">学生数据</div>
          <div class="warp">
            <div class="item">
              <div class="num">4</div>
              <div class="title2 fs_15">获奖次数</div>
            </div>
            <div class="item">
              <div class="num">30</div>
              <div class="title2 fs_15">学生红花</div>
            </div>
            <div class="item">
              <div class="num">4</div>
              <div class="title2 fs_15">当日运动时长</div>
            </div>
            <div class="item">
              <div class="num">10</div>
              <div class="title2 fs_15">上传信息次数</div>
            </div>
          </div>
        </div>
      </div>
      <div class="box">
        <div
            @click="getDetail('https://www.wuhousmartedu.com/#/resource/myCenter?scene=noSearch&ticket=ST-39402-0QsvoiiwXi5TG1MKhzwX-localhost')"
            class="wp">我的网盘
        </div>
        <div class="jxht">
          <div class="title fs_24">家校互通</div>
          <div class="pic">
            <img
                src="../../assets/hejia/Snipaste_2023-08-08_16-17-15.png"
                width="100%"/>
          </div>
          <div class="warp">
            <div @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/punchCard/cardListStudent')"
                 class="item title fs_20">习惯清单
            </div>
            <div @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/wisdomTask/expandListStudent')"
                 class="item title fs_20">学习反馈
            </div>
            <div
                @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/quest/fillTask?module=question')"
                class="item title fs_20">极简问卷
            </div>
            <div @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/infoCollect/fillTask')"
                 class="item title fs_20">信息填报
            </div>
            <div class="item title fs_20">红花榜</div>
            <div @click="getDetail('https://whqjyy-school.wuhousmartedu.com/index.html#/stuleave/addLeave')"
                 class="item title fs_20">学生请假
            </div>
            <div class="item title fs_20">师生互动</div>
            <div
                class="item title fs_20"
                @click="
                $router.push({
                  path: '/tools',
                  query: { name: '家校互通' },
                })
              ">
              更多
            </div>
          </div>
        </div>
        <div class="xxx">
          <div class="title">家校共育</div>
          <div class="warp">
            <div class="item">

              <div class="list">
                <div class="icon">
                  <img src="../../assets/hejia/Group 1774.png"/>
                </div>
                <div class="text">实践信息采集</div>
              </div>

            </div>
            <div class="item">
              <a @click="getDetail('https://whqjyy.wuhousmartedu.com/staticOut/#/honor/honorQuiry')">
                <div class="list">
                  <div class="icon">
                    <img src="../../assets/hejia/Group 1771.png"/>
                  </div>
                  <div class="text">获奖查询</div>
                </div>
              </a>
            </div>
            <div class="item">
              <a @click="getDetail('https://hdds.wuhousmartedu.com/index.html')">
                <div class="list">
                  <div class="icon">
                    <img src="../../assets/hejia/Group 1772.png"/>
                  </div>
                  <div class="text">精彩活动</div>
                </div>
              </a>
            </div>
            <div class="item">
              <div class="list">
                <div class="icon">
                  <img src="../../assets/hejia/Group 2447.png"/>
                </div>
                <div class="text">成长档案</div>
              </div>
            </div>
            <div class="item">

              <div class="list">
                <div class="icon">
                  <img src="../../assets/hejia/Group 2446.png"/>
                </div>
                <div class="text">智慧体育</div>
              </div>
            </div>
            <div class="item">
              <div
                  class="list"
                  @click="
                  $router.push({
                    path: '/tools',
                    query: { name: '家校共育' },
                  })
                ">
                <div class="icon">
                  <img src="../../assets/hejia/Group 2450.png"/>
                </div>
                <div class="text">更多</div>
              </div>
            </div>
          </div>
        </div>
        <div class="zyzx">
          <div class="title fs_24">资源中心</div>
          <div class="warp">
            <div @click="getDetail('https://cdwh.wuhousmartedu.com/communityCollege.html#/')" class="item fs_20">
              家长学校
            </div>
            <div @click="getDetail('https://basic.smartedu.cn/tchMaterial')" class="item fs_20">国家资源</div>
            <div @click="getDetail('https://www.wuhousmartedu.com/#/resource/region')" class="item fs_20">智慧云资源
            </div>
          </div>
        </div>
      </div>
      <div class="tzgg">
        <div class="warp">
          <div class="title">通知公告</div>
          <div class="contant">
            <div
                class="item"
                v-for="i in 4">
              <div class="fs_15 flex aligin_center">
                <i class="icon"></i>
                通知名称
              </div>
              <div class="opacity_6 fs_12">2023-02-02</div>
            </div>
            <div class="page">
              <el-pagination
                  :total="90"
                  :page-size="4"
                  :pager-count="4"
                  background
                  :hide-on-single-page="true"
                  layout=" pager"></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </scale-box>
</template>

<script>
import ScaleBox from 'vue2-scale-box';
import {mapGetters} from 'vuex';
import RolesItem from '@/components/RolesItem';
import male from '@/assets/avatar/maleParents.png';
import female from '@/assets/avatar/femaleParents.png';

export default {
  name: 'hejiaHome',
  data: () => {
    return {
      female,
      male,
    };
  },
  components: {
    RolesItem,
    ScaleBox,
  },
  computed: {
    ...mapGetters(['roles', 'role', 'userInfo']),
  },
  methods: {
    getDetail(url) {
      window.open(url);
    }
  },
};
</script>

<style scoped lang="scss">
.title2 {
  font-family: OPPOSans-L;
}

.container {
  height: 1080px;
  width: 1920px;
  margin: 0 auto;
  background-image: url('../../assets/hejia/Mask group.png');
  background-size: 100% 100%;

  .header {
    position: absolute;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 94%;
    margin-left: 3%;

    top: 14px;

    .home {
      background-color: #ebecf4;
      border: 1px #263b67 solid;
      width: 84px;
      border-radius: 16px;
      font-size: 1.5rem;
      font-weight: 700;
      color: #263b67;
      text-align: center;
      line-height: 28px;

      cursor: pointer;
    }
  }

  .info_box {
    position: absolute;
    height: 322px;
    width: 300px;
    margin-left: 70px;
    margin-top: 230px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 24px;

    .name {
      cursor: pointer;
      height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 2.2rem;
      color: #fff;

      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
      }
    }

    .datas {
      flex: 1;
      background: #95b9f3;
      color: #fff;
      border-radius: 10px;
      display: flex;
      flex-direction: column;

      .num {
        font-size: 3.4rem;
        font-family: BEBAS;
      }

      .title {
        text-align: center;
        font-size: 2.2rem;
        font-weight: 700;
        line-height: 50px;
      }

      .warp {
        height: 100%;
        padding-bottom: 35px;
        display: grid;
        grid-template-rows: repeat(2, 50%);
        grid-template-columns: repeat(2, 50%);

        .item {
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
        }
      }
    }
  }

  .box {
    .title {
      font-family: YouSheBiaoTiHei;
      color: #000;
    }

    width: 1100px;
    height: 530px;
    position: absolute;
    top: 380px;
    left: 400px;

    .wp {
      width: 98px;
      height: 45px;
      position: absolute;
      top: 28px;
      font-family: YouSheBiaoTiHei;
      font-size: 2rem;
      color: #fff;
      text-align: center;
      line-height: 45px;
      right: 126px;
      cursor: pointer;
      text-shadow: 2px 2px 1px rgba(186, 66, 42, 1);
    }

    display: flex;

    .jxht {
      width: 304px;
      height: 456px;
      margin-top: 55px;
      margin-left: 15px;
      padding: 13px;
      box-sizing: border-box;

      .pic {
        margin-top: 10px;
        height: 130px;

        img {
          height: 100%;
          object-fit: cover;
        }
      }

      .warp {
        display: grid;
        grid-template-columns: repeat(2, 50%);
        grid-template-rows: repeat(4, 34%);
        margin-top: 20px;
        padding: 0 8px;

        .item {
          border: 1px solid #434766;
          border-radius: 2px;
          width: 100px;
          margin: 0 auto;
          cursor: pointer;
          height: 40px;
          text-align: center;
          line-height: 40px;
          background: rgba(255, 255, 255, 0.2);
          color: #5d5e6a;
          font-family: OPPOSans-M;
          font-size: 2rem;
        }
      }
    }

    .xxx {
      width: 385px;
      height: 456px;
      margin-top: 55px;
      margin-left: 55px;
      display: flex;
      flex-direction: column;

      .title {
        height: 70px;
        text-align: center;
        line-height: 80px;
        font-size: 2.4rem;
      }

      .warp {
        flex: 1;

        display: grid;
        grid-template-rows: repeat(3, 33%);
        grid-template-columns: repeat(2, 50%);
        padding: 30px 40px;

        .item {
          display: flex;
          justify-content: center;
          align-items: center;

          .list {
            cursor: pointer;
            height: 80px;
            width: 130px;

            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            background-color: #edeef6;
            border-radius: 10px;

            .icon {
              padding: 5px;
            }

            .text {
              font-family: OPPOSans-M;
              font-size: 2rem;
              color: #353338;
              background: #e0e2f5;
              border-radius: 10px;
              width: 100%;

              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
      }
    }

    .zyzx {
      width: 235px;
      height: 227px;
      margin-top: 140px;
      margin-left: 80px;

      .title {
        margin-left: 30px;
        position: relative;
      }

      .title::after {
        position: absolute;
        content: '';
        border-bottom: 6px #cb6c54 solid;
        width: 45px;
        left: 0;
        bottom: -2px;
      }

      .item {
        font-family: OPPOSans-M;
        border-radius: 2px;
        cursor: pointer;
        color: #cb6c54;
        background-color: #ffdeb4;
        width: 140px;
        line-height: 40px;
        text-align: center;
        margin: 20px 30px;
      }
    }
  }

  .tzgg {
    width: 318px;
    color: #fff;
    position: absolute;
    right: 125px;
    bottom: 50px;

    background-repeat: no-repeat;

    .title {
      font-size: 2rem;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      padding: 8px;
      text-shadow: 2px 2px 1px rgba(56, 113, 210, 1);
      color: #fff;
    }

    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      font-size: 1.5rem;
      cursor: pointer;
      margin-bottom: 8px;

      .icon {
        height: 24px;
        width: 24px;
        display: inline-block;
        margin-right: 4px;
        background-image: url('../../assets/hejia/Group 1588.png');
      }
    }

    .page {
      padding-bottom: 10px;
      text-align: center;

      ::v-deep .el-pager li {
        background-color: #7f87c5 !important;
        color: #717abf;
      }

      ::v-deep .el-pager li:not(.disabled).active {
        background-color: #fff !important;
        color: #717abf;
      }
    }
  }
}
</style>
