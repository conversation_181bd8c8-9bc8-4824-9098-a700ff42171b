<template>
  <div>
    <Header></Header>
    <div class="content">
      <div class="warp">
        <router-view></router-view>
      </div>
    </div>
    <Footer></Footer>
  </div>
</template>

<script>
import Header from '@/views/layout/subpageLayout/Header';
import Footer from '@/views/layout/Footer';
export default {
  name: 'HomeLayout',
  components: {
    Header,
    Footer,
  },
  data: () => {
    return {};
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.content {
  padding-top:20px;
  background-color: #f6fdfc;
  min-height: 64.8vh;
  .warp {
    width: 1200px;
    margin: 0 auto;
    padding-bottom: 50px;
  }
}
</style>
