<template>
  <div class="header">
    <MyDialog
        v-if="showPhoneDialog"
        @close="(showPhoneDialog = false), closeLogin(), getCode()"
        dialogWidth="450px"
        title="填写用户信息"
        @confirm="$refs.bindInfo.getFormsDatas()">
      <MyForms
          :columns="bindInfo"
          editable
          ref="bindInfo"
          :modify="true"
          @formsDatas="bindUser"></MyForms>
    </MyDialog>
    <el-dialog
        :visible.sync="showLogin"
        @close="closeLogin()"
        :close-on-click-modal="false"
        class="loginDialog"
        width="50rem"
        append-to-body>
      <div
          class="login-box"
          v-loading="loading">
        <div class="warp">
          <div class="title">
            <img src="../../assets/logoTitle.png"/>
          </div>
          <!-- <div
            class="code"
            :style="`background:url(${loginMode ? code2 : code})`"
            @click="loginMode = !loginMode"></div> -->
          <div v-if="loginMode == 0">
            <div class="p_tb_20 fw_blod fs_20">用户登录</div>
            <el-form
                :model="userInfos"
                :rules="rules"
                ref="loginForm">
              <el-form-item prop="username">
                <el-input
                    placeholder="请输入用户名"
                    v-model="userInfos.username"></el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                    placeholder="请输入密码"
                    v-model="userInfos.password"
                    type="password"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                    type="primary"
                    style="width: 100%"
                    @click="submit">
                  登录
                </el-button>
              </el-form-item>
              <el-form-item prop="remember">
                <el-checkbox
                    label="记住密码"
                    v-model="userInfos.remember"></el-checkbox>
              </el-form-item>
            </el-form>
          </div>
          <div v-else>
            <div class="p_tb_20 fw_blod fs_20 flex just_center">扫码登录</div>
            <div
                style="margin-top: -12px; text-align: center; color: #000"
                class="fs_14 opacity_4">
              请使用微信扫码二维码登录
            </div>
            <div class="wechart">
              <img
                  :src="qrdata.qrdata"
                  width="100%"/>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <div class="logo">
      武侯智汇云
      <img src="../../assets/logo.png"/>
      <!--      <img src="../../assets/huawei_logo.png" />-->
      教育数字芯
    </div>
    <div class="login">
      <div class="account">
        <div
            @click="login"
            v-if="_.isEmpty(userInfo)">
          <!--          请登录您的账号-->
          登录
        </div>
        <div
            v-else
            class="navigation">
          <div
              class="switch"
              v-if="roles.length > 1">
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                {{ role.child_name }}{{ role.role_name }}（{{
                  role.full_name
                }}）
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                    v-for="(item, index) in roles"
                    :key="index"
                    @click.native="switchHandle(item)">
                  {{ item.child_name }}{{ item.role_name }}（{{
                    item.full_name
                  }}）
                  <!--                  {{ item.full_name }}{{ item.role_name }}-->
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div
              v-else
              @click="switchHandle(role)">
            <div class="btn">我的空间</div>
          </div>
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              <img
                  :src="avatar"
                  class="avatar"/>
              {{ name }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="toCenter">
                个人中心
              </el-dropdown-item>
              <el-dropdown-item @click.native="logout">
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import MyDialog from '@/components/MyDialog';
import MyForms from '@/components/MyForms';
import {set, get, clear} from '@/utils/local';
import {getWxqr, checkLogin, bindUser} from '@/api/login';
import code from '@/assets/images/Group2182.png';
import code2 from '@/assets/images/Group2183.png';
import {mapGetters} from 'vuex';

export default {
  name: 'HomeHeader',
  data: () => {
    return {
      showLogin: false,
      showPhoneDialog: false,
      loading: false,
      code,
      code2,
      loginMode: 0,
      times: 0, //计时器
      timer: '',
      lock: true,
      qrdata: '', //二维码信息
      bindInfo: [
        {
          prop: 'phone',
          label: '手机号',
          placeholder: '请输入手机号',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'password',
          label: '密码',
          placeholder: '请输入密码',
          type: 'password',
          default: '',
          required: true,
        },
      ],
      userInfos: {
        username: '',
        password: '',
        remember: '',
      },
      rules: {
        username: [
          {
            required: true,
            message: '请输入用户名',
            trigger: 'blur',
          },
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  created() {
    //检查是否记住密码
    if (get('loginInfo')) {
      this.userInfos = get('loginInfo');
    }
  },
  watch: {
    loginMode: {
      immediate: true,
      handler(val) {
        clearInterval(this.timer);
        //显示二维码
        if (val) {
          this.lock = true;
          this.getCode();
        }
      },
    },
  },
  components: {
    MyDialog,
    MyForms,
  },
  computed: {
    ...mapGetters(['name', 'userInfo', 'avatar', 'role', 'roles', 'defRole']),
  },
  methods: {
    //获取二维码
    async getCode() {
      this.times = 0;
      this.qrdata = '';
      //开始计时200秒后刷新
      this.timer = setInterval(() => {
        this.times++;
        if (this.times > 100) {
          this.getCode();
        }
        if (this.lock) {
          this.checkLogin();
        }
      }, 2000);
      const {data: result} = await getWxqr();
      this.qrdata = result;
    },
    //查询用户登录信息
    async checkLogin() {
      console.log(0);
      if (this.qrdata) {
        const {data: result} = await checkLogin({sid: this.qrdata.sid});
        //扫码后
        if (result.code != 1001) {
          console.log(1);
          this.lock = false;
          clearInterval(this.timer);
          if (result.code == 1002) {
            this.$modal.msgWarning('微信号未绑定，请填写信息');
            this.showPhoneDialog = true;
          }
          if (result.code == 1000) {
            this.$modal.msgSuccess('登录成功');
            this.$store.dispatch('SetToken', result.token);
            //获取用户信息
            this.$store.dispatch('GetInfo');
            //获取用户角色
            this.$store.dispatch('GetRole').then((res) => {
              //设置第一个角色作为默认角色
              if (res.data.length > 0) {
                this.$store.dispatch('SetRole', res.data[0]);
                this.switchHandle(res.data[0]);
              } else {
                this.$modal.msgWarning('当前用户无角色，请联系管理员');
              }
            });
          }
        }
      }
    },
    //用户普通模式登录
    login() {
      this.showLogin = true;
      if (this.loginMode) {
        this.qrdata = '';
        this.getCode();
      }
    },
    submit() {
      this.$refs['loginForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$store
              .dispatch('Login', this.userInfos)
              .then(() => {
                this.$modal.msgSuccess('登录成功');
                //获取用户信息
                this.$store.dispatch('GetInfo');
                //获取用户角色
                this.$store.dispatch('GetRole').then((res) => {
                  //设置第一个角色作为默认角色
                  if (res.data.length > 0) {
                    this.$store.dispatch('SetRole', res.data[0]);
                    this.switchHandle(res.data[0]);
                  } else {
                    this.$modal.msgWarning('当前用户无角色，请联系管理员');
                  }
                });
                this.showLogin = false;
                this.loading = false;
                if (this.userInfos.remember == true) {
                  set('loginInfo', this.userInfos);
                } else {
                  clear('loginInfo');
                }
              })
              .catch(() => {
                this.loading = false;
              });
        }
      });
    },
    logout() {
      this.$modal
          .confirm('是否退出登录')
          .then(() => {
            this.$store.dispatch('LogOut');
          })
          .catch(() => {
          });
    },
    //切换角色
    switchHandle(item) {
      this.$store.dispatch('SetRole', item).then((res) => {
        //进入对应角色空间
        switch (item.role_key) {
          case 'workers_wor':
          case 'area_man':
          case 'dept_man':
          case 'manager_man':
          case 'admin':
          case '':
            this.$router.push({path: '/zhili/index'});
            break;
          case 'school_man':
            this.$router.push({path: '/school/index'});
            //this.$router.push({ path: '/setting' });
            break;
          case 'teacher_tea':
          case 'classTeacher_tea':
          case 'deputyHeadTeacher_tea':
          case 'gradeLeader_tea':
            this.$router.push({path: '/teacher'});
            break;
          case 'student_stu':
            this.$router.push({path: '/lexue/index'});
            break;
          case 'parents_par':
            this.$router.push({path: '/hejia/index'});
            break;
        }
        // //this.$modal.msgSuccess('切换成功');
      });
    },
    //进入个人中心
    toCenter() {
      this.$router.push({path: '/personal'});
    },
    //关闭登录弹窗
    closeLogin() {
      console.log('close');
      setTimeout(() => {
        clearInterval(this.timer);
      }, 1000);
      this.lock = true;
    },
    //微信绑定用户
    bindUser(e) {
      e.sid = this.qrdata.sid;
      bindUser(e).then((res) => {
        if (res.code == 200) {
          this.$modal.msgSuccess('绑定成功');
          this.$store.dispatch('SetToken', res.data.token);
          //获取用户信息
          this.$store.dispatch('GetInfo');
          //获取用户角色
          this.$store.dispatch('GetRole').then((res) => {
            //设置第一个角色作为默认角色
            if (res.data.length > 0) {
              this.$store.dispatch('SetRole', res.data[0]);
              this.switchHandle(res.data[0]);
            } else {
              this.$modal.msgWarning('当前用户无角色，请联系管理员');
            }
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.btn {
  border: 1px #000 solid;
  padding: 2px 10px;
  border-radius: 10px;
}

.code {
  position: absolute;
  right: 20px;
  top: 60px;
  height: 79px;
  width: 79px;
  cursor: pointer;
}

.wechart {
  height: 210px;
  width: 210px;
  margin: 0 auto;
  margin-top: 10px;
  margin-bottom: 32px;
}

//登录框
.loginDialog {
  ::v-deep .el-dialog {
    background-color: transparent;
    box-shadow: none;
  }

  ::v-deep .el-dialog__headerbtn {
    right: 0px;
    top: 30px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 25px;
    height: 25px;
  }

  .login-box {
    color: #0f4444;
    background-color: #fff;
    border-radius: 0.8rem;
    display: flex;
    // padding-bottom: 1px;
    .warp {
      width: 70%;
      // padding: 1rem 0;
      padding-top: 2.5rem;

      margin: 0 auto;

      .title {
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 2.8rem;
        letter-spacing: 0.25rem;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          height: 3.6rem;
          padding: 0 0.6rem;
        }
      }
    }
  }
}

.header {
  height: 60px;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  position: absolute;
  z-index: 986;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  position: relative;
  color: #0f4444;

  .logo {
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 3.8rem;
    letter-spacing: 0.2rem;
    display: flex;
    align-items: center;

    img {
      height: 4rem;
      padding: 0 1rem;
    }
  }

  .login {
    position: absolute;
    right: 0;
    // width: 250px;
    height: 80px;
    margin-right: 40px;
    display: flex;

    align-items: center;

    .account {
      font-weight: 700;
      font-size: 1.5rem;
      cursor: pointer;
      font-family: PingFangSC;
    }
  }

  justify-content: center;

  .el-dropdown-link {
    //color: #0f4444;
    font-size: 1.5rem;
    line-height: 1.5rem;
    display: flex;
    justify-content: space-between;
    justify-items: center;
    align-items: center;

    img {
      padding: 0 0.8rem;
      width: 3rem;
      height: 3rem;
    }
  }

  .navigation {
    display: flex;
    align-items: center;
    cursor: pointer;

    .switch {
      margin-right: 10px;
      border-radius: 20px;
      border: 1px #000000 solid;
      text-align: center;
      padding: 3px 10px;
    }
  }
}
</style>
