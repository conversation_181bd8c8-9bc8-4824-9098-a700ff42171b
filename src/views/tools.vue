<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane
          v-for="(item, index) in datas"
          :label="item.title"
          :name="item.title"
          :key="index">
        <div class="content">
          <div
              v-for="item in item.tools"
              class="list">
            <div class="subTitle">{{ item.subTitle }}</div>
            <div class="apps">
              <div
                  class="app"
                  v-for="item in item.tools"
                  :style="{
                  '--color':
                    'linear-gradient(108deg,#ffffff 0%, ' +
                    item.bgColor +
                    ' 100%)',
                }">
                <a @click="getDetail(item.url)">
                  <img :src="item.icon"/>

                  <div>{{ item.name }}</div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';

export default {
  data() {
    return {
      activeName: '',
      datas: [],
    };
  },
  computed: {
    ...mapGetters(['role']),
  },
  created() {
    //进入指定板块
    this.activeName = this.$route.query.name;

    //进入对应角色空间
    switch (this.role.role_key) {
      case 'workers_wor':
      case 'area_man':
      case 'dept_man':
      case 'manager_man':
      case 'admin':
      case '':
        //治理工空间
        this.datas = [
          {
            title: '应用功能',
            tools: [
              {
                subTitle: '',
                tools: [
                  {
                    name: ' OA收发 ',
                    icon: require('@/assets/icons/hdkt.png'),
                    bgColor: '#FAF3EC',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/officCirculation/manage'
                  },
                  {
                    name: '通知公告',
                    icon: require('@/assets/icons/hjcx.png'),
                    bgColor: '#ecf4fa',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/notice/office_list'
                  },
                  {
                    name: '信息填报',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/infoCollect/fillTask'
                  },
                  {
                    name: '装备管理',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/dock/commonJump/13'
                  },
                  {
                    name: ' 晨午检  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/studentLeaveCheck/checkrecords'
                  },
                  {
                    name: ' 查询系统  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/querySystem/queryPage'
                  },
                  {
                    name: ' 极简问卷  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/quest/fillTask?module=question'
                  },
                  {
                    name: ' 精彩活动  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://hdds.wuhousmartedu.com/index.html'
                  },
                  {
                    name: ' 奖项查询  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy.wuhousmartedu.com/staticOut/#/honor/honorQuiry'
                  },
                  {
                    name: ' 获奖颁发  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy.wuhousmartedu.com/staticOut/#/honor/honorManagement'
                  },
                ],
              },
            ],
          },
        ];

        break;
      case 'school_man':
        //学校空间
        this.datas = [
          {
            title: '应用功能',
            tools: [
              {
                subTitle: '',
                tools: [
                  {
                    name: ' OA收发 ',
                    icon: require('@/assets/icons/hdkt.png'),
                    bgColor: '#FAF3EC',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/officCirculation/manage'
                  },
                  {
                    name: '通知公告',
                    icon: require('@/assets/icons/hjcx.png'),
                    bgColor: '#ecf4fa',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/notice/office_list'
                  },
                  {
                    name: '查询系统',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/querySystem/queryPage'
                  },
                  {
                    name: '极简问卷',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/quest/fillTask?module=question'
                  },
                  {
                    name: ' 信息填报  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/infoCollect/fillTask'
                  },
                  {
                    name: ' 精彩活动  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://hdds.wuhousmartedu.com/index.html'
                  },
                  {
                    name: ' 教育装备  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/dock/commonJump/13'
                  },
                  {
                    name: ' 知网  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://www.cnki.net/'
                  },
                  {
                    name: ' 空中课堂  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://www.wuhousmartedu.com/#/index'
                  },
                  {
                    name: ' 设备报修  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/AssetRepair/approvalSettings'
                  },
                  {
                    name: ' 社团选课  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/CourseSelect/html/admin_setting.html'
                  },
                  {
                    name: ' 教师请假  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/teacherLeave/teacherAdd'
                  },
                  {
                    name: ' 获奖颁发  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/honor/honorManagement'
                  },
                  {
                    name: ' 项目式学习  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/newProjectLearning/subjectSetting'
                  },
                  {
                    name: ' 学生请假  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/stuleave/addLeave'
                  },
                ],
              },
            ],
          },
        ];
        break;
      case 'teacher_tea':
      case 'classTeacher_tea':
      case 'deputyHeadTeacher_tea':
      case 'gradeLeader_tea':
        //优教空间
        this.datas = [
          {
            title: '我的教学区',
            tools: [
              {
                subTitle: '备课',
                tools: [
                  {
                    name: '课件专题（101教育PPT）',
                    icon: require('@/assets/icons/hdkt.png'),
                    bgColor: '#FAF3EC',
                    url: ''
                  },
                  {
                    name: '国家资源',
                    icon: require('@/assets/icons/hjcx.png'),
                    bgColor: '#ecf4fa',
                    url: 'https://basic.smartedu.cn/tchMaterial'
                  },
                  {
                    name: '智慧云资源中心',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://www.wuhousmartedu.com/#/resource/region'
                  },
                ],
              },
              {
                subTitle: '授课',
                tools: [
                  {
                    name: '授课工具（101教育PPT）',
                    icon: require('@/assets/icons/jsqj.png'),
                    bgColor: '#EFECFA',
                    url: ''
                  },
                  {
                    name: '智慧课堂（畅言）',
                    icon: require('@/assets/icons/tzgg.png'),
                    bgColor: '#f1fff4',
                    url: ''
                  },
                  {
                    name: '空中课堂',
                    icon: require('@/assets/icons/xsgl.png'),
                    bgColor: '#FAF3EC',
                    url: 'https://www.wuhousmartedu.com/#/index'
                  },
                  {
                    name: '纸笔互动',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#f1fff4',
                    url: 'https://zhzy.onetarget.cn/user/login'
                  },
                ],
              },
              {
                subTitle: '课后',
                tools: [
                  {
                    name: '教学研究（知网）',
                    icon: require('@/assets/icons/jhh.png'),
                    bgColor: '#f1fff4',
                    url: 'https://www.cnki.net/'
                  },
                  {
                    name: '精准教学（智学网）',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                  },
                ],
              },
            ],
          },
          {
            title: '办公应用',
            tools: [
              {
                subTitle: '',
                tools: [
                  {
                    name: '通知公告',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/notice/office_list'
                  },
                  {
                    name: 'OA收发',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/officCirculation/manage'
                  },
                  {
                    name: '信息填报',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/infoCollect/fillTask'
                  },
                  {
                    name: '极简问卷',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/quest/fillTask?module=question'
                  },
                  {
                    name: '查询系统',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/querySystem/queryPage'
                  },
                  {
                    name: '晨午检',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/studentLeaveCheck/checkrecords'
                  },
                ],
              },
            ],
          },
          {
            title: '我的工具箱',
            tools: [
              {
                subTitle: '',
                tools: [
                  {
                    name: 'PDF编辑器',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://osscdn.wuhousmartedu.com/web/tools/15-Acrobat_DC-2018-WIN-%E5%AE%89%E8%A3%85%E5%8C%85.rar?auth_key=1700578358-0-0-662437ab50ea7374d70b4b439d6898d2&response-content-type=application/octet-stream'
                  },
                  {
                    name: '高清图库',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://pixabay.com/zh/'
                  },
                  {
                    name: '录屏工具',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://osscdn.wuhousmartedu.com/web/tools/oCam_v520.0.rar?auth_key=1700557316-0-0-21ef580675717391f6e7e40be2245c58&response-content-type=application/octet-stream'
                  },
                  {
                    name: 'everything',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://www.voidtools.com/zh-cn/'
                  },
                  {
                    name: '几何画板',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://www.netpad.net.cn/svg.html'
                  },
                  {
                    name: '获奖查询',
                    icon: require('@/assets/icons/xxtb.png'),
                    bgColor: '#ECF4FA',
                    url: 'https://whqjyy.wuhousmartedu.com/staticOut/#/honor/honorQuiry'
                  },
                ],
              },
            ],
          },
        ];

        break;
      case 'student_stu':
        //学生空间
        this.datas = [
          {
            title: '应用功能',
            tools: [
              {
                subTitle: '',
                tools: [
                  {
                    name: ' 精彩活动 ',
                    icon: require('@/assets/icons/hdkt.png'),
                    bgColor: '#FAF3EC',
                    url: 'https://hdds.wuhousmartedu.com/index.html'
                  },
                  {
                    name: '获奖查询',
                    icon: require('@/assets/icons/hjcx.png'),
                    bgColor: '#ecf4fa',
                    url: 'https://whqjyy.wuhousmartedu.com/staticOut/#/honor/honorQuiry'
                  },
                  {
                    name: '乐学通',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://edures.lexuestudy.com/#/index/index'
                  },
                  {
                    name: '社团选课',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/CourseSelect/html/stu_course_batch.html'
                  },
                  {
                    name: ' 实验室  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                  },
                  {
                    name: ' 学习反馈  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/wisdomTask/expandListStudent'
                  },
                  {
                    name: ' 项目式学习  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://iuppbl.wuhousmartedu.com/index.html?login=success#/home'
                  },
                ],
              },
            ],
          },
        ];
        break;
      case 'parents_par':
        //和家空间
        this.datas = [
          {
            title: '家校互通',
            tools: [
              {
                subTitle: '',
                tools: [
                  {
                    name: '习惯清单',
                    icon: require('@/assets/icons/hdkt.png'),
                    bgColor: '#FAF3EC',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/punchCard/cardListStudent'
                  },
                  {
                    name: '教学反馈',
                    icon: require('@/assets/icons/hjcx.png'),
                    bgColor: '#ecf4fa',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/wisdomTask/expandListStudent'
                  },
                  {
                    name: '极简问卷',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/quest/fillTask?module=question'
                  },
                  {
                    name: '信息填报',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/infoCollect/fillTask'
                  },
                  {
                    name: ' 红花榜  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                  },
                  {
                    name: ' 学生请假  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://whqjyy-school.wuhousmartedu.com/index.html#/stuleave/addLeave'
                  },
                  {
                    name: ' 空中课堂  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://www.wuhousmartedu.com/#/index'
                  },
                  {
                    name: ' 师生互动  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                  },
                ],
              },
            ],
          },
          {
            title: '家校共育',
            tools: [
              {
                subTitle: '',
                tools: [
                  {
                    name: ' 实践信息采集 ',
                    icon: require('@/assets/icons/hdkt.png'),
                    bgColor: '#FAF3EC',
                  },
                  {
                    name: '获奖查询',
                    icon: require('@/assets/icons/hjcx.png'),
                    bgColor: '#ecf4fa',
                    url: 'https://whqjyy.wuhousmartedu.com/staticOut/#/honor/honorQuiry'
                  },
                  {
                    name: '精彩活动',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                    url: 'https://hdds.wuhousmartedu.com/index.html'

                  },
                  {
                    name: '成长档案',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                  },
                  {
                    name: ' 智慧体育  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                  },
                  {
                    name: ' 班级通知  ',
                    icon: require('@/assets/icons/kwsj.png'),
                    bgColor: '#faf3ec',
                  },
                ],
              },
            ],
          },
        ];
        break;
    }
  },
  methods: {
    getDetail(url) {
      window.open(url);
    }
  }
};
</script>

<style scoped lang="scss">
a {
  color: inherit; /* 继承父元素的字体颜色 */
  text-decoration: none; /* 去除下划线 */
}

.content {
  .list {
    .subTitle {
      font-size: 1.8rem;
      color: #0f4444;
      padding: 9px 0;
      font-weight: 800;
    }

    .apps {
      font-size: 1.5rem;
      // display: flex;
      display: inline-block;

      .app {
        float: left;

        margin-right: 20px;
        display: flex;
        margin-bottom: 20px;
        justify-content: center;
        align-items: center;
        color: #333333;
        height: 30px;
        font-weight: 700;
        padding: 8px 10px;
        border-radius: 6px;
        cursor: pointer;
        background: var(--color);

        img {
          width: 24px;
          height: 24px;

          margin-right: 10px;
        }
      }
    }
  }
}
</style>
