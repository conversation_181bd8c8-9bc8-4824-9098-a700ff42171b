<!-- 个人信息 -->
<template>
  <div class="container" style="width: 100%;padding-top: 20px;">
    <!--学生设置-->
      <el-form v-if="showDialog==false" style="width: 90%;margin: auto;"label-suffix=":" label-width="90px" :model="formElder" :rules="rulesElder" ref="rulesElder">

        <el-form-item label="学校" v-if="stageId">
          <span> {{ schoolName }} </span>
        </el-form-item>

        <el-form-item label="学校" prop="stageId" v-if="stageId==null">
          <el-select style="width: 100%;"
                  :disabled="modify"
                  @change="getGard"
                  v-model="formElder.stageId">
            <el-option
                    :value="item.stageId"
                    :label="item.fullName"
                    v-for="item in stageList"
                    :key="item.stageId"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="所在年级" prop="gradeId">
          <el-select style="width: 100%;"
            :disabled="modify"
            @change="getClass"
            v-model="formElder.gradeId">
            <el-option
              :value="item.gradeId"
              :label="item.gradeName"
              v-for="item in gradeList"
              :key="item.gradeId"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          label="所在班级"
          prop="classId">
          <el-select style="width: 100%;"
            @change="getStudentInfo"
            v-model="formElder.classId"
            :disabled="modify">
            <el-option
              v-for="item in classList"
              :key="item.classId"
              :value="item.classId"
              :label="item.showName"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
                label="学生"
                prop="stUserId">
          <el-select style="width: 100%;"
                  v-model="formElder.stUserId"
                  :disabled="modify" filterable >
            <el-option
                    v-for="item in studentList"
                    :key="item.userId"
                    :value="item.userId"
                    :label="item.userName"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="家长姓名" prop="userName">
          <el-input v-model="formElder.userName" placeholder="家长姓名" maxlength="20"/>
        </el-form-item>

        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="formElder.phone" placeholder="手机号码" maxlength="20"/>
        </el-form-item>

        <el-form-item label="监护人" prop="isGuardian">
          <el-select style="width: 100%;" v-model="formElder.isGuardian" placeholder="证件类型" clearable controls-position="right">
            <el-option
                    v-for="dict in isGuardianOptions"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="家长身份" prop="kinship">
          <el-select style="width: 100%;" v-model="formElder.kinship" placeholder="家长身份" clearable controls-position="right">
            <el-option
                    v-for="dict in dict.type.kinship_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="其他身份" prop="kinshipOther" v-if="formElder.kinship == '99'">
          <el-input v-model="formElder.kinshipOther" placeholder="其他身份" maxlength="20"/>
        </el-form-item>

        <el-form-item label="证件类型" prop="idcardType">
          <el-select style="width: 100%;" v-model="formElder.idcardType" placeholder="证件类型" clearable controls-position="right">
            <el-option
                    v-for="dict in dict.type.idcard_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="证件号码" prop="idcard">
          <el-input v-model="formElder.idcard" placeholder="证件号码" maxlength="20"/>
        </el-form-item>

      </el-form>
    <div class="dialog-button" v-if="showDialog==false">
      <el-button
              type="primary"
              style="width: 100%;border-radius:0px;"
              @click="bindElder">
        提交
      </el-button>
    </div>

    <div v-if="showDialog" style="width: 90%;margin: auto;">
      <div style="font-size: 15px;">
        {{ dialogText }}
      </div>
    </div>

<!--    <MyAlert v-if="showDialog" dialog-width="85%" @close="closeAlert">-->
<!--      <div>-->
<!--        {{ dialogText }}-->
<!--      </div>-->
<!--    </MyAlert>-->
  </div>
</template>

<script>
import Title from '@/components/Title';
import MyAlert from '@/components/MyAlert';
import BaseInfo from '@/components/BaseInfo';
import MyForms from '@/components/MyForms';
import {getCode,getStage,getStageList,getGradeList,getClassList,getStuList,bindElder} from '@/api/bind';
import { mapGetters } from 'vuex';
import {
  getInfoByidcard,
  getStudentInfo,
} from '@/api/youjiao/index';
export default {
  dicts: ['kinship_type','idcard_type'],
  name: 'bind',
  components: {
    Title,
    MyAlert,
    BaseInfo,
    MyForms,
  },
  data: () => {
    return {
      showDialog: false,
      dialogText: '',
      showSchool: false,
      showPsw: false,
      showRole: false,
      showStudent: false,
      showEditSchool: false,
      showHobby: false,
      hobbys: [],
      effective:false,
      isGuardianOptions: [
        {
          label: "监护人",
          value: "1"
        },
        {
          label: "非监护人",
          value: "0"
        },
      ],
      menus: {
        baseInfoSetting: true,
        accountSetting: true,
        roleSetting: false,
        classSetting: false,
        studentSetting: false,
        eldersSetting: false,
        hobbiesSetting: true,
      },
      activeName: 'first',
      modify: false,
      roleRadio: '', //默认角选择
      schoolName: '',
      stageId: null,
      stageList:[],//学校列表
      gradeList: [], //年级列表
      // classList: [],
      courseList: [], //学科列表
      subList: [], //教师任教班级
      elders: '',
      gradeNum: 1, //设置的年级数量
      school: [{ stageId: '', classId: '', gradeId: '', courseId: '' }],
      editSchool: {
        classId: '',
        gradeId: '',
        graphy: '',
        stageId: '',
        teacherId: '',
        userId: '',
        teacherCourseId: '',
      },
      baseStageList: [], //学校列表
      classList: [], //任教班级列表
      studentList: [], //关联学生列表
      student: {
      },
      formElder: {
        isGuardian: '1',
      },
      rulesElder: {
        userName: [
          {required: true, message: "家长姓名不能为空", trigger: "blur"}
        ],
        phone: [
          {required: true, message: "家长手机号不能为空", trigger: "blur"}
        ],
        kinship: [
          {required: true, message: "家长身份不能为空", trigger: "blur"}
        ],
        stUserId: [
          {required: true, message: "请选择学生", trigger: "blur"}
        ],
      },
    };
  },
  async created() {
    // console.log('this.$route.query.secret::::'+this.$route.query.secret);
    // this.menus.classSetting = true;
    // this.menus.studentSetting = true;
    // this.menus.eldersSetting = true;
    this.initPage();
  },
  computed: {
    ...mapGetters(['userInfo', 'defRole', 'roles', 'role']),
  },
  watch: {
  },
  methods: {
    async initPage(){
      getCode({secret:this.$route.query.secret}).then(response => {
        // console.log(response);
        this.effective = response.data.effective;

        if(this.effective){
          let stageId = response.data.stageId;
          if(stageId){
            this.stageId = stageId;
            getStage(stageId).then(stageRes => {
              this.schoolName = stageRes.data.fullName;
              this.getGard(stageId);
            });
          }else{
            let agencyId = response.data.agencyId;
            getStageList(agencyId).then(agencyRes => {
              this.stageList = agencyRes.data;
              // this.getGard(stageId);
            });
          }
        }else{
          this.dialogText = '您的邀请码已过期，请联系管理员重新获取邀请码！';
          this.showDialog = true;
        }
      });

    },

    //获取学生详细信息
    async getStudent() {
      const { data: info } = await getStudentInfo(this.role.student_id);
      this.elders = info.elders;
      console.log(info);
    },
    //获取年级
    getGard(e, gradeId) {
      this.school[0].gradeId = '';
      this.school[0].classId = '';
      this.school[0].courseId = '';
      getGradeList({ stageId: e }).then((res) => {
        if (gradeId) {
          //获取所在年级period
          let selected = res.find((item) => item.gradeId == gradeId);
          this.getClass(gradeId, selected.period);
        }
        this.gradeList = res;
      });
    },
    //获取班级
    getClass(e, period) {
      this.school[0].classId = '';
      getClassList({ gradeId: e }).then((res) => {
        this.classList = res;
      });
    },
    //获取学生信息
    getStudentInfo(e) {
      getStuList({ classId: e }).then((res) => {
        this.studentList = res;
      });
    },
    //绑定家长信息
    bindElder() {
      if(this.effective){
        this.$refs.rulesElder.validate((valid) => {
          if (valid) {
            bindElder(this.formElder).then((res) => {
              if (res.code == 200) {
                // this.$modal.msgSuccess('绑定成功！');
                if(res.data){
                  this.dialogText = '绑定学生成功，你的账户已存在，请用账户登陆平台！';
                  // this.$alert('绑定学生成功，你的账户已存在，请用账户登陆平台！');
                }else{
                  this.dialogText = '绑定学生成功，你的账户已创建，请用手机号登陆平台，初始密码为手机号后八位，请尽快登录平台修改密码！';
                  // this.$alert('绑定学生成功，你的账户已创建，请用手机号登陆平台，初始密码为手机号后八位，请尽快登录平台修改密码！');
                }
                this.showDialog = true;
                //window.location.close();
              }
            });
          }
        });
      }
    },
    closeAlert(){
      // this.showDialog = false;
      // $emit('close');
      window.close();
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-form-item__label {
  font-size: 15px;
}
::v-deep .el-form-item__content {
  font-size: 15px;
  font-weight: 700;
  opacity: 0.8;
}
.schoolBox {
  .schoolDialog {
    background-color: #fafafa;
    border: 1px #fafafa solid;
    padding: 0 20px;

    .el-select {
      width: 100%;
    }
  }
  .add {
    width: 80%;
    margin: 20px auto;
  }
}

.container {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  .box {
    border-radius: 0.6rem;
    margin: 1rem 0;
    padding: 2rem;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .baseInfo {
      display: flex;
      justify-content: space-around;
      width: 100%;
      padding: 2rem;
    }
    .account {
      width: 100%;
      text-align: left;
      display: flex;
      font-size: 1.5rem;
      > div {
        padding: 20px;
        margin-right: 100px;
      }
    }
    .roles {
      display: block;
      font-size: 1.5rem;
      .item {
        padding-bottom: 5px;
        display: inline-block;
        margin-right: 10px;
      }
    }
    .students {
      display: block;
      padding: 10px 0 0 15px;
      .warp {
        // width: 500px;
        display: flex;

        font-size: 1.5rem;
        display: grid;
        grid-template-columns: 18% 80%;
        > div {
          margin-bottom: 10px;
        }
      }
    }
    .hobbies {
      display: block;
      padding: 0 2rem;
      .warp {
        font-size: 1.5rem;
        display: grid;
        grid-template-columns: repeat(8, 10%);
        i {
          cursor: pointer;
        }
        > div {
          margin-top: 20px;
        }
      }
    }
    .classes {
      display: flex;
      flex-direction: column;
      .item {
        display: flex;
        align-items: center;
        justify-content: space-between;

        font-size: 1.5rem;
      }
    }
    .schools {
      display: grid;

      .item {
        background-color: #f6fafb;
        margin-bottom: 10px;
      }
      .title {
        font-size: 1.5rem;
        opacity: 0.6;
        font-weight: 500;
      }
      .name {
        font-size: 1.5rem;
        opacity: 0.8;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .classes {
        padding: 10px;
        height: 103px;

        // overflow: scroll;
      }
      .bar {
        border-top: 1px #dee1e2 solid;
        padding: 5px;
        border-radius: 6px;
        display: flex;
        button {
          flex: 1;
          height: 40px;
          border: none;
        }
      }
      grid-template-columns: repeat(3, 30%);
    }
  }
}

::v-deep .el-message-box__wrapper .el-message-box {
  /*position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);*/
  width: 85% !important; /* 设置弹窗的宽度 */
  border-radius: 4px;
  /* 其他样式 */
}
</style>
