<!-- 个人信息 -->
<template>
  <div class="container">
    <!--兴趣爱好-->
    <MyDialog
      title="选择兴趣爱好"
      v-if="showHobby"
      @close="showHobby = !showHobby"
      dialogWidth="380px"
      @confirm="addHobby">
      <div style="margin-bottom: 40px; margin-left: 50px">
        <el-select
          v-model="hobbys"
          multiple
          placeholder="请选择兴趣爱好">
          <el-option
            v-for="dict in dict.type['sys_hobby']"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>
      </div>
    </MyDialog>
    <!-- 账户信息编辑-->
    <MyDialog
      title="账户/密码设置"
      v-if="showPsw"
      @close="showPsw = !showPsw"
      dialogWidth="380px"
      @confirm="
        activeName == 'first'
          ? $refs.account1.getFormsDatas()
          : $refs.account2.getFormsDatas()
      ">
      <el-tabs
        type="border-card"
        class="m_b_20"
        v-model="activeName">
        <el-tab-pane
          label="账号设置"
          name="first">
          <MyForms
            :columns="account"
            labelWidth="80px"
            editable
            @formsDatas="setAccount"
            ref="account1"></MyForms>
        </el-tab-pane>
        <el-tab-pane
          label="密码设置"
          name="second">
          <MyForms
            :columns="password"
            labelWidth="100px"
            editable
            @formsDatas="setAccount"
            ref="account2"></MyForms>
        </el-tab-pane>
      </el-tabs>
    </MyDialog>
    <!-- 基础信息编辑-->
    <MyDialog
      title="基础信息"
      v-if="showDialog"
      @close="showDialog = !showDialog"
      @confirm="$refs.forms.getFormsDatas()"
      dialogWidth="950px">
      <div>
        <BaseInfo
          :columns="formColumns"
          :editable="true"
          ref="forms"
          @formsDatas="formsDatas"></BaseInfo>
      </div>
    </MyDialog>
    <!-- 学校设置-->
    <MyDialog
      v-if="showSchool"
      @close="showSchool = !showSchool"
      @confirm="saveSchool"
      title="学校设置"
      dialogWidth="550px"
      class="schoolBox">
      <el-form
        class="schoolDialog"
        label-width="80px"
        :model="school[i - 1]"
        v-for="i in gradeNum">
        <div v-show="i == 1">
          <el-form-item
            label="学校名称:"
            prop="stageId">
            <el-select
              :disabled="gradeNum > 1"
              v-model="school[i - 1].stageId"
              filterable
              @change="getGard">
              <el-option
                v-for="item in schoolList"
                :label="item.fullName"
                :value="item.stageId"
                :key="item.stageId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="任教年级:"
            prop="gradeId">
            <el-select
              :disabled="gradeNum > 1"
              v-model="school[i - 1].gradeId"
              @change="getClass">
              <el-option
                v-for="item in gradeList"
                :key="item.gradeId"
                :value="item.gradeId"
                :label="item.gradeName"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <el-form-item
          label="任教班级:"
          prop="classId">
          <el-select
            v-model="school[i - 1].classId"
            @change="getCourse"
            multiple>
            <el-option
              v-for="item in classList"
              :key="item.classId"
              :value="item.classId"
              :label="item.showName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="任教学科:"
          prop="courseId">
          <el-select v-model="school[i - 1].courseId">
            <el-option
              v-for="item in courseList"
              :key="item.courseId"
              :label="item.courseName"
              :value="item.courseId"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="add">
        <el-button
          style="width: 100%; border-radius: 20px"
          @click="addGrade">
          添加年级
        </el-button>
      </div>
    </MyDialog>
    <!-- 修改任教学校-->
    <MyDialog
      v-if="showEditSchool"
      @close="showEditSchool = !showEditSchool"
      @confirm="editTeacherCourse"
      title="学校设置"
      dialogWidth="550px"
      class="schoolBox">
      <el-form
        class="schoolDialog"
        label-width="80px"
        :model="editSchool">
        <el-form-item
          label="学校名称:"
          prop="stageId">
          <el-select
            v-model="editSchool.stageId"
            filterable
            @change="getGard">
            <el-option
              v-for="item in schoolList"
              :label="item.fullName"
              :value="item.stageId"
              :key="item.stageId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="任教年级:"
          prop="gradeId">
          <el-select
            v-model="editSchool.gradeId"
            @change="getClass">
            <el-option
              v-for="item in gradeList"
              :key="item.gradeId"
              :value="item.gradeId"
              :label="item.gradeName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="任教班级:"
          prop="classId">
          <el-select
            v-model="editSchool.classId"
            @change="getCourse">
            <el-option
              v-for="item in classList"
              :key="item.classId"
              :value="item.classId"
              :label="item.showName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="任教学科:"
          prop="graphy">
          <el-select v-model="editSchool.graphy">
            <el-option
              v-for="item in courseList"
              :key="item.courseId"
              :label="item.courseName"
              :value="item.courseId"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </MyDialog>
    <!--默认角色-->
    <MyDialog
      title="默认角色设置"
      dialogWidth="450px"
      v-if="showRole"
      @close="showRole = !showRole"
      @confirm="saveRole">
      <el-radio-group
        v-model="roleRadio"
        style="padding-bottom: 20px; font-size: 1.5rem">
        <el-radio
          style="padding-bottom: 10px"
          :label="item.role_name + item.full_name"
          v-for="(item, index) in roles"
          :key="index">
          {{ item.role_name + '（' + item.full_name + '）' }}
        </el-radio>
      </el-radio-group>
    </MyDialog>

    <!--学生设置-->
    <MyDialog
      v-if="showStudent"
      title="添加学生"
      dialogWidth="450px"
      @close="showStudent = !showStudent"
      @confirm="saveStudent">
      <el-form
        label-suffix=":"
        label-width="90px"
        :model="student">
        <el-form-item
          label="身份证号"
          prop="idCard">
          <el-input
            @blur="getStudentInfo"
            v-model="student.idCard"></el-input>
        </el-form-item>
        <el-form-item
          label="学生姓名"
          prop="userName">
          <el-input
            v-model="student.userName"
            disabled></el-input>
        </el-form-item>
        <el-form-item
          label="学校名"
          prop="stageNmae">
          <el-input
            v-model="student.stageNmae"
            disabled></el-input>
        </el-form-item>
        <el-form-item
          label="所在年级"
          prop="gradeId">
          <el-select
            :disabled="modify"
            @change="getClass"
            v-model="student.gradeId">
            <el-option
              :value="item.gradeId"
              :label="item.gradeName"
              v-for="item in gradeList"
              :key="item.gradeId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="所在班级:"
          prop="classId">
          <el-select
            v-model="student.classId"
            :disabled="modify">
            <el-option
              v-for="item in classList"
              :key="item.classId"
              :value="item.classId"
              :label="item.showName"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </MyDialog>
    <!-- 家长设置-->
    <div
      class="box"
      v-if="menus.baseInfoSetting">
      <Title title="基础信息"></Title>
      <el-button
        class="edit_btn"
        @click="editBase">
        编辑
      </el-button>
      <div class="baseInfo">
        <BaseInfo
          :columns="formColumns"
          labelWidth="12rem"></BaseInfo>
      </div>
    </div>
    <div
      class="box"
      v-if="menus.accountSetting">
      <Title title="账户信息"></Title>
      <el-button
        class="edit_btn"
        @click="showPsw = true">
        编辑
      </el-button>
      <div class="account">
        <div>
          <span class="opacity_6">登录账户：</span>
          {{ userInfo.account }}
        </div>
        <div>
          <span class="opacity_6">密码：</span>
          建议您使用安全性较高的密码，并定期更换
        </div>
      </div>
    </div>
    <div
      class="box"
      v-if="menus.roleSetting">
      <Title title="默认角色设置"></Title>
      <el-button
        class="edit_btn"
        @click="showRole = true">
        编辑
      </el-button>
      <div class="baseInfo roles">
        <div>
          <span class="opacity_6">默认角色：</span>
          <span>{{ defRole.full_name }}{{ defRole.role_name }}</span>
        </div>
        <br />
        <div class="flex">
          <div class="opacity_6">所有角色：</div>
          <div style="width: 80%">
            <span
              class="item"
              v-for="item in roles">
              {{ item.role_name + '（' + item.full_name + '）' }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div
      class="box"
      v-if="menus.classSetting">
      <Title title="任职信息"></Title>
      <el-button
        class="edit_btn"
        @click="showSchool = true">
        新增
      </el-button>
      <div class="baseInfo classes">
        <el-table :data="subList">
          <el-table-column
            prop="name"
            width="400"></el-table-column>
          <el-table-column prop="class.grade_name"></el-table-column>
          <el-table-column prop="class.show_name"></el-table-column>
          <el-table-column prop="class.course_name"></el-table-column>
          <el-table-column width="100">
            <template slot-scope="scope">
              <div class="flex">
                <el-button
                  plain
                  class="edit_btn_text"
                  style="padding: 0"
                  @click="editClass(scope.row)">
                  编辑
                </el-button>
                <el-button
                  plain
                  style="padding: 0"
                  class="del_btn_text"
                  @click="delClass(scope.row)">
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- <div
          class="item"
          v-for="(item, index) in subList"
          :key="index">
          <div
            class="flex just_between"
            style="width: 80%">
            <div>
              {{ item.name }}
            </div>
            <div>
              {{ item.class.grade_name }}
            </div>
            <div>
              {{ item.class.show_name }}
            </div>
            <div>
              {{ item.class.course_name }}
            </div>
          </div>
          <div
            class="flex just_center"
            style="flex: 1">
            <div
              style="width: 60px"
              class="flex just_around">
              <el-button
                plain
                class="edit_btn_text"
                @click="editClass(item)">
                编辑
              </el-button>
              <el-button
                plain
                class="del_btn_text"
                @click="delClass(item, index)">
                删除
              </el-button>
            </div>
          </div> -->
        <!-- <div class="school">
            <div class="classes">
              <el-row class="m_b_20">
                <el-col :span="24">
                  <span class="title">所属学校：</span>
                  <span>
                    <div
                      :title="item.name"
                      class="name"
                      style="
                        width: 220px;
                        display: inline-block;
                        position: absolute;
                      ">
                      {{ item.name }}
                    </div>
                  </span>
                </el-col>
              </el-row>
              <el-row class="m_b_10">
                <el-col :span="12">
                  <span class="title">任教年级：</span>
                  <span class="name">{{ item.class.grade_name }}</span>
                </el-col>
                <el-col :span="12">
                  <span class="title">任教班级：</span>
                  <span class="name">{{ item.class.class_name }}</span>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <span class="title">任教学科：</span>
                  <span class="name">{{ item.class.course_name }}</span>
                </el-col>
              </el-row>
            </div>
            <div class="bar">
              <el-button
                class="edit_btn_text"
                @click="$modal.alert('暂无功能')">
                编辑
              </el-button>
              <el-button
                class="del_btn_text"
                @click="$modal.alert('暂无功能')">
                删除
              </el-button>
            </div>
          </div>
        </div>-->
      </div>
    </div>
    <div
      class="box"
      v-if="menus.studentSetting">
      <Title title="关联学生"></Title>
      <div>
        <el-button
          class="edit_btn"
          @click="showStudent = true">
          新增
        </el-button>
        <!-- <el-button
          class="edit_btn"
          @click="showSchool = true">
          编辑
        </el-button> -->
      </div>
      <div
        class="baseInfo students"
        v-for="item in studentList"
        :key="item.class_id">
        <div class="warp">
          <div>
            <span class="opacity_6">学生姓名：</span>
            {{ item.child_name }}
          </div>
          <div>
            <span class="opacity_6">学生学校：</span>
            {{ item.full_name }}
          </div>
          <div>
            <span class="opacity_6">学生年级：</span>
            {{ item.grade_name }}
          </div>

          <div>
            <span class="opacity_6">学生班级：</span>
            {{ item.class_name }}
          </div>
        </div>

        <!-- <div>
          <span class="opacity_6">学生姓名：</span>
          <span>xxx</span>
        </div>
        <div>
          <span class="opacity_6">学生班级：</span>
          <span>xxx</span>
        </div> -->
      </div>
    </div>
    <div
      class="box"
      v-if="menus.eldersSetting">
      <Title title="家长信息"></Title>
      <div>
        <!-- <el-button
          class="edit_btn"
          @click="showStudent = true">
          新增
        </el-button> -->
      </div>
      <div class="baseInfo students">
        <div
          class="warp"
          v-for="(item, index) in elders"
          :key="item.userId">
          <div>
            <span class="opacity_6">监护人{{ index + 1 }}：</span>
            {{ item.userName }}
          </div>
          <div>
            <span class="opacity_6">联系方式：</span>
            {{ item.phone }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="box"
      v-if="menus.hobbiesSetting">
      <Title title="兴趣爱好"></Title>
      <div>
        <el-button
          class="edit_btn"
          @click="showHobby = true">
          新增
        </el-button>
        <!-- <el-button
          class="edit_btn"
          @click="$modal.alert('暂无功能')">
          编辑
        </el-button> -->
      </div>
      <div class="baseInfo hobbies">
        <div class="warp">
          <div v-for="item in userInfo.hobbyList">
            {{ item }}
            <i
              class="el-icon-error"
              style="color: red"
              @click="delHobby(item)"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Title from '@/components/Title';
import MyDialog from '@/components/MyDialog';
import BaseInfo from '@/components/BaseInfo';
import MyForms from '@/components/MyForms';
import {
  getProfile,
  setProfile,
  updatePwd,
  addHobby,
  delHobby,
} from '@/api/user';
import { mapGetters } from 'vuex';
import {
  baseStageList,
  getAllBaseStage,
  setRoleDef,
  getBaseGradeList,
  getBaseClassList,
  editCourse,
  getInfoByidcard,
  getStudentInfo,
  getCourseList,
  editTeacherCourse,
  delTeacherCourse,
} from '@/api/youjiao/index';
export default {
  dicts: ['sys_hobby'],
  name: 'PersonalInformation',
  components: {
    Title,
    MyDialog,
    BaseInfo,
    MyForms,
  },
  data: () => {
    return {
      showDialog: false,
      showSchool: false,
      showPsw: false,
      showRole: false,
      showStudent: false,
      showEditSchool: false,
      showHobby: false,
      hobbys: [],
      menus: {
        baseInfoSetting: true,
        accountSetting: true,
        roleSetting: false,
        classSetting: false,
        studentSetting: false,
        eldersSetting: false,
        hobbiesSetting: true,
      },
      activeName: 'first',
      modify: false,
      roleRadio: '', //默认角选择
      schoolList: [],
      gradeList: [], //年级列表
      // classList: [],
      courseList: [], //学科列表
      subList: [], //教师任教班级
      elders: '',
      gradeNum: 1, //设置的年级数量
      school: [{ stageId: '', classId: '', gradeId: '', courseId: '' }],
      editSchool: {
        classId: '',
        gradeId: '',
        graphy: '',
        stageId: '',
        teacherId: '',
        userId: '',
        teacherCourseId: '',
      },
      baseStageList: [], //学校列表
      classList: [], //任教班级列表
      studentList: [], //关联学生列表
      student: {
        idCard: '',
        userName: '',
        stageNmae: '',
      },
      account: [
        {
          prop: 'account',
          label: '账号名',
          placeholder: '请输入账号名',
          type: 'input',
          default: '',
          required: true,
        },
      ],
      password: [
        {
          prop: 'oldPassword',
          label: '原密码',
          placeholder: '请输入原始密码',
          type: 'password',
          default: '',
          required: true,
        },
        {
          prop: 'newPassword',
          label: '新密码',
          placeholder: '请输入新密码',
          type: 'password',
          required: true,
          default: '',
        },
        {
          prop: 'password2',
          label: '再次输入',
          placeholder: '请再次输入新密码',
          type: 'password',
          required: true,
          default: '',
        },
      ],
      formColumns: [
        {
          prop: 'headIcon',
          label: '头像',
          placeholder: '',
          type: 'photo',
          default: '',
          required: false,
        },
        {
          prop: 'userName',
          label: '姓名',
          placeholder: '请输入姓名',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'sex',
          label: '性别',
          placeholder: '请选择性别',
          type: 'dict',
          dictType: 'sys_user_sex',
          multiple: false,
          default: '',
          options: [],
          required: false,
        },
        {
          prop: 'phone',
          label: '手机号码',
          placeholder: '请输入手机号',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'politicalLandscape',
          label: '政治面貌',
          placeholder: '请选择政治面貌',
          type: 'dict',
          dictType: 'politics_status',
          multiple: false,
          default: '',
          required: true,
        },
        {
          prop: 'nation',
          label: '民族',
          placeholder: '请选择民族',
          type: 'dict',
          dictType: 'user_nation',
          default: '',
          required: true,
        },

        {
          prop: 'idcardType',
          label: '证件类型',
          placeholder: '请选择证件类型',
          type: 'dict',
          dictType: 'idcard_type',
          options: [],
          default: '',
          required: false,
        },
        {
          prop: 'idcard',
          label: '证件号码',
          placeholder: '请输入证件号码',
          type: 'input',
          default: '',
          required: false,
        },
        {
          prop: 'birthday',
          label: '出生日期',
          placeholder: '请选择出生日期',
          type: 'date',
          default: '',
          required: false,
        },
        // {
        //   prop: 'nativePlace',
        //   label: '籍贯',
        //   placeholder: '请填写籍贯',
        //   type: 'input',
        //   default: '',
        //   required: false,
        // },
        // {
        //   prop: 'registeredResidence',
        //   label: '户籍所在地',
        //   placeholder: '请填写户籍所在地',
        //   type: 'input',
        //   default: '',
        //   required: false,
        // },
      ],
    };
  },
  async created() {
    // console.log('this.$route.query.token::::'+this.$route.query.token);
    // this.$store
    //         .dispatch('loginTri', this.$route.query.token)
    //         .then(() => {
    //           // this.$modal.msgSuccess('登录成功');
    //           //获取用户信息
    //           this.$store.dispatch('GetInfo');
    //           //获取用户角色
    //           this.$store.dispatch('GetRole').then((res) => {
    //             //设置第一个角色作为默认角色
    //             if (res.data.length > 0) {
    //               this.$store.dispatch('SetRole', res.data[0]);
    //               // this.switchHandle(res.data[0]);
    //               this.initPage();
    //             } else {
    //               this.$modal.msgWarning('当前用户无角色，请联系管理员');
    //             }
    //           });
    //           this.loading = false;
    //         })
    //         .catch(() => {
    //           this.loading = false;
    //         });

    this.timerP = setInterval(() => {
      this.initPage();
      clearInterval(this.timerP);
    }, 100);

  },
  computed: {
    ...mapGetters(['userInfo', 'defRole', 'roles', 'role']),
  },
  watch: {
    //初始化弹窗
    showSchool(val) {
      if (val) {
        this.gradeNum = 1;
        this.school = [
          {
            stageId: '',
            classId: '',
            gradeId: '',
            courseId: '',
          },
        ];
      }
    },
  },
  methods: {
    async initPage(){
      this.getUserInfo();
      //const { data: baseList } = await baseStageList();

      if (this.roles.length > 1) {
        //显示角色配置
        this.menus.roleSetting = true;
        //设置默认账户
        this.account[0].default = this.userInfo.account;
        //设置默认角色
        this.roleRadio = this.defRole.role_name + this.defRole.full_name;
      }
      let showClass = false,
              showStu = false,
              showPar = false,
              stageList = [];
      this.roles.map((item) => {
        if (item.role_key == 'school_man') {
          showClass = true;
          stageList.push(item.stage_id);
        }
        if (item.role_key.includes('_tea')) {
          showClass = true;
          stageList.push(item.stage_id);
        }
        if (item.role_key.includes('_stu')) {
          showPar = true;
        }
        if (item.role_key.includes('_par')) {
          showStu = true;
          this.studentList.push(item);
        }
      });
      //教师处理
      if (showClass) {
        //查询角色学校

        this.schoolList = await getAllBaseStage(stageList);
        // console.log(this.schoolList);
        //获取学科
        // this.getCourseList();
        //获取任教学校
        this.getTeacherCourse();
      }
      if (showPar) {
        this.getStudent();
      }
      this.menus.classSetting = showClass;
      this.menus.studentSetting = showStu;
      this.menus.eldersSetting = showPar;
    },
    getData(item) {
      item.map((item) => {
        if (item.children.length > 0) {
          this.getData(item.children);
        }
      });
      return item;
    },
    //获取任教学科
    getTeacherCourse() {
      let subs = [];
      // console.log('this.roles::::'+JSON.stringify(this.roles));
      this.roles.map((item) => {
        if (item.sub) {
          item.sub.map((item2) => {
            subs.push({
              name: item.full_name,
              stageId: item.stage_id,
              class: item2,
              teacherId: item.teacher_id,
            });
          });
        }
      });
      this.subList = subs;
    },
    //用户信息处理
    getUserInfo() {
      //赋值
      this.formColumns.forEach((item) => {
        item.default = this.userInfo[item.prop];
      });
    },
    editBase() {
      this.showDialog = true;
    },
    //获取表单数据
    formsDatas(res) {
      let newObj = _.clone(res);
      delete newObj.headIcon;
      setProfile(newObj)
        .then((r) => {
          if (r.code == 200) {
            this.showDialog = false;
            this.$store.dispatch('UpdateInfo', res);
            this.getUserInfo();
            this.$modal.msgSuccess('修改成功！');
          }
        })
        .catch(() => {});
    },
    //设置账号信息
    setAccount(r) {
      if (this.activeName == 'first') {
        setProfile(r)
          .then(() => {
            this.showDialog = false;
            let res = this.userInfo;
            res.account = r.account;
            this.$store.dispatch('UpdateInfo', res);
            this.getUserInfo();
            this.$modal.msgSuccess('修改成功！');
            this.showPsw = false;
          })
          .catch(() => {});
      } else {
        if (r.newPassword === r.password2) {
          //密码强弱判断
          var patt =
            /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[~!@#$%^&*(){}\[\]<>?\\+])[A-Za-z\d~!@#$%^&*()\[\]{}<>?\\+]{8,20}$/;
          if (patt.test(r.newPassword)) {
            updatePwd(r).then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess('修改成功！');
                this.showPsw = false;
              }
            });
          } else {
            this.$alert(
              `
            1、长度8~20 位<br />

            	  2、必须包含至少一个大写字母<br />
            	  3、必须包含至少一个小写字母<br />
            	  4、必须包含数字<br />
            	  5、必须包含至少一个特殊字符，不能包含空格<br />
            	  6、只能包含以下特殊字符 ~!@#$%^&*()[]{}<>?\+
            `,
              '密码规则',
              {
                dangerouslyUseHTMLString: true,
              }
            );
          }
        } else {
          this.$modal.msgWarning('两次密码不一致请重新输入！');
        }
        // console.log(r);
      }
    },
    //设置默认角色信息
    saveRole() {
      let result = this.roles.find(
        (item) => item.role_name + item.full_name == this.roleRadio
      );

      if (result) {
        setRoleDef({
          id:
            this.roleRadio == 'workers_wor'
              ? result.dept_id
              : result.stage_id
              ? result.stage_id
              : result.agency_id,
          roleKey: result.role_key,
        }).then((res) => {
          this.$modal.msgSuccess('设置成功！');
          this.showRole = false;
          this.$store.dispatch('UpdateRole', result);
        });
      } else {
        this.$modal.msgWarning('请选择角色！');
      }
    },
    //获取学生详细信息
    async getStudent() {
      const { data: info } = await getStudentInfo(this.role.student_id);
      this.elders = info.elders;
      // console.log(info);
    },
    //获取年级

    getGard(e, gradeId) {
      this.school[0].gradeId = '';
      this.school[0].classId = '';
      this.school[0].courseId = '';
      getBaseGradeList({ stageId: e }).then((res) => {
        if (gradeId) {
          //获取所在年级period
          let selected = res.find((item) => item.gradeId == gradeId);
          this.getClass(gradeId, selected.period,e);
        }
        this.gradeList = res;
      });
    },
    //获取班级
    getClass(e, period,stageId) {
      this.school[0].classId = '';
      this.school[0].courseId = '';
      //获取任教学科
      let item = this.gradeList.find((item) => item.gradeId == e);
      console.log('item:::'+item);
      this.getCourseList(item ? item : { period: period,stageId:stageId});
      getBaseClassList({ gradeId: e }).then((res) => {
        this.classList = res;
      });
    },
    //获取任教学科
    getCourse(e) {},
    //设置学校时增加年级按钮操作
    addGrade() {
      if (this.school[0].stageId != '' && this.school[0].gradeId != '') {
        this.school.push({
          classId: [],
          courseId: '',
        });
        this.gradeNum++;
      } else {
        this.$modal.msgWarning('请选择学校和年级！');
      }
    },
    //保存学校设置
    saveSchool() {
      if (this.school[0].stageId != '' && this.school[0].gradeId != '') {
        //数据合并
        let datas = {
          stageId: this.school[0].stageId,
          teacherId: this.role.teacher_id,
          userId: this.role.user_id,
          courseVos: [],
        };
        this.school.map((item) => {
          datas.courseVos.push({
            gradeId: this.school[0].gradeId,
            courseId: item.courseId,
            classId: item.classId,
          });
        });
        editCourse(datas).then((res) => {
          this.$modal.msgSuccess('设置成功');
          //获取角色
          this.$store.dispatch('GetRole');
          this.showSchool = false;
          this.gradeNum = 1;
          this.school = [
            {
              stageId: '',
              classId: '',
              gradeId: '',
              courseId: '',
            },
          ];
          setTimeout(() => {
            this.getTeacherCourse();
          }, 1000);
        });
      } else {
        this.$modal.msgWarning('请选择学校和年级！');
      }
    },
    //获取学生信息
    getStudentInfo() {
      getInfoByidcard({ idcard: this.student.idCard }).then((res) => {
        this.student = {
          idCard: this.student.idCard,
          stageId: '',
          classId: '',
          gradeId: '',
          courseId: '',
          studentInfo: res.data,
        };
        this.schoolList = [];
        this.gradeList = [];
        if (res.data) {
          this.modify = false;
          this.student.userName = res.data.userName;
          this.student.stageNmae = res.data.baseStage.fullName;
          //通过校id查询年级
          this.getGard(res.data.baseStage.stageId);
        } else {
          this.modify = true;
          this.$modal.msgWarning('身份证号无关联学生，请重新输入！');
        }
      });
    },
    //添加关联学生
    saveStudent() {
      if (this.student.studentInfo) {
        if (
          _.isEmpty(this.student.studentInfo.baseClass) == false &&
          this.student.studentInfo.baseClass.classId == this.student.classId
        ) {
          // console.log(this.student.studentInfo);
        } else {
          this.$modal.msgWarning('班级错误，请重新选择！');
        }
      } else {
        if (_.isEmpty(this.student.idCard)) {
          this.$modal.msgWarning('请输入身份证号！');
        }
      }
    },
    async getCourseList(e) {
      console.log('e::::'+e);
      let { rows: list } = await getCourseList({ gradeLevel: e.period, stageId: e.stageId });
      this.courseList = list;
    },
    //修改任教学校
    editClass(item) {
      // console.log(item.class);
      this.editSchool.stageId = item.stageId;
      this.getGard(item.stageId, item.class.grade_id);
      this.editSchool.gradeId = item.class.grade_id;

      this.editSchool.classId = item.class.class_id;
      this.editSchool.graphy = item.class.graphy;
      this.editSchool.userId = this.role.user_id;
      this.editSchool.teacherId = item.teacherId;
      this.editSchool.teacherCourseId = item.class.teacher_course_id;

      //editTeacherCourse(this.editSchool);

      this.showEditSchool = true;
    },
    editTeacherCourse() {
      // console.log('this.editSchool::::'+JSON.stringify(this.editSchool));
      editTeacherCourse(this.editSchool).then((res) => {
        if (res.code == 200) {
          this.$modal.msgSuccess('修改成功');
          this.$store.dispatch('GetRole');
          this.showEditSchool = false;
          setTimeout(() => {
            this.getTeacherCourse();
          }, 1000);
        }
      });
    },
    //删除任教学校
    delClass(item) {
      // console.log(item);
      this.$modal.confirm('是否删除班级？').then(() => {
        delTeacherCourse(item.class.teacher_course_id).then((res) => {
          this.$modal.msgSuccess('删除成功');
          //this.subList.splice(index, 1);
          this.$store.dispatch('GetRole');
          setTimeout(() => {
            this.getTeacherCourse();
          }, 1000);
        });
      });
    },
    //添加兴趣爱好
    addHobby() {
      if (this.hobbys.length > 0) {
        addHobby({ hobby: this.hobbys.toString() }).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('添加成功');
            this.showHobby = false;
            this.$store.dispatch('GetInfo');
          }
        });
      } else {
        this.$modal.msgWarning('请选择兴趣爱好');
      }
    },
    delHobby(name) {
      let res = this.dict.type['sys_hobby'].filter(
        (item) => item.label == name
      );
      if (res.length > 0) {
        delHobby({ hobby: res[0].value }).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('删除成功');
            this.$store.dispatch('GetInfo');
          }
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-form-item__label {
  font-size: 15px;
}
::v-deep .el-form-item__content {
  font-size: 15px;
  font-weight: 700;
  opacity: 0.8;
}
.schoolBox {
  .schoolDialog {
    background-color: #fafafa;
    border: 1px #fafafa solid;
    padding: 0 20px;

    .el-select {
      width: 100%;
    }
  }
  .add {
    width: 80%;
    margin: 20px auto;
  }
}

.container {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  .box {
    border-radius: 0.6rem;
    margin: 1rem 0;
    padding: 2rem;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .baseInfo {
      display: flex;
      justify-content: space-around;
      width: 100%;
      padding: 2rem;
    }
    .account {
      width: 100%;
      text-align: left;
      display: flex;
      font-size: 1.5rem;
      > div {
        padding: 20px;
        margin-right: 100px;
      }
    }
    .roles {
      display: block;
      font-size: 1.5rem;
      .item {
        padding-bottom: 5px;
        display: inline-block;
        margin-right: 10px;
      }
    }
    .students {
      display: block;
      padding: 10px 0 0 15px;
      .warp {
        // width: 500px;
        display: flex;

        font-size: 1.5rem;
        display: grid;
        grid-template-columns: 18% 80%;
        > div {
          margin-bottom: 10px;
        }
      }
    }
    .hobbies {
      display: block;
      padding: 0 2rem;
      .warp {
        font-size: 1.5rem;
        display: grid;
        grid-template-columns: repeat(8, 10%);
        i {
          cursor: pointer;
        }
        > div {
          margin-top: 20px;
        }
      }
    }
    .classes {
      display: flex;
      flex-direction: column;
      .item {
        display: flex;
        align-items: center;
        justify-content: space-between;

        font-size: 1.5rem;
      }
    }
    .schools {
      display: grid;

      .item {
        background-color: #f6fafb;
        margin-bottom: 10px;
      }
      .title {
        font-size: 1.5rem;
        opacity: 0.6;
        font-weight: 500;
      }
      .name {
        font-size: 1.5rem;
        opacity: 0.8;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .classes {
        padding: 10px;
        height: 103px;

        // overflow: scroll;
      }
      .bar {
        border-top: 1px #dee1e2 solid;
        padding: 5px;
        border-radius: 6px;
        display: flex;
        button {
          flex: 1;
          height: 40px;
          border: none;
        }
      }
      grid-template-columns: repeat(3, 30%);
    }
  }
}
</style>
