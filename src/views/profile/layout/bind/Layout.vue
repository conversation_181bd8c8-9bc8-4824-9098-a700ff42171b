<template>
  <div>
    <Header></Header>
    <div class="content">
      <div class="warp">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script>
import Header from '@/views/profile/layout/bind/Header';
export default {
  name: 'HomeLayout',
  components: {
    Header,
  },
  data: () => {
    return {};
  },
  created() {
    // console.dir(this.$route);
    // console.log('this.$route.query.tokenloyout::::'+this.$route.query.token);

  },
  methods: {},
};
</script>

<style scoped lang="scss">
.content {
  background-color: #f6fdfc;
  min-height: 64.8vh;
  .warp {
    width: 100%;
    margin: 0 auto;
    //padding-bottom: 5px;
  }
}
</style>
