<template>
  <div>
    <!-- 导航-->
    <div class="header">
      <!--      <div-->
      <!--        class="crumbs"-->
      <!--        @click="goHome">-->
      <!--        <i class="el-icon-arrow-left"></i>-->
      <!--        返回空间-->
      <!--      </div>-->
      <div class="logo">
        武侯智汇云
        <img src="../../../assets/logo_white.png"/>
        教育数字芯
      </div>
      <div class="login">
        <div class="account">
          <!--          <div-->
          <!--            @click="login"-->
          <!--            v-if="_.isEmpty(userInfo)">-->
          <!--            &lt;!&ndash;          请登录您的账号&ndash;&gt;-->
          <!--            登录-->
          <!--          </div>-->
          <!--          <div-->
          <!--            v-else-->
          <!--            class="navigation">-->
          <div class="navigation">
            <div
                v-show="showRole"
                class="switch"
                v-if="roles.length > 1">
              <el-dropdown trigger="click">
                <span class="el-dropdown-link">
                  {{ role.child_name }}{{ role.role_name }}（{{
                    role.full_name
                  }}）
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                      v-for="(item, index) in roles"
                      :key="index"
                      @click.native="switchHandle(item)">
                    {{ item.child_name }}{{ item.role_name }}（{{
                      item.full_name
                    }}）
                    <!--                  {{ item.full_name }}{{ item.role_name }}-->
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <div
                v-else
                class="p_20"
                style="cursor: default">
              {{ role.full_name }}
            </div>
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                <img
                    :src="avatar"
                    class="avatar"/>
                {{ name }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <!--                <el-dropdown-item @click.native="$router.push('/personal')">-->
                <!--                  个人中心-->
                <!--                </el-dropdown-item>-->
                <el-dropdown-item @click.native="logout">
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {mapGetters} from 'vuex';
import {clear, set} from "@/utils/local";

export default {
  name: 'ProHeader',
  components: {
    // Tabs,
  },
  computed: {
    ...mapGetters([
      'role',
      'name',
      'userInfo',
      'avatar',
      'role',
      'roles',
      'defRole',
    ]),
  },
  data: () => {
    return {
      showRole: true
    };
  },
  async created() {
    // console.log('this.$route.query.token::::'+this.$route.query.token);

  },
  methods: {
    //切换角色
    switchHandle(item) {
      // this.$store.dispatch('SetRole', item);
    },
    logout() {
      this.$modal
          .confirm('是否退出登录')
          .then(() => {
            this.$store.dispatch('LogOut');
            //this.$router.push('/');
            window.location.href = "https://cdwh.wuhousmartedu.com/whIndexNew.html";
          })
          .catch(() => {
          });
    },
  },
};
</script>

<style scoped lang="scss">
.header {
  height: 60px;
  display: flex;
  align-items: center;
  min-width: 1300px;
  background-color: #3b8989;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  position: relative;
  color: #fff;

  .logo {
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 3.8rem;
    letter-spacing: 0.2rem;
    display: flex;
    align-items: center;

    img {
      height: 4rem;
      padding: 0 1rem;
    }
  }

  .crumbs {
    position: absolute;
    left: 38px;

    height: 80px;
    display: flex;

    align-items: center;
    font-weight: 700;
    font-size: 1.8rem;
    cursor: pointer;
    font-family: PingFangSC;
  }

  .login {
    position: absolute;
    right: 0;
    // width: 250px;
    height: 80px;
    margin-right: 40px;
    display: flex;

    align-items: center;

    .account {
      font-weight: 700;
      font-size: 1.5rem;
      font-family: PingFangSC;
    }
  }

  justify-content: center;

  .el-dropdown-link {
    color: #0f4444;
    font-size: 1.5rem;
    line-height: 1.5rem;
    display: flex;
    justify-content: space-between;
    justify-items: center;
    align-items: center;

    img {
      padding: 0 0.8rem;
      width: 3rem;
      height: 3rem;
    }
  }

  .navigation {
    display: flex;
    align-items: center;
    cursor: pointer;

    .switch {
      margin-right: 10px;
      border-radius: 20px;
      border: 1px #0f4444 solid;
      text-align: center;
      padding: 3px 10px;
    }
  }
}
</style>
