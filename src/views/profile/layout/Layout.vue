<template>
  <div>
    <Header></Header>
    <div class="content">
      <div class="warp">
        <router-view></router-view>
      </div>
    </div>
    <Footer></Footer>
  </div>
</template>

<script>
import Header from '@/views/profile/layout/Header';
import Footer from '@/views/layout/Footer';
export default {
  name: 'HomeLayout',
  components: {
    Header,
    Footer,
  },
  data: () => {
    return {};
  },
  created() {
    // console.dir(this.$route);
    // console.log('this.$route.query.tokenloyout::::'+this.$route.query.token);

  },
  methods: {},
};
</script>

<style scoped lang="scss">
.content {
  background-color: #f6fdfc;
  min-height: 64.8vh;
  .warp {
    width: 1200px;
    margin: 0 auto;
    padding-bottom: 50px;
  }
}
</style>
