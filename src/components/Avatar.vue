<template>
  <div :class="$options.name">
    <el-upload
        class="upload-el"
        accept="image/*"
        ref="fileUpload"
        name="pic"
        :action="action"
        :data="uploadData"
        :on-change="selectChange"
        :show-file-list="false"
        :auto-upload="false"
        :http-request="httpRequest">
      <img
          :style="cssWidth"
          v-if="defaultImg || resultImg"
          :src="resultImg ? resultImg : defaultImg"
          :class="width ? 'custom' : 'avatar'"/>
      <i
          v-else
          class="el-upload el-icon-plus avatar-uploader-icon"></i>
      <!-- <div>
        <span class="icon upload-icon" />
        <el-button>选择图片</el-button>
      </div> -->
      <div
          slot="tip"
          class="el-upload__tip">
        图片大小不超过5M
      </div>
    </el-upload>
    <!-- <figure
      v-show="resultImg"
      class="result-img">
      <img :src="resultImg" />
      <el-button @click="updateCropper">重新上传</el-button>
    </figure> -->
    <cropper
        v-if="showCropper"
        :dialog-visible="showCropper"
        :cropper-img="cropperImg"
        @update-cropper="updateCropper"
        @colse-dialog="closeDialog"
        @upload-img="uploadImg"/>
  </div>
</template>

<script>
import Cropper from './Cropper.vue';
import {Loading} from 'element-ui';
import axios from 'axios';
import {getSessionToken} from "@/utils/local";

export default {
  name: 'UploadImg',
  components: {
    Cropper,
  },
  props: {
    defaultImg: [],
    width: [],
  },
  computed: {
    cssWidth() {
      return {
        '--width': this.width,
      };
    },
  },
  data() {
    return {
      uploadData: {
        // 上传需要的额外参数
        siteId: 1,
        source: 1,
      },
      action: '/api/system/user/profile/avatar', // 上传地址，必填
      cropperImg: '', // 需要裁剪的图片
      showCropper: false, // 是否显示裁剪框
      uploadFile: '', // 裁剪后的文件
      resultImg: '', // 上传成功，后台返回的路径
      fileInfo: '', //文件信息
    };
  },
  methods: {
    // submit 之后会触发此方法
    httpRequest() {
      //const { action, data, filename } = request;
      // 新建formDate对象
      let formData = new FormData();
      // for (let key in data) {
      //   formData.append(key, data[key]);
      // }
      // 文件单独push,第三个参数指定上传的文件名
      formData.append(
          'avatarfile',
          this.uploadFile,
          this.fileInfo.name.split('.')[0] + '.jpg'
          //request.file.name.split('.')[0] + '.jpg'
      );
      let loadingInstance = Loading.service({fullscreen: true});
      axios({
        headers: {
          Authorization: 'Bearer ' + getSessionToken(),
          'Content-Type': 'multipart/form-data', // 需要指定上传的方式
        },
        // content-Type:'application/octet-stream',
        url: process.env.VUE_APP_BASE_API + '/system/user/profile/avatar',
        method: 'post',
        data: formData,
        timeout: 200000000, // 防止文件过大超时
      })
          .then((result) => {
            loadingInstance.close();

            let res = result.data;
            if (res.code == 200) {
              this.$message.success('图片上传成功');
              this.$emit('success', res);
              this.showCropper = false;
              this.resultImg = res.ossFileUrl; // 上传成功后展示的图片
            } else {
              this.$message.error(res.msg || '网络错误');
            }
          })
          .catch((err) => {
            loadingInstance.close();
            console.log(err);
          });
    },
    // 选择文件
    selectChange(file) {
      console.log(file);
      this.fileInfo = file;
      const {raw} = file;
      this.openCropper(raw);
    },
    /**
     * @param {file} 上传的文件
     */
    openCropper(file) {
      var files = file;
      let isLt5M = files.size > 5 << 20;
      if (isLt5M) {
        this.$message.error('请上传5M内的图片');
        return false;
      }
      var reader = new FileReader();
      reader.onload = (e) => {
        let data;
        if (typeof e.target.result === 'object') {
          // 把Array Buffer转化为blob 如果是base64不需要
          data = window.URL.createObjectURL(new Blob([e.target.result]));
        } else {
          data = e.target.result;
        }
        this.cropperImg = data;
        this.showCropper = true;
      };
      // 转化为base64
      // reader.readAsDataURL(file)
      // 转化为blob
      reader.readAsArrayBuffer(files);
    },
    // 上传图片
    uploadImg(file) {
      console.log(22223335555);
      this.uploadFile = file;
      this.httpRequest();
      //this.$refs.fileUpload.submit();
    },
    // 更新图片
    updateCropper() {
      this.$refs.fileUpload.$children[0].$el.click();
    },
    // 关闭窗口
    closeDialog() {
      this.showCropper = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.UploadImg {
  .video-image {
    display: flex;

    figure {
      width: 100px;

      img {
        width: 100%;
        display: block;
      }
    }
  }
}

::v-deep.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  line-height: 128px;
  text-align: center;
}

.el-upload__tip {
  // text-align: center;
  // border: 1px #000 solid;
}

.custom {
  width: var(--width);
}

.avatar {
  width: 128px;
  height: 128px;
  display: block;
}
</style>
