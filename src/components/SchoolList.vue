
<template>
  <div class="pointer" @click="gotoDetail">
    <img :src="info.status!='0' ? info.agencyLogo : image" />
    <div class="flex-content">
      <div ref="school" class="title">{{info.fullName}}</div>
      <div class="introduce">
        <div class="span">简介:</div>
        <div class="intro">{{info.introduce}}</div>
<!--        <div class="intro">是一所隶属于武侯区教育局的全民事业公办学校，地处成都市西南川藏路旁，座落于风景秀丽的江安河畔，紧邻双流国际机场和著名的西部鞋都；学校创建于1969年，1996年行政区划调整由双流县划归武侯区，现占地22亩，全校教职工75人，在校学生1294人；-->
<!--          校园环境优雅，硬件设施完善，学习氛围浓厚；教学楼、实验室错落有致；花台、假山点缀其间；拥有大型微机室，多媒体教室，-->
<!--          是成都市首批农村现代信息技术远程教育项目学校。</div>-->
      </div>
    </div>
  </div>
</template>
<script>
import image from '@/assets/images/schoolLogo.png'
export default{
  name: "SchoolList",
  data(){
    return {
      image
    }
  },
  props:{
    info:{}
  },

  methods:{
    gotoDetail(){
        this.$router.push({
          path:'/zhili/organization/detail',
          query:{
            id:this.info.agencyId
          }
        })
    },
  },
  created() {
  }
}
</script>

<style scoped lang="scss">
.pointer{
  position: relative;
  margin:1rem 1rem;
  height:316px;
  width: 360px;
  border-radius:6px;
  background-color: #fff;
  box-shadow: 2px 2px 2px #d3d7d4;
  cursor:pointer;
  img{
    width: 360px;
    border-radius:6px;
  }
  .flex-content{
    margin:0 10px 0 10px;
    .title{
      font-size:18px;
      font-weight: 700;
    }
    .introduce{
      display:flex;
      margin-top:8px;
      .span{
        font-size:15px;
        height:40px;
        width:40px;
      }
      .intro{
        height: 40px;
        width:320px;
        font-size: 15px;
        font-weight: 600;
        background-color:#fff;
        opacity: .8;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }

}
</style>