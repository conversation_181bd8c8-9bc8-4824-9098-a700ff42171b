<template>
  <div>
    <el-cascader
      v-if="show"
      :options="tree"
      :props="props"
      collapse-tags
      :show-all-levels="showAllLevels"
      :clearable="clearable"
      placeholder="全部年级"
      @change="change"
      v-model="datas"></el-cascader>
  </div>
</template>

<script>
import { getGradeTree } from '@/api/youjiao';
import { mapGetters } from 'vuex';
export default {
  data: () => {
    return {
      props: {
        multiple: true,
        checkStrictly: true,
        value: 'id',
        emitPath: true,
      },
      tree: [],
      show: false,
      datas: [], //初始化参数
    };
  },
  props: {
    stageId: '',
    treeData: '',
    classId: '',
    classes: '',
    gradeId: '',
    emitPath: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    checkStrictly: {
      type: Boolean,
      default: true,
    },
    showAllLevels: {
      type: <PERSON>olean,
      default: true,
    },
    level: { type: Number, default: 2 },
  },
  watch: {
    show(val) {
      this.datas = [];

      if (val) {
        if (!_.isEmpty(this.school.gradeId)) {
          console.log(1);
          this.datas.push(this.school.gradeId);
        }
        if (!_.isEmpty(this.school.classId)) {
          console.log(2);
          this.datas.push(this.school.classId);
        }
        if (this.classId) {
          console.log(3);
          this.datas = [this.gradeId, this.classId];
        }
        if (this.classes) {
          console.log(4);
          console.log(this.classes);
          this.datas = [this.classes[1], this.classes[2]];
        }
      }
    },
  },
  async created() {
    this.props.emitPath = this.emitPath;
    this.props.multiple = this.multiple;
    this.props.checkStrictly = this.checkStrictly;
    // //清空缓存
    // this.$store.dispatch('SetGradeId', '');
    // this.$store.dispatch('SetClassId', '');
    if (this.treeData) {
      this.tree = this.getData(this.treeData);

      this.show = true;
      return false;
    }
    if (this.stageId) {
      let tree = await getGradeTree({ stageId: this.stageId });

      this.tree = this.getData(tree);
      this.show = true;
    }
  },
  computed: {
    ...mapGetters(['school', 'role']),
  },
  methods: {
    change(e) {
      let res = '';
      if (this.emitPath) {
        res = this.getDataById(this.tree, e[1]);
      } else {
        res = this.getDataById(this.tree, e);
      }

      this.$emit('change', e, res);
    },
    getDataById(data, id) {
      let queue = [];
      for (let item of data) {
        queue.push(item);
        while (queue.length) {
          let top = queue.shift();
          if (top.id == id) {
            return top;
          }
          if (top.children) {
            queue.push(...top.children);
          }
        }
      }
    },
    //获取学校数据
    async getGrade(id) {
      this.show = false;
      let tree = await getGradeTree({ stageId: id });
      this.tree = this.getData(tree);
      this.show = true;
    },
    getData(data) {
      if (this.level == 1) {
        for (var i = 0; i < data.length; i++) {
          data[i].children = undefined;
        }
      }
      // else {

      //   // for (var i = 0; i < data.length; i++) {
      //   //   if (data[i].children.length < 1) {
      //   //     data[i].children = undefined;
      //   //   } else {
      //   //     this.getData(data[i].children);
      //   //   }
      //   // }
      // }

      return data;
    },
  },
};
</script>

<style>
.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  color: #3b8989;
}
.el-cascader-node.in-active-path {
  background-color: rgba(63, 139, 137, 0.1);
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  //background-color: #3b8989;
  //border-color: #3b8989;
}
</style>
