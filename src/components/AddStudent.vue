<!-- 新增学生 -->
<template>
  <div v-loading="loading">
    <!-- 新增学生-->
    <MyDialog
      dialogWidth="550px"
      @confirm="$refs.student.getFormsDatas()"
      @close="(showMessageError = false), $emit('close')"
      :title="dialogState == 'add' ? '新增学生' : '修改学生'">
      <div class="schoolDialog">
        <MyForms
          ref="student"
          :key="componentKey"
          :columns="studentDatas"
          editable
          :modify="modify"
          :dialogState="dialogState"
          labelWidth="142px"
          @change="getStudentInfo"
          :showMessageError="showMessageError"
          @formsDatas="saveStudent"
          :classes="classes"></MyForms>

        <!-- 监护人信息-->
        <el-form
          label-width="140px"
          :model="{ elders }"
          ref="elder">
          <div
            v-for="(item, index) in elders"
            class="elders">
            <el-divider>监护人{{ index + 1 }}信息</el-divider>
            <div class="flex just_center m_b_30">
              <el-button
                class="edit_btn"
                @click="addGuardian">
                添加监护人
              </el-button>
              <el-button
                v-if="index > 0"
                class="edit_btn"
                @click="delGuardian(index)">
                删除监护人
              </el-button>
            </div>
            <el-form-item
              :label="`监护人(${index + 1})姓名:`"
              :prop="`elders.${index}.userName`"
              :rules="{
                required: true,
                message: '请填写监护人姓名',
                trigger: 'blur',
              }">
              <el-input
                v-model="item.userName"
                placeholder="请填写监护人姓名"></el-input>
            </el-form-item>
            <el-form-item
              :label="`监护人(${index + 1})证件类型:`"
              :prop="`elders.${index}.idcardType`">
              <el-select
                v-model="item.idcardType"
                placeholder="请选择证件类型">
                <el-option
                  v-for="dict in dict.type['idcard_type']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              :label="`监护人(${index + 1})证件号:`"
              :prop="`elders.${index}.idcard`">
              <el-input
                v-model="item.idcard"
                placeholder="请填写证件号"></el-input>
            </el-form-item>
            <el-form-item
              :label="`监护人(${index + 1})手机:`"
              :prop="`elders.${index}.phone`"
              :rules="{
                required: true,
                message: '请填写监护人手机',
                trigger: 'blur',
              }">
              <el-input
                v-model="item.phone"
                placeholder="请填写手机号"></el-input>
            </el-form-item>
            <el-form-item
              :label="`监护人(${index + 1})身份:`"
              :prop="`elders.${index}.kinship`"
              :rules="{
                required: true,
                message: '请选择监护人关系',
                trigger: 'blur',
              }">
              <el-select
                v-model="item.kinship"
                placeholder="请选择监护人身份">
                <el-option
                  v-for="dict in dict.type['kinship_type']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="item.kinship == 99"
              :label="`亲属(${index + 1})关系备注:`"
              :prop="`elders.${index}.kinshipOther`"
              :rules="{
                required: true,
                message: '请填写监护人关系',
                trigger: 'blur',
              }">
              <el-input
                placeholder="请填写关系"
                v-model="item.kinshipOther"></el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </MyDialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getInfoByidcard, getStudentInfo } from '@/api/youjiao';
import MyDialog from '@/components/MyDialog.vue';
import MyForms from '@/components/MyForms';
export default {
  dicts: ['idcard_type', 'kinship_type'],
  components: {
    MyDialog,
    MyForms,
  },
  data() {
    return {
      loading: false,
      modify: false,
      componentKey: 1,
      classes: '', //学生班级信息
      studentDatas: [
        {
          prop: 'userId',
          type: 'hidden',
          default: null,
          required: false,
        },
        {
          prop: 'studentId',
          type: 'hidden',
          default: null,
          required: false,
        },
        {
          prop: 'headIcon',
          label: '学生头像',
          placeholder: '请上传学生头像',
          type: 'photo',
          default: '',
          required: false,
        },
        {
          prop: 'idcardType',
          label: '证件类型',
          placeholder: '请选择证件类型',
          type: 'dict',
          dictType: 'idcard_type',
          options: [],
          default: '',
          required: true,
        },
        {
          prop: 'idcard',
          label: '证件号码',
          placeholder: '请输入证件号码',
          type: 'idcard',
          default: '',
          required: true,
        },

        {
          prop: 'userName',
          label: '学生姓名',
          placeholder: '请输入学生姓名',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'sex',
          label: '学生性别',
          placeholder: '请选择学生性别',
          type: 'dict',
          dictType: 'sys_user_sex',
          multiple: false,
          default: '',
          options: [],
          required: false,
        },

        {
          prop: 'phone',
          label: '手机号码',
          placeholder: '请输入手机号码',
          type: 'input',
          default: '',
          required: false,
        },
        {
          prop: 'email',
          label: '邮箱号码',
          placeholder: '请输入邮箱号码',
          type: 'input',
          default: '',
        },
        {
          prop: 'nation',
          label: '民族',
          placeholder: '请选择民族',
          type: 'dict',
          dictType: 'user_nation',
          default: '',
          required: true,
        },
        {
          prop: 'birthday',
          label: '生日',
          placeholder: '请选择日期',
          type: 'date',
          default: '',
          required: false,
        },
      ],
      //学生监护人信息
      elders: [
        {
          userName: '',
          phone: '',
          kinship: '',
          kinshipOther: '',
          idcardType: '',
          idcard: '',
          isGuardian: 1,
        },
      ],
    };
  },
  props: {
    showMessageError: '',
    userInfo: '',
    dialogState: '',
    showSchool: {
      default: false,
      type: Boolean,
    },
  },
  computed: {
    ...mapGetters(['role']),
  },
  async created() {
    if (this.showSchool) {
      this.studentDatas.push({
        prop: 'stageId',
        label: '所在年级、所在班级',
        placeholder: '请选择所在年级、所在班级',
        type: 'school',
        default: '',
      });
    }
    //修改用户
    if (this.dialogState != 'add') {
      this.modify = false;
      //获取学生信息
      this.loading = true;
      const { data } = await getStudentInfo(this.userInfo.studentId);
      //将学生信息存入临时表
      this.$store.dispatch('SetTemporary', data.userId);
      this.studentDatas.forEach((item) => {
        if (item.prop == 'headIcon') {
          item.default = this.userInfo.headIcon;
        } else {
          item.default = data[item.prop];
        }
      });

      this.classes = [data.stageId, data.gradeId, data.classId];
      console.log(this.classes);
      this.elders = data.elders;
      this.componentKey += 1;
      this.loading = false;
    } else {
      this.classes = '';
    }
  },
  methods: {
    addGuardian() {
      this.elders.push({
        userName: '',
        phone: '',
        kinship: '',
        kinshipOther: '',
        idcardType: '',
        idcard: '',
        isGuardian: 1,
      });
    },
    delGuardian(index) {
      this.elders.splice(index, 1);
    },
    saveStudent(r) {
      this.$refs.elder.validate((valid) => {
        if (valid) {
          //判断手机号
          let ary = [];
          this.elders.map((item) => {
            ary.push(item.phone);
          });
          var nary = ary.sort();
          let rr = true;
          for (var i = 0; i < ary.length; i++) {
            if (nary[i] == nary[i + 1]) {
              rr = false;
            }
          }
          if (rr) {
            r.elders = this.elders;
            //编辑学生时不传头像
            if (this.dialogState != 'add') {
              delete r.headIcon;
            }
            this.$emit('studentInfo', r);
          } else {
            this.$modal.msgWarning('手机号重复，请重新输入');
          }
        }
      });
    },
    //通过身份证获取学生数据
    getStudentInfo(e) {
      this.modify = false;
      this.loading = true;

      getInfoByidcard({ idcard: e }).then((res) => {
        this.componentKey += 1;
        this.loading = false;
        if (res.data) {
          this.showMessageError = false;
          //重组数据
          this.studentDatas.forEach((item) => {
            item.default = res.data[item.prop];
          });
          this.elders = res.data.elders;
        } else {
          this.showMessageError = true;
          this.studentDatas.forEach((item) => {
            if (item.prop == 'idcard') {
              item.default = e;
            } else {
              if (item.prop != 'idcardType') {
                item.default = '';
              }
            }
          });
          this.elders = {
            userName: '',
            phone: '',
            kinship: '',
            kinshipOther: '',
            idcardType: '',
            idcard: '',
            isGuardian: 1,
          };
        }
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
