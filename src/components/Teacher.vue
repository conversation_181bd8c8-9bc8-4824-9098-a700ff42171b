<template>
  <div class="warp">
    <!-- 设置校区-->
    <MyDialog
      v-if="campusDialog"
      @close="campusDialog = false"
      title="设置校区"
      dialogWidth="350px"
      @confirm="$emit('set', 5, userInfo.userId, selectStage)">
      <div class="select">
        <el-form>
          <!-- <el-form-item label="当前校区:"></el-form-item> -->
          <el-form-item label="选择校区:">
            <el-select
              :multiple="false"
              v-model="selectStage">
              <el-option
                v-for="item in stageList"
                :label="item.fullName"
                :value="item.stageId"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </MyDialog>
    <MyDialog
      v-if="showDialog"
      @close="(showDialog = false), closeDialog()"
      dialogWidth="380px"
      @confirm="$emit('set', type, userInfo.teacherId, ids)"
      :title="
        type == 2
          ? '设置年级组长'
          : type == 3
          ? '设置班主任'
          : type == 4
          ? '设置副班主任'
          : ''
      ">
      <el-form>
        <el-form-item label="教师角色信息:">
          <br />
          <div
            class="flex just_around"
            v-for="(item, index) in roles"
            :key="index">
            <div>
              {{ index + 1 + '、' + item.grade_name + item.role_sub_name }}
            </div>
            <div>
              <el-button
                plain
                class="edit_btn_text"
                @click="unset(item, index)">
                删除
              </el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="请选择设置:">
          <div class="select">
            <SchoolOrg
              :stageId="school.stageId"
              :level="type == 2 ? 1 : 2"
              @change="ids = $event"
              :checkStrictly="false"></SchoolOrg>
          </div>
        </el-form-item>
      </el-form>
    </MyDialog>
    <div v-if="userInfo.advisers.split(',').length == 1">
      <div
        class="type1"
        v-show="userInfo.isAdmin == 1">
        管理员
      </div>
      <div
        class="type2"
        v-show="userInfo.advisers == 3">
        年级组长
      </div>
      <div
        class="type3"
        v-show="userInfo.advisers == 1 || userInfo.advisers == 2">
        {{ userInfo.advisers == 1 ? '班主任' : '副班主任' }}
      </div>
    </div>
    <div v-else>
      <div
        class="type1"
        v-show="userInfo.isAdmin == 1">
        管理员
      </div>
      <div class="type4">
        {{
          userInfo.advisers
            .split(',')
            .filter((item) => item != 0)
            .map((item) => {
              if (item == 1) {
                return '班主任';
              }
              if (item == 2) {
                return '副班主任';
              }
              if (item == 3) {
                return '年级主长';
              }
            })
            .toString()
        }}
      </div>
    </div>
    <div class="flex just_between p_20">
      <div class="phtot">
        <img
          :src="
            userInfo.headIcon
              ? userInfo.headIcon
              : userInfo.sex == 0
              ? male
              : female
          " />
      </div>
      <div class="base fs_15">
        <div
          class="fs_20 opacity_8 fw_700 ellipsis"
          :title="userInfo.userName">
          {{ userInfo.userName }}
        </div>
        <div>
          <span class="opacity_6">电话：</span>
          {{ userInfo.desPhone }}
        </div>
        <div class="flex">
          <span class="opacity_6">性别：</span>
          <dict-tag
            :options="dict.type['sys_user_sex']"
            :value="userInfo.sex" />
        </div>
      </div>
    </div>

    <div
      class="flex just_around"
      style="border-radius: 5px; border-top: 1px #e6e6e6 solid">
      <el-button
        class="edit_btn_text"
        @click="edit">
        编辑
      </el-button>
      <el-popover
        placement="right"
        width="200"
        trigger="click">
        <div class="set_btn">
          <!-- <el-button
            type="primary"
            @click="set(1)">
            设置副管理员
          </el-button> -->
          <el-button
            type="primary"
            @click="set(2)">
            设置年级组长
          </el-button>
          <el-button
            type="primary"
            @click="set(3)">
            设置班主任
          </el-button>
          <el-button
            type="primary"
            @click="set(4)">
            设置副班主任
          </el-button>
          <el-button
            type="primary"
            @click="set(5)">
            设置校区
          </el-button>
        </div>
        <el-button
          slot="reference"
          @click="visible = !visible"
          class="edit_btn_text">
          设置
        </el-button>
      </el-popover>

      <el-button
        class="del_btn_text"
        @click="del">
        删除
      </el-button>
      <el-button
        class="edit_btn_text"
        @click="reset">
        密码重置
      </el-button>
    </div>
  </div>
</template>

<script>
import male from '@/assets/avatar/maleTeacher.png';
import female from '@/assets/avatar/femaleTeacher.png';
import SchoolOrg from '@/components/SchoolOrg';
import MyDialog from '@/components/MyDialog';
import { teacherRoleList, editClassMange, editGradeMange } from '@/api/youjiao';
import { resetPwd } from '@/api/login';
import { mapGetters } from 'vuex';
export default {
  dicts: ['sys_user_sex'],
  data: () => {
    return {
      male,
      female,
      type: '',
      showDialog: false,
      visible: false,
      campusDialog: false,
      ids: [],
      roles: [],
      selectStage: [],
      isEdited: false,
    };
  },
  components: {
    SchoolOrg,
    MyDialog,
  },
  computed: {
    ...mapGetters(['school', 'role']),
  },

  props: {
    userInfo: [],
    stageList: [],
  },
  methods: {
    del() {
      this.$modal.confirm('是否删除？').then(() => {
        this.$emit('del', this.userInfo.teacherId);
      });
    },
    edit() {
      this.$emit('edit', this.userInfo);
    },

    //设置班主任
    set(type) {
      this.type = type;
      this.ids = [];
      if (type == 2 || type == 3 || type == 4) {
        this.getTeacherRoles(type == 2 ? 3 : type == 3 ? 1 : 2);
        this.showDialog = true;
      } else {
        this.campusDialog = true;
      }
    },
    //获取教师角色
    async getTeacherRoles(type) {
      const { data } = await teacherRoleList({
        stageId: this.school.stageId,
        userId: this.userInfo.userId,
        adviser: type,
      });
      this.roles = data;
    },
    //取消设置
    unset(item, index) {
      //年级主任
      if (item.adviser == 3) {
        this.$modal.confirm('是否删除？').then(() => {
          editGradeMange({
            teacherId: this.userInfo.teacherId,
            gradeId: item.grade_id,
            stageId: item.stage_id,
          }).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess('删除成功');
              this.roles.splice(index, 1);
              this.isEdited = true;
            }
          });
        });
      }
      //副班主任or班主任
      if (item.adviser < 3) {
        this.$modal.confirm('是否删除？').then(() => {
          editClassMange({
            teacherId: this.userInfo.teacherId,
            gradeId: item.grade_id,
            stageId: item.stage_id,
            classId: item.class_id,
            editType: item.adviser,
          }).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess('删除成功');
              this.roles.splice(index, 1);
              this.isEdited = true;
            }
          });
        });
      }
    },
    closeDialog() {
      if (this.isEdited) {
        this.isEdited = false;
        this.$parent.getTeacherList();
      }
    },
    resetAll() {
      this.visible = false;
      this.showDialog = false;
      this.campusDialog = false;
    },
    //重置密码
    reset() {
      this.$modal.confirm('是否重置密码？').then(() => {
        resetPwd(this.userInfo.userId, 'teacher').then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('重置成功');
          }
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.select {
  padding-bottom: 30px;
}
.role {
  border: 1px #000 solid;
}
.set_btn {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .el-button {
    width: 100% !important;
    margin: 5px 0;
  }
}
.warp {
  width: 275px;
  height: 172px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
  position: relative;

  .type1 {
    position: absolute;
    right: -1px;
    width: 70px;
    height: 30px;
    border-radius: 0px 4px 4px 4px;
    border: 0px solid;
    font-size: 1.4rem;
    background-color: #35d073;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 5px;
  }
  .type1::after {
    content: '';
    border-right: 2px #35d073 solid;
    position: absolute;
    right: 0px;
    bottom: -2px;
    height: 20px;
    border-radius: 20px;
  }
  .type2 {
    position: absolute;
    right: -1px;
    width: 70px;
    height: 30px;
    border-radius: 0px 4px 4px 4px;
    border: 0px solid;
    font-size: 1.4rem;
    background-color: #ff8d4d;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 5px;
  }
  .type2::after {
    content: '';
    border-right: 2px #ff8d4d solid;
    position: absolute;
    right: 0px;
    bottom: -2px;
    height: 20px;
    border-radius: 20px;
  }
  .type3 {
    position: absolute;
    right: -1px;
    width: 70px;
    height: 30px;
    border-radius: 0px 4px 4px 4px;
    border: 0px solid;
    font-size: 1.4rem;
    background-color: #6a94ff;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 5px;
  }
  .type3::after {
    content: '';
    border-right: 2px #6a94ff solid;
    position: absolute;
    right: 0px;
    bottom: -2px;
    height: 20px;
    border-radius: 20px;
  }
  .type4 {
    position: absolute;
    right: -1px;
    // width: 70px;
    padding: 0 5px;
    height: 30px;
    border-radius: 0px 4px 4px 4px;
    border: 0px solid;
    font-size: 1.4rem;
    background-color: #6a94ff;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 5px;
  }
  .type4::after {
    content: '';
    border-right: 2px #6a94ff solid;
    position: absolute;
    right: 0px;
    bottom: -2px;
    height: 20px;
    border-radius: 20px;
  }
  img {
    width: 77px;
    height: 77px;
    object-fit: cover;
    border-radius: 50%;
  }
  .base {
    width: 150px;
    > div {
      line-height: 28px;
    }
  }
}
</style>
