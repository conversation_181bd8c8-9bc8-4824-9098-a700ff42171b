<template>
  <div v-loading="loading">
    <el-cascader
      v-if="show"
      :options="tree"
      :props="props"
      collapse-tags
      :show-all-levels="showAllLevels"
      clearable
      placeholder="请选择年级"
      @expand-change="changeHandle"
      v-model="datas"></el-cascader>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getStudentList, getGradeTree } from '@/api/youjiao';
export default {
  data: () => {
    return {
      loading: false,
      props: { multiple: true, checkStrictly: true, value: 'id' },
      tree: [],
      show: true,
      datas: [], //初始化参数
    };
  },
  props: {
    stageId: '',
    // classId: '',
    multiple: {
      type: Boolean,
      default: true,
    },
    checkStrictly: {
      type: Boolean,
      default: false,
    },
    showAllLevels: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    show(val) {
      this.datas = [];
      if (val) {
        if (!_.isEmpty(this.school.gradeId)) {
          this.datas.push(this.school.gradeId);
        }
        if (!_.isEmpty(this.school.classId)) {
          this.datas.push(this.school.classId);
          //当选择班级时查询学生信息合并原始数据
          //this.getStudents(this.school.classId);
        }
      }
    },
  },
  async created() {
    this.props.multiple = this.multiple;
    this.props.checkStrictly = this.checkStrictly;
    if (this.stageId) {
      this.loading = true;
      let tree = await getGradeTree({ stageId: this.stageId, haveStu: 1 });
      this.tree = this.getData(tree);
      this.loading = false;
    }
    // if (this.classId) {
    //   this.loading = true;
    //   let tree = await getGradeTree({ ...this.classId, haveStu: 1 });
    //   this.tree = this.getData(tree);
    //   this.loading = false;
    // }
  },
  computed: {
    ...mapGetters(['school']),
  },
  methods: {
    //获取学生列表
    async getStudents(id) {
      const { rows: list } = await getStudentList({
        classId: id,
        pageNum: 1,
        pageSize: 1000,
      });

      for (var i in this.tree) {
        this.tree[i].children.map((item) => {
          if (item.id == id && item.isUpdate != true) {
            item.isUpdate = true;
            item.children = list.map((item) => {
              return { id: item.studentId, label: item.userName };
            });
          }
        });
      }
    },
    //获取学校数据
    async getGrade(id) {
      this.show = false;
      let tree = await getGradeTree({ stageId: id });
      this.tree = this.getData(tree);
      this.show = true;
    },
    getFormsDatas() {
      this.$emit('getFormsDatas', this.datas);
    },
    getData(data) {
      // for (var i = 0; i < data.length; i++) {
      //   if (data[i].children.length < 1) {
      //     data[i].children = undefined;
      //   } else {
      //     this.getData(data[i].children);
      //   }
      // }
      return data;
    },
    changeHandle(e) {
      //this.getStudents(e.pop());
    },
  },
};
</script>

<style>
.el-cascader__tags {
  .el-tag__close {
    display: none !important;
  }
  .el-tag:nth-child(2) {
    display: none !important;
  }
}
.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  color: #3b8989;
}
.el-cascader-node.in-active-path {
  background-color: rgba(63, 139, 137, 0.1);
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  //background-color: #3b8989;
  //border-color: #3b8989;
}
</style>
