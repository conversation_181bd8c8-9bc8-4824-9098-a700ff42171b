<template v-loading="loading">
  <div>
    <MyDialog
      @close="$emit('close')"
      @confirm="$emit('close')"
      title="毕业记录"
      dialogWidth="590px">
      <el-form label-suffix=":">
        <el-form-item>
          <el-table :data="gList">
            <el-table-column
              label="操作时间"
              width="180"
              prop="createTime"></el-table-column>
            <el-table-column
              label="毕业学生数"
              width="120"
              align="center"
              prop="studentNums"></el-table-column>
            <el-table-column
              label="状态"
              prop="implementStatus">
              <template slot-scope="scope">
                <span>
                  {{ scope.row.status == 0 ? '' : '已撤销' }}
                </span>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              align="center">
              <template slot-scope="scope">
                <el-button
                  v-show="scope.row.status == 0 && scope.row.canUn == true"
                  @click="handleClick(scope.row)"
                  type="text"
                  class="edit_btn_text"
                  style="padding: 0; width: auto; height: 0"
                  size="small">
                  撤销
                </el-button>
                <el-button
                  @click="downLoad(scope.row)"
                  type="text"
                  class="edit_btn_text">
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </MyDialog>
  </div>
</template>

<script>
import MyDialog from '@/components/MyDialog';
import MyForms from '@/components/MyForms';
import { getgList, unGraduation } from '@/api/youjiao';
export default {
  data() {
    return {
      gList: [],

      loading: false,
    };
  },
  components: {
    MyDialog,
    MyForms,
  },

  created() {
    this.getgList();
  },
  methods: {
    //获取操作记录
    async getgList() {
      const { rows } = await getgList();
      this.gList = rows;
    },
    handleClick(item) {
      this.$modal.confirm('是否撤销毕业操作？').then(() => {
        unGraduation(item.graduationId).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('撤销成功');
            this.getgList();
          }
        });
      });
    },
    downLoad(item) {
      this.$download.file(
        `${item.createTime}-毕业记录-（${item.studentNums}人）`,
        '/portal/student/exportGraduation',
        {
          graduationId: item.graduationId,
        }
      );
    },
  },
};
</script>

<style></style>
