<template>
  <div class="warp">
    <div
      class="type"
      v-show="userInfo.status == 2">
      已休学
    </div>
    <div class="flex just_between p_20">
      <div class="phtot">
        <img
          :src="
            userInfo.headIcon
              ? userInfo.headIcon
              : userInfo.sex == 0
              ? male
              : female
          " />
      </div>
      <div class="base fs_15">
        <div
          class="fs_20 opacity_8 fw_700 ellipsis"
          :title="userInfo.userName">
          {{ userInfo.userName }}
        </div>
        <!-- <div>
          <span class="opacity_6">学号：</span>
          {{ userInfo.idcard }}
        </div> -->
        <div class="flex m_t_10">
          <span class="opacity_6">性别：</span>
          <dict-tag
            :options="dict.type['sys_user_sex']"
            :value="userInfo.sex" />
        </div>
      </div>
    </div>
    <div
      class="flex aligin_center"
      style="border-radius: 5px; border-top: 1px #e6e6e6 solid">
      <slot>
        <!-- <el-button
          class="edit_btn_text"
          @click="$emit('graduate', userInfo)">
          毕业
        </el-button>

        <el-button
          class="edit_btn_text"
          @click="$emit('leave', userInfo)">
          转校
        </el-button>

        <el-button
          class="del_btn_text"
          @click="$emit('del', userInfo)">
          删除
        </el-button> -->
      </slot>
    </div>
  </div>
</template>

<script>
import male from '@/assets/avatar/maleStudent.png';
import female from '@/assets/avatar/femaleStudent.png';
export default {
  dicts: ['sys_user_sex'],
  data: () => {
    return {
      male,
      female,
    };
  },
  // inject: ['reload'],
  props: {
    userInfo: [],
  },
  methods: {
    // test() {
    //   this.reload();
    // },
  },
};
</script>

<style scoped lang="scss">
.set_btn {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .el-button {
    width: 100% !important;
    margin: 5px 0;
  }
}
.type {
  position: absolute;
  right: -1px;
  width: 70px;
  height: 30px;
  border-radius: 0px 4px 4px 4px;
  border: 0px solid;
  font-size: 1.4rem;
  background-color: #35d073;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 5px;
}
.warp {
  width: 275px;
  height: 172px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
  position: relative;

  .type1 {
    position: absolute;
    right: -1px;
    width: 70px;
    height: 30px;
    border-radius: 0px 4px 4px 4px;
    border: 0px solid;
    font-size: 1.4rem;
    background-color: #35d073;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 5px;
  }
  .type1::after {
    content: '';
    border-right: 2px #35d073 solid;
    position: absolute;
    right: 0px;
    bottom: -2px;
    height: 20px;
    border-radius: 20px;
  }
  .type2 {
    position: absolute;
    right: -1px;
    width: 70px;
    height: 30px;
    border-radius: 0px 4px 4px 4px;
    border: 0px solid;
    font-size: 1.4rem;
    background-color: #ff8d4d;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 5px;
  }
  .type2::after {
    content: '';
    border-right: 2px #ff8d4d solid;
    position: absolute;
    right: 0px;
    bottom: -2px;
    height: 20px;
    border-radius: 20px;
  }
  .type3 {
    position: absolute;
    right: -1px;
    width: 70px;
    height: 30px;
    border-radius: 0px 4px 4px 4px;
    border: 0px solid;
    font-size: 1.4rem;
    background-color: #6a94ff;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 5px;
  }
  .type3::after {
    content: '';
    border-right: 2px #6a94ff solid;
    position: absolute;
    right: 0px;
    bottom: -2px;
    height: 20px;
    border-radius: 20px;
  }
  img {
    width: 77px;
    height: 77px;
    object-fit: cover;
    border-radius: 50%;
  }
  .base {
    width: 150px;
    > div {
      line-height: 28px;
    }
  }
}
</style>
