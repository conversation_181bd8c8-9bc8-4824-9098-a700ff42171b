<template>
  <div class="wrap">
    <div style="height: 153px">
      <div :class="[info.teachingSchools.length > 1 ? 'infos' : 'newInfo']">
        <div class="image">
          <img
            :src="
              info.headIcon
                ? info.headIcon
                : require('@/assets/images/Snipaste_2023-06-15_15-28-05.png')
            " />
        </div>
        <div class="basic fs_15">
          <div
            class="fs_20 opacity_8 fw_700 ellipsis m_b_5"
            :title="info.userName">
            {{ info.userName }}
          </div>
          <div class="flex m_b_5">
            <div class="opacity_6">电话:</div>
            {{ info.phone }}
          </div>
          <div style="display: flex">
            <div class="opacity_6">性别:</div>
            <dict-tag
              :options="dict.type['sys_user_sex']"
              :value="info.sex"></dict-tag>
          </div>
        </div>
      </div>
      <div
        style="padding: 0 1rem"
        class="text flex">
        <div
          class="opacity_6"
          style="height: 21px; width: 45px">
          学校:
        </div>
        <div>
          <div
            v-for="(item, index) in info.teachingSchools"
            :key="index">
            <!--            <div class="schoolTitle">{{item.fullName}}</div>-->
            <div class="schoolTitle">
              <Tooltip
                :title="item.fullName"
                @getTitleWidth="getTitleWidth"></Tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      style="
        border: 1px #e6e6e6 solid;
        border-radius: 6px;
        height: 40px;
        text-align: center;
        cursor: pointer;
      ">
      <span
        class="btn"
        @click="showSchoolEdit">
        任职学校编辑
      </span>
    </div>

    <!-- 弹窗设置-->
    <MyDialog
      v-if="teachingDialog"
      title="设置任职学校"
      @close="teachingDialog = !teachingDialog"
      @confirm="$refs.forms.setSelect()"
      :dialogWidth="dialogWidth > '420' ? '600px' : '430px'">
      <TeachingSetting
        :column="info"
        ref="forms"
        @getSelect="confirmSelect"
        :schoolInfo="schoolForms"
        @deleteSchool="deleteSchool"></TeachingSetting>
    </MyDialog>
  </div>
</template>
<script>
import MyDialog from '@/components/MyDialog.vue';
import TeachingSetting from '@/components/TeachingSetting.vue';
import Tooltip from '@/components/Tooltip.vue';
import {
  getTeacher,
  setTeacherSchool,
  getTeacherList,
  delTeacherSchool,
} from '@/api/zhili';

export function schoolNameForms() {
  return [];
}
export default {
  name: 'TeacherList',
  components: {
    TeachingSetting,
    MyDialog,
    Tooltip,
  },
  dicts: ['sys_user_sex'],
  data() {
    return {
      teachingDialog: false,
      parameter: {
        pageNum: '',
        pageSize: 12,
      },
      dialogWidth: '',
      loading: false,
    };
  },
  props: {
    info: {},
    schoolForms: [],
  },
  methods: {
    confirmSelect(res) {
      if (res.stageId === '') {
        this.teachingDialog = false;
      } else {
        setTeacherSchool(res).then(() => {
          // 1.管理员通过页面跳转找到目标 获取跳转page
          this.$message.success('设置成功!');
          if (sessionStorage.getItem('goto')) {
            this.parameter.pageNum = Number(
              sessionStorage.getItem('TeacherPage')
            );
            this.$parent.getTeacherLists(this.parameter); // 执行父组件中的更新列表方法
            sessionStorage.removeItem('goto');
            sessionStorage.removeItem('TeacherPage');
          } else {
            // 第一次进入列表页时 或者设置第一页未跳转时 默认将pageSize设定为1 保证刷新
            this.parameter.pageNum = 1;
            this.$parent.getTeacherLists(this.parameter); // 执行父组件中的更新列表方法
            sessionStorage.removeItem('TeacherPage');
          }
          // 2.管理员通过搜索功能找到目标 设置任职学校
        });
        this.teachingDialog = false;
      }
    },
    showSchoolEdit() {
      this.teachingDialog = true;
    },
    // 获取文本的长度  设置弹窗宽度
    getTitleWidth(res) {
      this.dialogWidth = res;
    },
    deleteSchool(item) {
      this.$modal
        .confirm('确定删除当前学校吗？')
        .then(async () => {
          await delTeacherSchool(item.teacherId).then(async () => {
            // 实现删除后局部刷新
            this.teachingDialog = false;
            await this.$parent.getTeacherLists();
            this.teachingDialog = true;
            this.$message({
              message: '删除成功!',
              type: 'success',
              customClass: 'zZindex',
            });
          });
        })
        .catch(() => {});
    },
  },
  created() {
    // 手动刷新时清除存储数据
    sessionStorage.removeItem('goto');
    sessionStorage.removeItem('TeacherPage');
  },
};
</script>
<style lang="scss" scoped>
.wrap {
  width: 275px;
  height: 193px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
  .newInfo {
    display: flex;
    padding: 1.5rem 1.5rem;
  }
  .infos {
    display: flex;
    padding: 1rem 1.5rem 0 1.5rem;
  }
  img {
    width: 77px;
    height: 77px;
    border-radius: 50%;
  }
  .basic {
    margin: 0 1.5rem;
  }
  .text {
    font-size: 15px;
    .schoolTitle {
      width: 215px;
      height: 20px;
    }
  }
}
.btn {
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  line-height: 40px;
  color: #0f4444;
  cursor: pointer;
}
.zZindex {
  z-index: 999999 !important;
}
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
</style>
