<template>
  <!-- 菜单栏-->
  <div class="menus">
    <div class="tabs">
      <div
        :class="tabIndex == index ? 'item active' : 'item'"
        v-for="(item, index) in datas"
        :key="index"
        @click="tabClick(item, index)">
        <i :class="item.icon"></i>
        {{ item.title }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MyTabs',
  data: () => {
    return {
      tabIndex: 0, //默认选中标签
    };
  },
  props: {
    datas: [],
  },
  methods: {
    tabClick(item, index) {
      this.tabIndex = index;
      this.$router.push({ path: item.path });
    },
  },
};
</script>

<style scoped lang="scss">
.menus {
  height: 60px;
  .tabs {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    color: #0f4444;
    font-family: PingFangSC;
    font-weight: 700;
    .item {
      cursor: pointer;
      display: flex;
      height: 60px;
      padding: 0 20px;
      align-items: center;
      font-size: 1.8rem;
      i {
        font-size: 2rem;
        margin-top: 0.1rem;
        padding-right: 0.5rem;
      }
    }
    .active {
      background-color: #0f4444;
      color: #fff;
    }
  }
}
</style>
