<template>
  <div class="container-tree">
    <div class="box">
       <!-------搜索框 --------->
        <div class='tree_input'>
          <el-input placeholder="请输入关键字搜索" v-if="showSearch" v-model='filterKey' clearable></el-input>
        </div>
        <el-tree :data='treeModelData' :show-checkbox="imultiple" class='tree_form'
                 :expand-on-click-node="false" highlight-current ref="tree"
                 :filter-node-method="filterNode" :node-key="treeNodeKey" @node-click="handleNodeClick"
                  >
          <div class='tree-node_body node_body flex' slot-scope="{ node, data }">
            <div class="custom-tree-node">
              <span class='label' :title='node.label || "-"'>{{ node.label }}</span>
            </div>
            <!-- 自定义按钮区 -->
            <div class="flex">
<!--              <el-button class="btn">-->
<!--                设置-->
<!--              </el-button>-->
              <span v-if="data.addDept">
                <span class="btn" @click="AddDepartment(node,data)">新增部门</span>
<!--                  <el-button class="btn" @click="AddDepartment(node,data)">新增科室</el-button>-->
              </span>
<!--              <span v-if="data.addUser">-->
<!--                <el-button class="btn" @click="AddMembers(node,data)">新增成员</el-button>-->
<!--              </span>-->
<!--              <span v-if="data.addAgen">-->
<!--                 <el-button class="btn" @click="AddUnit(node,data)">新增直属单位</el-button>&nbsp;-->
<!--              </span>-->
<!--              <span v-if="data.addScho">-->
<!--                 <el-button class="btn" @click="AddCampus(node,data)">新增学校</el-button>&nbsp;-->
<!--              </span>-->
<!--              <span v-if="data.addStage">-->
<!--                 <el-button class="btn" @click="AddBranchCampus(node,data)">新增分校</el-button>&nbsp;-->
<!--              </span>-->
              <span v-if="data.type==='dept'">
                <span @click="deleteOrgan(node,data)" class="delete_node">删除</span>
              </span>
<!--              <span v-else-if="(data.children<=0||!data.children)&&!data.addDept">-->
<!--                <span @click="Edit(node, data)" class="edit_node">编辑</span>&nbsp;-->
<!--                <span @click='Delete(node, data)' class="delete_node">删除</span>-->
<!--              </span>-->
            </div>
          </div>
        </el-tree>


      </div>

<!--    <div>-->
<!--      <div class="btns" id="first_menu" v-show="showFirstMenu">-->
<!--        <el-button type="primary" @click="AddDepartment">新增科室</el-button>-->
<!--        <el-button type="primary" @click="AddMembers">新增成员</el-button>-->
<!--        <el-button type="primary" @click="deleteOrgan">删除</el-button>-->
<!--      </div>-->
<!--    </div>-->

  </div>
</template>

<script>
export default {
  components: {  },
  data() {
    return {
      filterKey: '', // 搜索框对应的value值
      treeModelData: [], // 树的展示数据
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      user:this.$store.getters.role.manager_id,
      currentNode:null,
      showFirstMenu:false,
      nodedata:''
    }
  },
  props: {
    treeData: { // 接收树数据
      type: Array,
      default: () => {
        return []
      }
    },
    btns: { // 可用于自定义按钮数据展示
      type: Array,
      default: () => {
        return []
      }
    },
    showSearch: { // 是否显示搜索框
      type: Boolean,
      default: false
    },
    imultiple: { // 是否显示多选框
      type: Boolean,
      default: false
    },
    treeNodeKey:{
      type:String,
      default:''
    },
    // maxHeight: {
    //   type: Number,
    //   default: 1080
    // },
    // isForm: { // 是否需要显示按钮区
    //   type: Boolean,
    //   default: false
    // }
  },
  watch: {
    treeData: { // 接收到的数据进行处理
      handler(val) {
        if (val) {
          this.treeModelData = val
        }
      },
      deep: true,
      immediate: true
    },
    filterKey(val){
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    // 新增科室
      AddDepartment(node, data) {
        this.$emit('AddDepartment', node, data);
      },
    // AddDepartment() {
    //   console.log(888)
    //   this.$emit('AddDepartment',this.nodedata);
    //   this.nodedata='';
    // },
    // 新增成员
    // AddMembers(node,data){
    //   this.$emit('AddMembers', node, data);
    // },

    AddMembers(){
      this.$emit('AddMembers', this.nodedata);
      this.nodedata='';
    },
    // 新增直属单位
    AddUnit(node,data){
      this.$emit('AddUnit', node, data);
    },
    // 新增学校
    AddCampus(node,data){
      this.$emit('AddCampus', node, data);
    },
    // 新增分校
    AddBranchCampus(node,data){
      this.$emit('AddBranchCampus', node, data);
    },
    // 删除
    Delete(node, data) {
      this.$emit('Delete', node, data);
    },
    // 删除部门
    deleteOrgan(node,data){
      this.$emit('deleteOrgan',node,data);
    },
    // deleteOrgan(node,data){
    //   this.$emit('deleteOrgan',this.nodedata);
    // },
    // 编辑
    Edit(node, data) {
      this.$emit('Edit', node, data);
      // this.$router.push({
      //   path:'/zhili/organization/detail',
      //   query:{
      //     id:data.id
      //   }
      // })
    },

    // 搜索功能
    filterNode(value, data, node) {
      if (!value) return true;
      return this.findSearchKey(node, value);
    },
    // 递归搜索父级是否包含关键字
    findSearchKey(node, key) {
      if (node.label.indexOf(key) !== -1) {
        return true;
      } else {
        if (node.parent.parent == null) {
          return false;
        } else {
          return this.findSearchKey(node.parent, key);
        }
      }
    },
    // 懒加载
    // 删除节点node
    treeDeleteNode(val){
      this.$refs.tree.remove(val);
    },
    // 新增节点node
    treeAddNode(data){
      this.$refs.tree.append(data,data.parentId)
    },
    // 保存当前节点
    handleNodeClick(data,node,box){
      this.currentNode=node;
      this.$emit('handleNodeClick',data,node);
    }
  },
  created() {},
}
</script>

<style lang="scss" scoped>
.container{
  width:400px;
  margin:0 auto;
}

.node_body{
  width:100%;
  justify-content: space-between;
  align-items: center;
  .btn {
    margin:0 1rem;
    opacity: 0.8;
    color: #0f4444 ;
    font-size: 15px ;
    font-weight: 700;
    padding:10px;
  }
  .delete_node{
    color:red;
    font-size: 15px;
    font-weight: 550;
  }
  .edit_node{
    color: #0f4440;
    font-size:15px;
    font-weight: 550;
  }
}
.box {
  border-radius: 0.6rem;
  margin:0 0 2rem 0;
  padding: 0 2rem 0 1rem;
}
.tree_input{
  width:200px;
  margin:2rem 0;
}

::v-deep .el-tree-node__content {
  font-size: 15px;
  font-weight: bold;
  color: #0f4444;
  height: 60px;
  padding:0 36px;
}

// 图标样式
::v-deep .el-tree-node__expand-icon {
  color: #0f4444;
  font-size: 20px;
  padding: 0 3px 2px 0;
}
::v-deep .tree_input .el-input .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;

}
// 无子节点图标隐藏
::v-deep .el-tree-node__expand-icon.is-leaf {
  color: transparent
}

//.btns{
//  position: absolute;
//  display: flex;
//  flex-direction: column;
//  aligns-items: center;
//  background-color: #fff;
//  border-radius: 3rem;
//  width:175px;
//  padding:1rem 0;
//  box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.20);
//  .el-button {
//    width: 138px !important;
//    margin: 5px auto;
//    height:40px !important;
//    text-align: center;
//  }
//
//.tree_form {
//  margin-top: 10px;
//  position: relative;
//  }
//  .el-tree-node {
//    position: relative;
//    color: black;
//    padding: 3px 0px;
//    padding-left: 10px;
//  }
//
//  .el-tree-node__children {
//    padding-left: 16px;
//  }
//
//  .el-tree-node:last-child::before {
//    height: 38px;
//  }
//  //.el-tree-node.is-current>.el-tree-node__content {
//  //
//  //  .tree-node_body,
//  //  .tree_form_icon,
//  //  .custom-tree-node .no_children {
//  //    color: #0f4444 !important;
//  //  }
//  //}
//  //.el-tree-node__content {
//  //  .custom-tree-node .label {
//  //    margin-left: 5px;
//  //  }
//  //}
//  //.tree-node_body {
//  //  width: 100%;
//  //  display: flex;
//  //  justify-content: space-between;
//  //
//  //
//  //}
//}
</style>
