<template>
  <div
    class="warp"
    :style="cssVars">
    <div
      :title="`${item.role_name}-${item.full_name}`"
      :class="
        item.full_name + item.role_name == role.full_name + role.role_name
          ? 'item active'
          : 'item'
      "
      v-for="(item, index) in roles"
      :key="index"
      @click="switchHandle(item)">
      {{ item.role_name }}-{{ item.full_name }}
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  data() {
    return {};
  },
  props: {
    color: '',
  },
  methods: {
    //切换角色
    switchHandle(item) {
      this.$store.dispatch('SetRole', item).then((res) => {
        //进入对应角色空间
        switch (item.role_key) {
          case 'workers_wor':
          case 'area_man':
          case 'dept_man':
          case 'manager_man':
          case 'admin':
          case '':
            this.$router.push({ path: '/zhili/index' });
            break;
          case 'school_man':
            this.$router.push({ path: '/school/index' });
            break;
          case 'teacher_tea':
          case 'classTeacher_tea':
          case 'deputyHeadTeacher_tea':
          case 'gradeLeader_tea':
            this.$router.push({ path: '/youjiao/index' });
            break;
          case 'student_stu':
            this.$router.push({ path: '/lexue/index' });
            break;
          case 'parents_par':
            this.$router.push({ path: '/hejia/index' });
            break;
        }
      });
    },
  },
  computed: {
    ...mapGetters(['roles', 'role']),
    cssVars() {
      return {
        '--color': this.color,
      };
    },
  },
};
</script>

<style scoped lang="scss">
.warp {
  display: flex;
  .item {
    border: 1px var(--color) solid;
    border-radius: 16px;
    opacity: 0.8;
    height: 30px;
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 30px;
    // display: flex;
    // align-items: center;
    padding: 4px 20px;
    margin-left: 20px;
    cursor: pointer;
    background-color: #fff;
    color: var(--color);
    font-size: 1.5rem;
  }
  .active {
    background-color: var(--color);
    color: #fff;
  }
}
</style>
