<template>
  <div class="content">
      <div class="flex">
        <div class="fs_15 opacity_6">当前学校:</div>
        <div class="current-box">
          <div class="flex school-title" v-for="(item,index) in teacherSelf" :key="index">
            <div class="title">{{item.fullName}}</div>
            <div class="edit" @click="$emit('deleteSchool',item)" v-show="item.fullName">删除</div>
          </div>
        </div>
      </div>
      <div class="flex second-content">
        <div class="fs_15 opacity_6">选择学校:</div>
        <el-select class="select" v-model="options.select" clearable filterable>
          <el-option v-for="item in schoolInfo"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"></el-option>
        </el-select>
      </div>
      <div class="tip">
        <span style="color:#FF6262;font-size: 12px;">*新增学校可直接选择,替换学校先删除原来的学校再来选择</span>
      </div>
  </div>
</template>

<script>
export default {
  name:'TeachingSetting',
  props:{
    column:{
      type:Object,
      default(){
        return {}
      }
    },
    schoolInfo:{
      type:Array,
      default() {
        return [];
      }
    }
  },
  data(){
    return {
      options: {
        select: ''
      },
      teacherSelf:this.column.teachingSchools,
      teacherId:'',
    }
  },
  methods:{
    // deleteSchool(item){
    //   // console.log(this.$parent.$parent.$parent.$parent.getTeacherLists());
    //     this.$modal.confirm('确定删除当前学校吗？').then(async()=>{
    //      await delTeacherSchool(item.teacherId).then(async()=>{
    //        //  // 实现删除后局部刷新
    //        // const res= await getTeacherList({userName:this.column.userName});
    //        // 删除后刷新老师列表实现
    //        await this.$parent.$parent.$parent.$parent.getTeacherLists();
    //        this.$modal.msgSuccess('删除成功!');
    //       })
    //     }).catch(()=>{})
    // },
    setSelect(){
      this.$emit('getSelect',{
        stageId:this.options.select,
        userId:this.column.userId
      })
    },
  },
  created(){
  }
}
</script>
<style lang="scss" scoped>
.content{
  width: 100%;
  margin:0 auto;
  .current-box{
    margin-bottom: 1rem;
    min-width: 250px;
    .school-title{
      padding:0 0 1rem 0;
      justify-content: space-between;
      .title{
        color:#6A94FF;
        padding-left:1rem;
      }
      .edit{
        color:#FF6262;
        cursor: pointer;
        margin-left:1rem;
      }
    }
  }
  .second-content{
    height:40px;
    line-height:40px;
    .select{
      margin-left: 5px;
      min-width:250px;
    }
  }
  .tip{
    width:100%;
    text-align: center;
    padding:1rem 0;
  }
}
</style>