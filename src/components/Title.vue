<template>
  <div class="TitleBox">
    <div class="font-size-title">{{ title }}</div>
    <div v-if="showBtn" class="fs_14 pointer" @click="$emit('checkMore')">
      <span>更多</span>
      <i class="el-icon-caret-right"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MyTitle',
  data: () => {
    return {};
  },
  props: {
    title: {
      type: [String],
    },
    showBtn:{
      type:Boolean,
      default:false
    }
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.TitleBox{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.font-size-title {
  font-size: 1.8rem;
  opacity: 0.8;
  font-weight: 800;
  display: flex;
  align-items: center;
}
</style>
