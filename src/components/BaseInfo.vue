<template>
  <div class="info-warp">
    <el-form
        :label-width="labelWidth"
        label-suffix=":"
        :model="formDatas"
        :rules="rules"
        ref="forms">
      <div
          class="infos"
          v-if="editable == false">
        <div
            class="photo"
            v-for="item in columns"
            :key="item.prop"
            v-show="item.type == 'photo'">
          <img
              :src="
              item.default
                ? item.default
                : require('@/assets/images/Snipaste_2023-06-15_15-28-05.png')
            "/>
        </div>
        <div class="items">
          <el-form-item
              :label="item.label"
              v-for="item in columns"
              :key="item.prop"
              v-show="item.type != 'photo'">
            <span v-if="item.type == 'dict'">
              <dict-tag
                  :options="dict.type[item.dictType]"
                  :value="(item.default || '').split(';')"
                  myTag="phrases"/>
            </span>
            <span v-else-if="item.type == 'date'">
              {{ item.default }}
            </span>
            <span v-else-if="item.type == 'number'">
              {{ splitNumber(item.default) }}
            </span>
            <span v-else-if="item.type == 'card'">
              {{ splitCard(item.default) }}
            </span>
            <span v-else>
              {{ item.default }}
            </span>
          </el-form-item>
        </div>
      </div>
      <div
          class="infos"
          v-else>
        <div
            class="photo"
            v-for="item in columns"
            :key="item.prop"
            v-show="item.type == 'photo'">
          <Avatar
              :defaultImg="item.default"
              @success="uploadSuccess"></Avatar>
          <!-- <img src="../assets/images/Snipaste_2023-06-15_15-28-05.png" /> -->
        </div>
        <div class="items">
          <el-form-item
              :label="item.label"
              v-for="item in columns"
              :key="item.prop"
              v-show="item.type != 'photo'"
              :prop="item.prop">
            <!-- 文本输入-->
            <el-input
                :placeholder="item.placeholder"
                v-if="item.type == 'input'"
                v-model="formDatas[item.prop]"></el-input>
            <el-input
                :placeholder="item.placeholder"
                v-if="item.type == 'number'"
                v-model="formDatas[item.prop]"></el-input>
            <el-input
                :placeholder="item.placeholder"
                v-if="item.type == 'card'"
                v-model="formDatas[item.prop]"></el-input>
            <!-- 下拉选择-->
            <el-select
                v-if="item.type == 'select'"
                :placeholder="item.placeholder"
                v-model="formDatas[item.prop]"
                :multiple="item.multiple ? item.multiple : false">
              <el-option
                  v-for="option in item.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"></el-option>
            </el-select>

            <!-- 单选-->
            <!-- 多选-->
            <!-- tree-->
            <!-- 日期-->
            <el-date-picker
                v-if="item.type == 'date'"
                value-format="yyyy-MM-dd"
                :placeholder="item.placeholder"
                v-model="formDatas[item.prop]"></el-date-picker>
            <!-- 字典-->
            <el-select
                v-if="item.type == 'dict'"
                :placeholder="item.placeholder"
                v-model="formDatas[item.prop]"
                :multiple="item.multiple ? item.multiple : false">
              <el-option
                  v-for="dict in dict.type[item.dictType]"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import Avatar from '@/components/Avatar';

export default {
  name: 'MyForms',
  dicts: [
    'user_nation',
    'sys_user_sex',
    'kinship_type',
    'grade_period',
    'stage_name',
    'school_type',
    'school_setup',
    'agency_type',
    'idcard_type',
    'politics_status',
  ],
  data: () => {
    return {
      formDatas: {},
      rules: {},
      avatar: '',
    };
  },
  components: {
    Avatar,
  },
  props: {
    columns: [],
    labelWidth: {
      type: String,
      default: '12rem',
    },
    editable: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    //验证数据规则
    if (this.editable) {
      this.columns.map((item) => {
        this.rules[item.prop] = [
          {
            required: item.required ? item.required : false,
            message: item.placeholder,
            trigger: item.type == 'input' ? 'blur' : 'change',
          },
        ];
      });
    }
    //获取默认值
    this.columns.forEach((item) => {
      this.formDatas[item.prop] = item.default;
    });
    this.formDatas = _.clone(this.formDatas);
  },
  methods: {
    splitNumber(val) {
      // 检查参数是否为有效的字符串且长度足够
      if (!val || typeof val !== 'string' || val.length < 8) {
        return val || ''; // 如果参数无效，返回原值或空字符串
      }
      return val.substr(0, 3) + '****' + val.substr(7)
    },
    splitCard(val) {
      // 检查参数是否为有效的字符串且长度足够
      if (!val || typeof val !== 'string' || val.length < 16) {
        return val || ''; // 如果参数无效，返回原值或空字符串
      }
      return val.substr(0, 3) + '************' + val.substr(15)
    },
    getFormsDatas() {
      this.$refs.forms.validate((valid) => {
        if (valid) {
          this.$emit('formsDatas', this.formDatas);
        }
      });
    },
    uploadSuccess(r) {
      this.formDatas.headIcon = r.ossFileUrl;
      this.avatar = r.ossFileUrl;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep input {
  width: 200px !important;
}

.info-warp {
  width: 100%;

  .infos {
    display: flex;
    justify-content: space-around;
    width: 100%;
    // padding: 2rem;

    img {
      height: 23rem;
      width: 19rem;
      object-fit: cover;
    }

    .photo {
      width: 20%;
      text-align: right;
    }

    .items {
      display: grid;
      grid-template-columns: 50% 50%;
      grid-auto-rows: 6rem;
      width: 70%;
    }
  }
}
</style>
