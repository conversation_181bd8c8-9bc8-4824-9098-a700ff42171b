<!-- 学生转校 -->
<template v-loading="loading">
  <div>
    <MyDialog
      @close="$emit('close')"
      @confirm="$emit('close')"
      title="学生转校"
      dialogWidth="590px">
      <el-form label-width="80px">
        <el-form-item label="选择年级:">
          <div class="flex">
            <div>
              <SchoolStudentOrg
                :stageId="school.stageId"
                ref="students"
                @getFormsDatas="$emit('confirm', $event)"></SchoolStudentOrg>
            </div>
            <div class="m_l_10">
              <el-button
                type="primary"
                @click="$refs.students.getFormsDatas()">
                确定转校
              </el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <el-form label-suffix=":">
        <el-form-item>
          <el-table :data="gList">
            <el-table-column
              label="操作时间"
              width="180"
              prop="createTime"></el-table-column>
            <el-table-column
              label="毕业学生数"
              width="120"
              align="center"
              prop="studentNums"></el-table-column>
            <el-table-column
              label="状态"
              prop="implementStatus">
              <template slot-scope="scope">
                <span>
                  {{ scope.row.status == 0 ? '' : '已撤销' }}
                </span>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              align="center">
              <template slot-scope="scope">
                <el-button
                  v-show="scope.row.status == 0 && scope.row.canUn == true"
                  @click="handleClick(scope.row)"
                  type="text"
                  class="edit_btn_text"
                  style="padding: 0; width: auto; height: 0"
                  size="small">
                  撤销
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </MyDialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import MyDialog from '@/components/MyDialog';
import MyForms from '@/components/MyForms';
import { getgList, unGraduation } from '@/api/youjiao';
import SchoolStudentOrg from '@/components/SchoolStudentOrg';
export default {
  data() {
    return {
      gList: [],

      loading: false,
    };
  },
  components: {
    MyDialog,
    MyForms,
    SchoolStudentOrg,
  },
  computed: {
    ...mapGetters(['school']),
  },

  created() {
    this.getgList();
  },
  methods: {
    //获取操作记录
    async getgList() {
      const { rows } = await getgList();
      this.gList = rows;
    },
    handleClick(item) {
      this.$modal.confirm('是否撤销转校操作？').then(() => {
        unGraduation(item.graduationId).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('撤销成功');
            this.getgList();
          }
        });
      });
    },
  },
};
</script>

<style></style>
