<template>
  <div class="header">
    <div class="box">
      <div>{{ info.courseName }}</div>
      <div class="opacity_6">
        <dict-tag
          :options="dict.type['stage_name']"
          :value="info.gradeLevel"></dict-tag>
      </div>
      <div>
        <button
          class="edit_btn_text"
          @click="CourseEdit">
          编辑
        </button>

        &nbsp;
        <button
          class="del_btn_text"
          @click="CourseDelete">
          删除
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { deleteCourse } from '@/api/zhili';

export default {
  name: 'CourseList',
  dicts: ['stage_name'],
  data() {
    return {
      // gradeLevel:''
    };
  },
  props: {
    info: {},
  },
  methods: {
    CourseDelete() {
      this.$modal
        .confirm('确定删除该学科吗？')
        .then(() => {
          console.log(this.info);
          deleteCourse(this.info.courseId).then(() => {
            this.$parent.getCourseLists(); // 父组件中的方法刷新
            this.$message.success('删除成功!');
          });
        })
        .catch(() => {
          this.$message.info('已取消!');
        });
    },
    CourseEdit() {
      // this.$router.push({
      //   path:'/zhili/organs/personaldetail',
      //   query:{
      //     courseId:this.info.courseId
      //   }
      // })
    },
  },
  created() {
    //   if(this.info.gradeLevel==='01'){
    //     this.gradeLevel='幼儿园';
    //   }else if(this.info.gradeLevel==='02'){
    //     this.gradeLevel='小学'
    //   }else if(this.info.gradeLevel==='03'){
    //     this.gradeLevel='初中'
    //   }else if(this.info.gradeLevel==='04'){
    //     this.gradeLevel='高中';
    //   }else{
    //     this.gradeLevel=null;
    //   }
  },
};
</script>
<style lang="scss" scoped>
.header {
  width: 1160px;
  .box {
    height: 40px;
    display: flex;
    justify-content: space-between;
    border-radius: 1rem;
    margin: 0 2rem 1rem 2rem;
    line-height: 40px;
    padding: 0 20px;
    color: #0f4444;
    font-size: 15px;

    background: #f6fafb;
  }
}
</style>
