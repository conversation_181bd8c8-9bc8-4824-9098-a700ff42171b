<template>
  <div>
    <div class="contents">
      <div class="flex" style="align-items: center;">
        <img src="../assets/avatar/maleTeacher.png">
        <div class="name">{{info.userName}}</div>
      </div>
      <dict-tag :options="dict.type['sys_user_sex']" :value="info.sex"></dict-tag>
      <div>{{ info.desPhone}}</div>
      <div class="flex operate">
        <div class="edit" @click="StaffEdit">编辑</div>
        <div class="delete" @click="DeleteStaff">删除</div>
      </div>
    </div>
  </div>

</template>
<script>
import {deleteStaff} from "@/api/zhili";

export default {
  name:'myStaff',
  dicts:['sys_user_sex'],
  components:{},
  data(){
    return {
    }
  },
  props:{
    info:{}
  },
  methods:{
    DeleteStaff(){
      console.log(this.info.staffId);
      this.$modal.confirm('确认删除该职员吗?').then(()=>{
        deleteStaff(this.info.staffId).then(()=>{
          this.$message.success('删除成功!');
          this.$parent.staffParameter.deptId='';
        })
      }).catch(()=>{})

    },
    StaffEdit(){
      console.log(this.info.staffId)
        this.$router.push({
          path:'/zhili/organs/personaldetail',
          query:{
            id:this.info.staffId
          }
        })
    }
  },
  created(){
  }
}
</script>
<style lang="scss" scoped>
.contents{
  height:40px;
  display: flex;
  justify-content: space-between;
  line-height:40px;
  padding:0 3rem;
  font-size: 16px;
  color:#0F4444;
  background-color:#F6FAFB;
  border-radius: .6rem;
  img{
    width:30px;
    height:30px;
  }
  .name{
    font-weight: 500;
    margin-left:2rem;
  }
  .operate{
    font-weight: 600;
    .edit{
      color:#0F4444;
      cursor: pointer;
    }
    .delete{
      margin-left:2rem;
      color:red;
      cursor: pointer;
    }
  }
}
</style>