<template>
  <div class="active" @click="toThemes(id)">
    <div class="cover">
      <img style="object-fit: fill" :src="cover" />
    </div>
    <div class="title">{{ title }}</div>
    <div class="time">活动截止时间：{{ time }}</div>
  </div>
</template>

<script>
  import {getAppSpaceToken} from "@/api/home";
  import {mapGetters} from 'vuex'
  import {profile} from "@/api/login";
  export default {
    name: 'ActivityList',
    data: () => {
      return {};
    },
    props: {
      title: [],
      time: [],
      cover: [],
      id: [],
    },
    computed:{
      ...mapGetters(['token'])
    },
    methods: {
      toThemes(id){
        if (!_.isEmpty(this.token)) {
          profile();
          getAppSpaceToken({appId:'a51d10d1af4faeb4fdc480fb3969d3d1'}).then(res=>{
            let token=res.data
            window.open('http://lzx.21spt.com/SSOLogin.aspx?token='+token+'&themeid='+id)
            // window.open('http://lzx.21spt.com/SSOLogin.aspx?token='+token)

            // console.log('http://lzx.21spt.com/SSOLogin.aspx?token='+token);
          })
        } else {
          this.$emit('getToken')
          // this.$refs.loginRef.showLogin=true
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .active {
    background: #fff;
    border-radius: 10px;
    cursor: pointer;
    .cover {
      img {
        width: 100%;
        height: 198px;
        object-fit: cover;
        border-radius: 10px;
      }
    }
    .title {
      font-size: 1.5rem;
      font-weight: bold;
      padding: 0.8rem 1rem;
    }
    .time {
      font-size: 1.2rem;
      font-weight: 400;
      padding: 0 0 1rem 1rem;
    }
  }
</style>
