<template v-loading="loading">
  <div>
    <input
        type="file"
        @change="uploadTemple"
        ref="uploadBtn"
        hidden/>
    <MyDialog
        @close="$emit('close')"
        @confirm="$emit('close')"
        title="上传学生"
        dialogWidth="800px">
      <el-form label-suffix=":">
        <el-form-item>
          <el-button
              class="edit_btn"
              @click="downTemple">
            下载导入模板
          </el-button>
          <el-button
              class="edit_btn"
              style="width: auto"
              @click="$refs.uploadBtn.click()">
            上传
          </el-button>

        </el-form-item>
        <el-form-item>
          <el-table :data="logList">
            <el-table-column
                label="文件名"
                prop="fileName"
                width="120"></el-table-column>
            <el-table-column
                label="上传时间"
                width="180"
                prop="createTime"></el-table-column>
            <el-table-column
                label="任务状态"
                prop="implementStatus">
              <template slot-scope="scope">
                <span>
                  {{
                    scope.row.implementStatus == 1
                        ? '待处理'
                        : scope.row.implementStatus == 2
                            ? '处理中'
                            : '已完成'
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
                label="记录数量"
                prop="sum"></el-table-column>
            <el-table-column
                label="失败记录数"
                prop="failSum"
                width="100"></el-table-column>
            <el-table-column
                label="操作"
                width="150"
                align="center">
              <template slot-scope="scope">
                <el-button
                    @click="window.open(scope.row.errExcelUrl)"
                    type="text"
                    class="edit_btn_text"
                    v-show="scope.row.implementStatus ==3 && scope.row.errExcelUrl != null"
                    style="padding: 0; width: auto; height: 0"
                    size="small">
                  错误导出
                </el-button>
                <el-button
                    @click="handleClick(scope.row)"
                    v-show="scope.row.implementStatus ==3"
                    type="text"
                    class="edit_btn_text"
                    style="padding: 0; width: auto; height: 0"
                    size="small">
                  查看结果
                </el-button>
                <el-button
                    @click="getLogs"
                    type="text"
                    class="edit_btn_text"
                    v-show="scope.row.implementStatus !=3"
                    style="padding: 0; width: auto; height: 0"
                    size="small">
                  刷新状态
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </MyDialog>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';
import MyDialog from '@/components/MyDialog';
import MyForms from '@/components/MyForms';
import {
  importTemplate,
  importData,
  getLogList,
  getLogTask,
} from '@/api/youjiao';

export default {
  data() {
    return {
      logList: [],
      showUpload: true,
      loading: false,
      window,
    };
  },
  components: {
    MyDialog,
    MyForms,
  },
  computed: {
    ...mapGetters(['school', 'role']),
  },
  created() {
    console.log(this.school);

    this.getLogs();
  },
  methods: {
    //下载模板
    downTemple() {
      // this.$download.file('导入模板', '/portal/student/importTemplate', {
      //   stageId: this.school.stageId ? this.school.stageId : this.role.stage_id,
      // });

      let stage = this.school.stageId
          ? this.school.stageId
          : this.role.stage_id;
      window.open(
          process.env.VUE_APP_TARGET +
          '/portal/student/importTemplate?stageId=' +
          stage
      );
    },
    //上传学生
    uploadStudent() {
      this.showUpload = true;
      this.getLogs();
    },
    //获取上传结果
    getLogs() {
      this.loading = true;
      getLogList({orderByColumn: 'createTime', isAsc: 'desc'}).then((res) => {
        this.logList = res.rows;
        this.loading = false;
      });
    },
    uploadTemple(e) {
      this.loading = true;
      let file = e.target.files[0];
      var formData = new FormData();
      formData.append('file', file);
      formData.append(
          'stageId',
          this.school.stageId ? this.school.stageId : this.role.stage_id
      );
      formData.append('updateSupport', true);
      importData(formData)
          .then((res) => {
            this.loading = false;
            this.$modal.msgSuccess('操作成功！');
            this.getLogs();
          })
          .catch(() => {
            this.loading = false;
          });
      e.target.value = '';
    },
    handleClick(item) {
      this.loading = true;
      getLogTask(item.taskId).then((res) => {
        this.$alert(res.data.result, '导入结果', {
          dangerouslyUseHTMLString: true,
        });
        this.loading = false;
      });
    },
  },
};
</script>

<style></style>
