<template>
  <div class="news-box" @click="handleClick">
    <div v-if="showPic" style="margin-right: 20px;">
      <img style="width:250px;height:130px;
            object-fit: fill;border-radius: 10px"
           :src="data.coverUrl">
    </div>
    <div :style="widthStyle" class="title">
      <div class="fs_bold fs_18">{{ data.title }}</div>
      <div style="display: flex;flex-direction: column;justify-content: space-between;height:80%;">
        <div :class="showPic?'content2':'content'"
             class="fs_16 c_999 m_t_10 ">{{ data.introduce }}
        </div>
        <div class="fs_14 c_999 m_t_10"
             style="text-align: right;bottom:0">{{ parseTime(data.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {parseTime} from "../utils/tools";

export default {
  name: 'NewsList',
  data: () => {
    return {};
  },
  props: {
    data: [],
    showPic: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    widthStyle() {
      if (this.showPic) {
        return {
          'width': '880px'
        }
      } else {
        return {
          'width': '100%'
        }
      }
    }
  },
  methods: {
    parseTime,
    handleClick() {
      this.$router.push({
        path: '/news/detail',
        query: {
          id: this.data.id
        }
      })
    }
  }
};
</script>

<style scoped lang="scss">
.news-box {
  display: flex;
  cursor: pointer;
  padding: 10px;
  width: 100%;

  .cover {
    img {
      width: 84px;
      height: 48px;
      object-fit: cover;
      opacity: 0.88;
    }
  }

  .title {
    flex: 0.93;
    width: 880px;
  }

  .c_999 {
    color: #999;
  }

  .content {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .content2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
}
</style>
