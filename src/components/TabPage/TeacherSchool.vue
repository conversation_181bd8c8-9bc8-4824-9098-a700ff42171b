<!-- 老师任教学校 -->
<template>
  <div
    class="container"
    v-loading="loading">
    <!--  老师任教学校 新增教师-->
    <MyDialog
      title="新增教师"
      v-if="showAddTeacherDialog"
      @close="showAddTeacherDialog = false"
      dialogWidth="550px"
      @confirm="$refs.teacher.getFormsDatas()">
      <div class="schoolDialog">
        <MyForms
          :columns="TeacherDatas"
          editable
          labelWidth="90px"
          ref="teacher"
          @formsDatas="addTeacher"></MyForms>
      </div>
    </MyDialog>
    <div class="content box">
      <div class="">
        <div class="head">
          <el-select
            v-model="teacherParamter"
            placeholder="全部"
            style="width: 110px"
            clearable
            @clear.native="clearSelect">
            <el-option
              @click.native="searchTeacher"
              v-for="dict in info"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
          <div class="flex">
            <el-input
              placeholder="输入名称进行搜索"
              style="width: 200px; margin-right: 2rem"
              @change="searchTeacher"
              suffix-icon="el-icon-search"
              @click-suffix.native="searchTeacher"
              v-model="teacherParamter.userName"></el-input>
            <el-button
              class="edit_btn"
              @click="showAddTeacherDialog = true">
              新增
            </el-button>
          </div>
        </div>
        <div class="contents flex">
          <div
            style="margin: 0 0 10px 15px"
            v-for="(item, index) in teacherContents.rows"
            :key="index">
            <TeacherList
              :info="item"
              @SchoolEdit="SchoolEdit"
              :schoolForms="schoolOption"></TeacherList>
          </div>
          <div class="paging">
            <el-pagination
              :page-size="teacherParamter.pageSize"
              background
              :total="teacherContents.total"
              :current-page.sync="teacherParamter.pageNum"
              @current-change="handleTeacherChange"></el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getTeacherList, getSchooList, addTeacher } from '@/api/zhili/index';
import SchoolList from '@/components/SchoolList.vue';
import MyDialog from '@/components/MyDialog.vue';
import Forms from '@/components/Forms.vue';
import MyForms from '@/components/MyForms.vue';
import TeacherList from '@/components/TeacherList.vue';
export default {
  dicts: ['school_type', 'school_setup'],
  data() {
    return {
      TeacherDatas: [
        {
          prop: 'headIcon',
          label: '教师头像',
          placeholder: '请上传教师头像',
          type: 'photo',
          default: '',
          required: false,
        },
        {
          prop: 'userName',
          label: '教师姓名',
          placeholder: '请输入教师姓名',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'sex',
          label: '教师性别',
          placeholder: '请选择教师性别',
          type: 'dict',
          dictType: 'sys_user_sex',
          multiple: false,
          default: '',
          options: [],
          required: true,
        },
        {
          prop: 'stageId',
          label: '任职学校',
          placeholder: '请选择任职学校',
          type: 'select',
          options: [],
          default: '',
          required: true,
        },
        {
          prop: 'idcardType',
          label: '证件类型',
          placeholder: '请选择证件类型',
          type: 'dict',
          dictType: 'idcard_type',
          options: [],
          default: '01',
          required: true,
        },
        {
          prop: 'idcard',
          label: '证件号码',
          placeholder: '请输入证件号码',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'phone',
          label: '手机号码',
          placeholder: '请输入手机号码',
          type: 'input',
          default: '',
          required: false,
        },
        {
          prop: 'qq',
          label: 'qq号码',
          placeholder: '请输入qq号码',
          type: 'input',
          default: '',
          required: false,
        },
        {
          prop: 'wx',
          label: '微信号码',
          placeholder: '请输入微信号码',
          type: 'input',
          default: '',
          required: false,
        },
        {
          prop: 'email',
          label: '邮箱号码',
          placeholder: '请输入邮箱号码',
          type: 'input',
          default: '',
          required: false,
        },
      ],
      info: [],
      loading: false,
      //   新增弹窗
      showNewOrgan: false,
      showAddOragns: false,
      showAddUnit: false,
      showAddSchool: false,
      showAddmember: false,
      showAddBranchSchool: false,
      showAddCourse: false,
      teachingDialog: false,
      showAddTeacherDialog: false,
      // 任教学校
      teacherParamter: {
        pageNum: 1,
        pageSize: 12,
        userName: '',
      },
      // 老师数据
      teacherContents: '',
      schoolInfoForms: [],
      schoolOption: [],
      //   弹窗数据
      newOrganForm: [],
      option: this.schoolOption,
    };
  },
  components: {
    MyDialog,
    MyForms,
    TeacherList,
  },
  created() {
    this.getTeacherLists();
    // 处理学校列表 保留fullName stageId
    getSchooList().then((res) => {
      if (res.constructor == Array) {
        res.forEach((item) => {
          let obj = {
            label: '',
            value: '',
          };
          obj.label = item.fullName;
          obj.value = item.stageId;
          this.schoolOption.push(obj);
        });
      }
    });
  },
  methods: {
    // 新增老师
    addTeacher(res) {
      addTeacher(res).then(() => {
        this.$message.success('添加成功!');
        this.getTeacherList();
      });
      this.showAddTeacherDialog = false;
    },
    // 教师列表跳转
    handleTeacherChange(page) {
      sessionStorage.setItem('goto', true); // 记录点击页面跳转操作
      sessionStorage.setItem('TeacherPage', page); // 存储跳转page
      this.teacherParamter.pageNum = page;
      this.getTeacherLists();
    },
    // 教师搜索
    searchTeacher() {
      this.getTeacherLists();
    },
    // 任职学校弹窗
    SchoolEdit() {
      this.teachingDialog = true;
    },
    // 获取教师列表
    async getTeacherLists(parameter) {
      this.loading = true;
      if (parameter == null) {
        this.teacherContents = await getTeacherList(this.teacherParamter);
      } else {
        this.teacherParamter = parameter;
        this.teacherContents = await getTeacherList(this.teacherParamter);
      }
      this.loading = false;
      // this.loading = true;
      // const data= await getTeacherList(this.teacherParamter);
      // this.teacherContents=data;
      // console.log(data,'教师列表')
      // this.loading = false;
    },
  },
  mounted() {
    this.TeacherDatas.forEach((item) => {
      if (item.prop === 'stageId') {
        item.options = this.schoolOption;
      }
    });
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  .box {
    border-radius: 8px;
    // padding: 0 20px;

    background-color: #fff;
    display: flex;
  }
  .content-box {
    margin: 3rem 0;
    height: 50px;
    align-items: center;
    .btn_anniu {
      margin: 0 1rem;
      height: 3.7rem;
      font-size: 1.8rem;
      font-weight: bold;
      border: 0 solid #fff;
      color: #0f4440;
      opacity: 0.5;
      cursor: pointer;
      outline: none;
      background: #fff;
    }
    .newStyle {
      position: relative;
      color: #0f4440;
      font-size: 1.8rem;
      font-weight: bold;
      opacity: 0.9;
    }
    .newStyle:before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 50%;
      height: 1px;
      border-bottom: 0.4rem solid #0f4440;
      box-sizing: border-box;
      opacity: 0.9;
    }
  }
  .content {
    //background-color:#fff;
    margin-bottom: 3rem;
    .head {
      width: 1100px;
      margin: 2rem 0;
      wdith: 50px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .contents {
      display: flex;
      flex-direction: row;
      //justify-content: space-between;
      flex-wrap: wrap;
      .paging {
        margin-top: 30px;
        width: 100%;
        text-align: center;
        margin-bottom: 20px;
      }
    }
  }
}
.schoolDialog {
  .el-select {
    display: flex;
    flex: 1;
  }
}
::v-deep .head .el-select .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;
  font-size: 16px;
  font-weight: 700;

  &::placeholder {
    color: #0f4444;
  }
}

::v-deep .head .el-select .el-icon-arrow-up:before {
  content: '\e78f';
  color: #0f4444;
}

::v-deep .head .el-select .el-input.is-focus .el-input__inner {
  border-color: #0f4444;
}

::v-deep .head .el-input .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;
}
::v-deep .body .el-tree-node__label {
  font-size: 1.6rem;
  color: #0f4444;
  opacity: 0.8;
  font-weight: 600;
}
::v-deep .head .el-select-dropdown__item.selected {
  color: #0f4444;
}
::v-deep .el-collapse-item__arrow {
  display: none;
}
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
</style>
