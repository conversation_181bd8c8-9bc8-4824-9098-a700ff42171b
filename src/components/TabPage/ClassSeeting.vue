<!-- 年级班级设置 -->
<template>
  <div>
    <!-- 新增年级-->
    <MyDialog
      v-if="showGradeDialog"
      dialogWidth="550px"
      @close="closeClassDialog"
      @confirm="saveGrade"
      :title="dialogState == 'add' ? '新增年级信息' : '编辑年级信息'">
      <div class="schoolDialog">
        <el-form
          label-width="90px"
          :model="gradeDatas"
          :rules="gradeRules"
          ref="gradeForm">
          <el-form-item
            v-show="role.is_branch == false"
            label="学校:"
            prop="stageId">
            <el-select
              v-model="gradeDatas.stageId"
              @change="selectStageDialog">
              <el-option
                v-for="(item, index) in stageList"
                :label="item.fullName"
                :value="item.stageId"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            prop="period"
            label="学段">
            <el-select
              placeholder="请选择学段"
              v-model="gradeDatas.period">
              <el-option
                v-for="dict in dict.type['grade_period']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="year"
            label="入学年份">
            <el-select
              v-model="gradeDatas.year"
              placeholder="请选择入学年份">
              <el-option
                v-for="item in years"
                :key="item"
                :label="item"
                :value="item"></el-option>
            </el-select>
          </el-form-item>

          <!-- <el-form-item label="年级备注:">
            <el-input type="textarea"></el-input>
          </el-form-item> -->
          <el-form-item
            v-if="dialogState == 'add'"
            label="班级数量:"
            prop="classNums">
            <el-input v-model.number="gradeDatas.classNums"></el-input>
          </el-form-item>
          <!-- <el-form-item label="年级组长:">
            <el-select>
              <el-option>www</el-option>
            </el-select>
          </el-form-item> -->
        </el-form>
      </div>
    </MyDialog>
    <!-- 新增班级-->
    <MyDialog
      v-if="showClassDialog"
      dialogWidth="450px"
      :title="dialogState == 'add' ? '新增班级' : '编辑班级'"
      @confirm="saveClass"
      @close="showClassDialog = false">
      <el-form
        label-width="90px"
        :model="classDatas"
        :rules="classRules"
        ref="classForm">
        <el-form-item
          label="班序:"
          prop="cdsSort">
          <el-input
            v-model.number="classDatas.cdsSort"
            type="number"></el-input>
        </el-form-item>
        <el-form-item
          label="班级别称:"
          prop="anotherName">
          <el-input v-model="classDatas.anotherName"></el-input>
        </el-form-item>
      </el-form>
    </MyDialog>
    <div
      class="stage_warp"
      v-show="role.is_branch == false">
      <div
        @click="selectStage(item, index)"
        :class="actived == index ? 'item active' : 'item'"
        v-for="(item, index) in stageList"
        :key="item.stageId">
        {{ item.fullName }}
      </div>
    </div>
    <div class="box">
<!--      <Title title="年级设置"></Title>-->
      <el-button
        class="edit_btn"
        @click="
          (showGradeDialog = true),
            (dialogState = 'add'),
            (gradeDatas.year = new Date().getFullYear())
        ">
        新增
      </el-button>
      <div class="baseInfo gradeWarp">
        <div
          class="lists"
          v-for="(item, index) in gradeList"
          :key="item.gradeName">
          <el-row>
            <el-col
              :span="10"
              class="grade"
              :class="gradeActive != index ? 'grade' : 'grade active'"
              @click.native="
                showClass(item),
                  gradeActive == index
                    ? (gradeActive = null)
                    : (gradeActive = index)
              ">
              <!--              <i class="dot"></i>-->
              <i
                :class="
                  gradeActive != index
                    ? 'el-icon-arrow-right'
                    : 'el-icon-arrow-down'
                "
                style="font-weight: bold; color: #0f4444; border: #0f4444"></i>
              <!--              {{ item.gradeName }}-->
              {{ item.gradeName }}
            </el-col>
            <el-col :span="11">
              {{ item.otherName }}
            </el-col>
            <!-- <el-col :span="5">
              <span class="opacity_6">班级：</span>
              5个
            </el-col> -->
            <!-- <el-col :span="11">
              <span class="opacity_6">备注：</span>
              谢谢谢谢谢
            </el-col> -->
            <el-col
              :span="3"
              class="flex just_around">
              <el-button
                type="text"
                class="edit_btn_text"
                @click="addClass(item, index)">
                新增班级
              </el-button>
              <el-button
                type="text"
                class="edit_btn_text"
                @click="editGarde(item)">
                编辑
              </el-button>
              <el-button
                type="text"
                class="del_btn_text"
                @click="delBaseGrade(item.gradeId, index)">
                删除
              </el-button>
            </el-col>
          </el-row>
          <el-row
            class="class_item"
            v-for="(item, index1) in classList"
            :key="item.classId"
            v-show="gradeActive == index">
            <el-col
              :span="10"
              class="class">
              <i class="dot"></i>
              {{ item.showName }}
            </el-col>
            <el-col :span="12">
              <span class="opacity_6">别名：</span>
              {{ item.anotherName }}
            </el-col>
            <!-- <el-col :span="6">
              <span class="opacity_6">备注：</span>
              谢谢谢谢谢
            </el-col> -->
            <el-col
              :span="2"
              class="flex just_around">
              <el-button
                type="text"
                class="edit_btn_text"
                @click="editClass(item, index)">
                编辑
              </el-button>
              <el-button
                type="text"
                class="del_btn_text"
                @click="delClass(item, index1)">
                删除
              </el-button>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <!--    <div class="box">-->
    <!--      <Title title="班级设置"></Title>-->

    <!--      <div class="baseInfo classWarp">-->
    <!--        <div-->
    <!--          class="lists"-->
    <!--          v-for="(item, index) in gradeList"-->
    <!--          :key="item.gradeName">-->
    <!--          <el-row>-->
    <!--            <el-col-->
    <!--              :span="21"-->
    <!--              :class="gradeActive != index ? 'grade' : 'grade active'"-->
    <!--              @click.native="showClass(item), (gradeActive = index)">-->
    <!--              {{ item.gradeName }}-->
    <!--              <i-->
    <!--                :class="-->
    <!--                  gradeActive != index-->
    <!--                    ? 'el-icon-arrow-right'-->
    <!--                    : 'el-icon-arrow-down'-->
    <!--                "-->
    <!--                style="font-weight: bold; color: #0f4444"></i>-->
    <!--            </el-col>-->

    <!--            <el-col-->
    <!--              :span="3"-->
    <!--              class="flex just_around">-->
    <!--              <el-button-->
    <!--                class="edit_btn"-->
    <!--                @click="addClass(item, index)">-->
    <!--                新增-->
    <!--              </el-button>-->
    <!--            </el-col>-->
    <!--          </el-row>-->
    <!--          <el-row-->
    <!--            class="class_item"-->
    <!--            v-for="(item, index1) in classList"-->
    <!--            :key="item.classId"-->
    <!--            v-show="gradeActive == index">-->
    <!--            <el-col-->
    <!--              :span="10"-->
    <!--              class="class">-->
    <!--              <i class="dot"></i>-->
    <!--              {{ item.className }}-->
    <!--            </el-col>-->
    <!--            <el-col :span="12">-->
    <!--              <span class="opacity_6">别名：</span>-->
    <!--              {{ item.anotherName }}-->
    <!--            </el-col>-->
    <!--            &lt;!&ndash; <el-col :span="6">-->
    <!--              <span class="opacity_6">备注：</span>-->
    <!--              谢谢谢谢谢-->
    <!--            </el-col> &ndash;&gt;-->
    <!--            <el-col-->
    <!--              :span="2"-->
    <!--              class="flex just_around">-->
    <!--              <el-button-->
    <!--                type="text"-->
    <!--                class="edit_btn_text"-->
    <!--                @click="editClass(item)">-->
    <!--                编辑-->
    <!--              </el-button>-->
    <!--              <el-button-->
    <!--                type="text"-->
    <!--                class="del_btn_text"-->
    <!--                @click="delClass(item, index1)">-->
    <!--                删除-->
    <!--              </el-button>-->
    <!--            </el-col>-->
    <!--          </el-row>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Title from '@/components/Title';
import MyDialog from '@/components/MyDialog';
import {
  baseStageList,
  getBaseGradeList,
  getBaseClassList,
  addBaseGrade,
  editBaseGrade,
  delBaseGrade,
  getTeacherList,
  getStudentList,
  addStudent,
  getProfileExist,
  getInfoByidcard,
  addTeacher,
  delTeacher,
  editGradeMange,
  editClassMange,
  setTeacherSchool,
  teacherInfo,
  graduation,
  addBaseClass,
  editBaseClass,
  delBaseClass,
  getBaseClass,
} from '@/api/youjiao';
export default {
  dicts: ['grade_period'],
  data() {
    return {
      loading: false,
      actived: 0,
      gradeActive: null,
      showGradeDialog: false,
      showClassDialog: false,
      dialogState: 'add',
      stageList: [], //分校列表
      gradeList: [], //年级列表
      chooseGradeId: '',
      classList: [], //班级列表
      years: [], //年

      gradeRules: {
        stageId: [
          {
            required: true,
            message: '请选择分校',
            trigger: 'blur',
          },
        ],
        period: [
          {
            required: true,
            message: '请选择学段',
            trigger: 'blur',
          },
        ],
        year: [
          {
            required: true,
            message: '请选择年',
            trigger: 'blur',
          },
        ],
        classNums: [
          {
            required: false,
            message: '请填写班级数量',
            trigger: 'blur',
          },
        ],
      },
      classRules: {
        cdsSort: [
          {
            required: true,
            message: '请填写班序',
            trigger: 'blur',
          },
        ],
      },
      classDatas: {
        cdsSort: '',
        anotherName: '',
      },
      gradeDatas: {
        year: '',
        classNums: '',
        period: '',
        stageId: '',
      },
    };
  },
  components: {
    Title,
    MyDialog,
  },
  computed: {
    ...mapGetters(['role', 'school']),
  },
  async created() {
    //获取分校
    let stageList = await baseStageList({
      agencyId: this.role.agency_id,
    });
    //总校管理员
    if (this.role.is_branch == false) {
      //如果总校只有1个，当做分校处理
      if (stageList.length > 1) {
        stageList.map((item) => {
          //排除总校数据
          if (item.schoolRelated == '1') {
            this.stageList.push(item);
          }
        });
      } else {
        this.stageList = stageList;
      }
    } else {
      this.gradeDatas.stageId = stageList[0].stageId;
      this.stageList = stageList;
    }

    this.$store.dispatch('SetStageId', stageList[0].stageId);

    //获取年级
    this.getBaseGradeList(this.stageList[0].stageId);

    //获取年
    for (var i = -2; i < 9; i++) {
      this.years.push(new Date().getFullYear() - i);
    }
    this.gradeDatas.year = new Date().getFullYear();
  },
  methods: {
    //选择分校
    selectStage(r, index) {
      this.actived = index;
      this.gradeActive = null;
      this.getBaseGradeList(r.stageId);
    },
    //弹窗中选择分校
    selectStageDialog(e) {
      this.stageList.map((item, index) => {
        if (item.stageId == e) {
          this.actived = index;
          this.getBaseGradeList(e);
        }
      });
    },
    //获取年级信息
    async getBaseGradeList(id) {
      this.loading = true;
      this.gradeList = await getBaseGradeList({ stageId: id });
      this.loading = false;
    },
    //关闭班级弹窗
    closeClassDialog() {
      this.showGradeDialog = false;
      this.gradeDatas = {
        year: '',
        classNums: '',
        period: '',
        stageId: '',
      };
    },
    //保存年级
    saveGrade() {
      this.$refs.gradeForm.validate((valid) => {
        if (valid) {
          let apiParmas =
            this.dialogState == 'add' ? addBaseGrade : editBaseGrade;
          this.loading = true;
          this.gradeDatas.classNums = this.gradeDatas.classNums
            ? this.gradeDatas.classNums
            : 1;
          apiParmas(this.gradeDatas).then((res) => {
            if (res.code == 200) {
              this.showGradeDialog = false;
              this.$modal.msgSuccess('操作成功');
              this.getBaseGradeList(this.gradeDatas.stageId);
            }
          });
        }
      });
    },
    //编辑年级
    editGarde(item) {
      this.dialogState = 'edit';
      this.gradeDatas = item;
      this.showGradeDialog = true;
    },
    //删除年级
    delBaseGrade(id, index) {
      this.$modal.confirm('是否删除年级？').then(() => {
        delBaseGrade(id).then((res) => {
          this.$modal.msgSuccess('删除成功');
          this.gradeList.splice(index, 1);
        });
      });
    },
    //点击新增班级按钮
    addClass(item, index) {
      this.classDatas = {};
      this.showClassDialog = true;
      this.dialogState = 'add';
      this.classInfo = item;
      this.gradeActive = index;
      this.showClass(item);
    },
    editClass(item, index) {
      this.classDatas.anotherName = item.anotherName;
      this.classDatas.cdsSort = item.cdsSort;
      this.classInfo = item;
      this.dialogState = 'edit';
      this.showClassDialog = true;
      this.gradeActive = index;
    },
    delClass(item, index) {
      this.$modal.confirm('是否删除班级？').then(() => {
        delBaseClass(item.classId).then((res) => {
          this.$modal.msgSuccess('删除成功');
          this.classList.splice(index, 1);
        });
      });
    },
    //保存班级
    saveClass() {
      this.$refs.classForm.validate((valid) => {
        if (valid) {
          let apiParmas =
            this.dialogState == 'add' ? addBaseClass : editBaseClass;
          this.loading = true;
          let datas = {
            cdsSort: this.classDatas.cdsSort,
            gradeId: this.classInfo.gradeId,
            anotherName: this.classDatas.anotherName,
            classId: this.classInfo.classId,
          };

          apiParmas(datas).then((res) => {
            console.log(res);
            if (res.code == 200) {
              this.showClassDialog = false;
              this.$modal.msgSuccess('操作成功');
              this.loading = false;

              this.showClass(this.classInfo, 'edit');
              //this.getBaseGradeList(this.gradeDatas.stageId);
            }
          });
        }
      });
    },
    //显示班级
    showClass(item, type) {
      //this.loading = true;

      if (this.chooseGradeId == item.gradeId) {
        if (type != 'edit') {
          this.classList = [];
          this.chooseGradeId = '';
        }
        getBaseClass({ gradeId: item.gradeId }).then((res) => {
          //this.loading = false;
          this.classList = res;
        });
      } else {
        getBaseClass({ gradeId: item.gradeId }).then((res) => {
          //this.loading = false;
          this.classList = res;
        });
        this.chooseGradeId = item.gradeId;
        // this.showGradeDialog = true;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.stage_warp {
  padding: 23px 0;
  display: flex;
  color: #0f4444;
  font-size: 1.8rem;
  font-weight: 700;
  .item {
    margin-right: 30px;
    position: relative;
    cursor: pointer;
  }
  .active::after {
    content: '';
    position: absolute;
    width: 40px;
    border-bottom: 4px #0f4444 solid;

    bottom: -3px;
    left: 0;
  }
}
.box {
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .search {
    margin-right: 20px;
  }
  ::v-deep .el-input__inner {
    border-radius: 20px !important;
  }

  .baseInfo {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin-top: 20px;
  }
  .classWarp {
    display: flex;
    flex-direction: column;

    .lists {
      margin: 10px 0;

      // height: 40px;
      // line-height: 40px;
      border-radius: 6px;
      // padding: 10px 0px;
      color: #44506a;
      font-size: 1.5rem;
      .grade {
        font-size: 2rem;
        color: #0f4444;
        cursor: pointer;
        font-weight: 700;
        display: flex;
        align-items: center;
      }
      .class_item {
        line-height: 40px;
        padding: 0 30px;
        border-radius: 6px;
        background-color: #f6fafb;
        margin: 10px 0;
      }
      .class {
        font-size: 2rem;
        color: #3b8989;

        font-weight: 700;
        display: flex;
        align-items: center;
      }
      .active {
        color: #3b8989;
      }
    }
  }
  .gradeWarp {
    display: flex;
    flex-direction: column;

    .lists {
      margin: 10px 0;

      background-color: #f6fafb;
      /*height: 40px;*/
      line-height: 40px;
      border-radius: 6px;
      padding: 10px 30px;
      color: #44506a;
      font-size: 1.5rem;
      .grade {
        font-size: 2rem;
        color: #3b8989;
        font-weight: 700;
        display: flex;
        align-items: center;
      }
      .class_item {
        line-height: 40px;
        padding: 0 30px;
        border-radius: 6px;
        background-color: #f6fafb;
        margin: 10px 0;
      }
      .class {
        font-size: 2rem;
        color: #3b8989;

        font-weight: 700;
        display: flex;
        align-items: center;
      }
      .active {
        color: #3b8989;
      }
    }
  }
  .schools {
    display: grid;

    .item {
      background-color: #f6fafb;
      margin-bottom: 10px;
    }
    .title {
      font-size: 1.5rem;
      opacity: 0.6;
      font-weight: 500;
    }
    .name {
      font-size: 1.5rem;
      opacity: 0.8;
    }
    .classes {
      padding: 10px;
      height: 103px;
      overflow: scroll;
    }
    .bar {
      border-top: 1px #dee1e2 solid;
      padding: 5px;
      border-radius: 6px;
      display: flex;
      button {
        flex: 1;
        height: 40px;
        border: none;
      }
    }
    grid-template-columns: repeat(3, 30%);
  }
}
</style>
