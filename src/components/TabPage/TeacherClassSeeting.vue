<!-- 教师班级管理 -->
<template>
  <div v-loading="loading">
    <!-- 学生转校弹窗-->
    <studentLeave
      v-if="showChangeDialog"
      @close="showChangeDialog = false"
      @confirm="leave($event, 2)"
      :schoolInfo="pramas"></studentLeave>
    <!--撤销毕业-->
    <studentGraduate
      v-if="showReturnStudent"
      @close="showReturnStudent = !showReturnStudent"></studentGraduate>
    <!--上传学生-->
    <uploadStudent
      v-if="showUploadStudent"
      @close="showUploadStudent = !showUploadStudent"></uploadStudent>
    <!-- 学生转班-->
    <MyDialog
      v-if="showLeaveDialog"
      @close="showLeaveDialog = false"
      dialogWidth="320px"
      title="转班"
      @confirm="changeClass">
      <el-form label-width="80px">
        <el-form-item label="选择班级:">
          <SchoolOrg
            :stageId="role.stage_id"
            :checkStrictly="false"
            :clearable="false"
            :emitPath="true"
            @change="selectClassId = $event"></SchoolOrg>
        </el-form-item>
      </el-form>
    </MyDialog>
    <!-- 新增or修改学生-->
    <addStudent
      v-if="showStudentDialog"
      @close="showStudentDialog = !showStudentDialog"
      :showStudentDialog="showStudentDialog"
      :showMessageError="showMessageError"
      @studentInfo="saveStudent"
      :dialogState="dialogState"
      :userInfo="userInfo"></addStudent>

    <!--留言弹窗-->
    <MyDialog
      :title="'给' + userInfo.userName + '进行留言'"
      dialogWidth="450px"
      v-if="showMessageDialog"
      @close="showMessageDialog = false"
      @confirm="sendMessage()">
      <div class="m_b_30">
        <el-input
          type="textarea"
          v-model="message"
          placeholder="请输入留言信息(1000字以内)"
          :autosize="{ minRows: 8, maxRows: 22 }"
          maxlength="1000"
          show-word-limit>
          123
        </el-input>
      </div>
    </MyDialog>
    <MyDialog
      title="设置副班主任"
      dialogWidth="350px"
      v-if="showDeputyDialog"
      @close="showDeputyDialog = false"
      @confirm="setClassMange()">
      <div class="items">
        <el-select v-model="adviser">
          <el-option
            v-for="item in teacherList"
            :key="item.userId"
            :label="item.userName"
            :value="item.teacherId"
            :disabled="
              _.isEmpty(
                teacherAdviserList.find(
                  (item1) => item1.teacherId == item.teacherId
                )
              ) == false
            "></el-option>
        </el-select>
      </div>
    </MyDialog>
    <el-tabs>
<!--      <el-tab-pane label="班级管理1">-->
<!--      <el-tab-pane>-->
        <div class="box">
          <div>
            <SchoolOrg
              :gradeId="pramas.gradeId"
              :classId="teacherInfo.id"
              :treeData="role.gTree"
              :checkStrictly="false"
              :clearable="false"
              :emitPath="true"
              @change="selectClass"></SchoolOrg>
          </div>
          <div
            class="warp"
            v-show="teacherInfo.adviser == 1">
            <div class="flex just_between m_t_10">
              <Title title="副班主任"></Title>
              <el-button
                class="edit_btn"
                @click="showDeputyDialog = true"
                v-show="teacherInfo.adviser == 1">
                新增
              </el-button>
            </div>
            <div class="teachers">
              <el-empty v-if="teacherAdviserList.length == 0"></el-empty>
              <div
                class="item"
                v-for="item in teacherAdviserList"
                :key="item.teacherId">
                <div class="flex aligin_center text">
                  <div class="avatar">
                    <img
                      :src="
                        item.headIcon
                          ? item.headIcon
                          : item.sex == 0
                          ? male
                          : female
                      " />
                  </div>
                  <div>{{ item.userName }}</div>
                  <div>
                    <span class="opacity_6">任教学科：</span>
                    {{ item.courseNames }}
                  </div>
                  <div>
                    <span class="opacity_6">联系方式：</span>
                    {{ item.phone }}
                  </div>
                </div>
                <div class="btn">
                  <el-button
                    type="primary"
                    @click="delClassMange(item)">
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <div class="warp">
            <div class="flex just_between m_t_10">
              <div class="flex aligin_center">
                <Title title="学生花名册"></Title>
                <span
                  class="m_l_20 fs_15"
                  style="color: #6a94ff">
                  共计：{{ pageTotal }}人
                </span>
              </div>
              <div class="flex">
                <el-input
                  @change="getStudentLists()"
                  v-model="pramas.userName"
                  type="search"
                  suffix-icon="el-icon-search"
                  @click-suffix="getStudentLists"
                  class="search"
                  placeholder="输入学生姓名进行搜索"></el-input>
                <el-button
                  class="edit_btn"
                  @click="addStudent">
                  新增
                </el-button>

                <el-button
                  class="edit_btn"
                  @click="showUploadStudent = true">
                  导入学生
                </el-button>
                <el-button
                  v-if="teacherInfo.adviser != 0"
                  class="edit_btn"
                  @click="exportStudents">
                  导出学生
                </el-button>
                <!-- <el-button
                  class="edit_btn"
                  @click="showChangeDialog = true">
                  转校记录
                </el-button> -->
                <el-button
                  v-if="teacherInfo.adviser != 0"
                  class="edit_btn"
                  @click="showReturnStudent = true">
                  毕业记录
                </el-button>
              </div>
            </div>
            <div class="students">
              <Student
                v-for="item in studentList"
                :key="item.studentId"
                :userInfo="item">
                <div
                  class="flex just_around"
                  style="width: 100%">
<!--                  <el-button-->
<!--                    class="edit_btn_text"-->
<!--                    @click="leaveMessage(item)">-->
<!--                    留言-->
<!--                  </el-button>-->

                  <el-button
                    v-show="teacherInfo.adviser != 0"
                    class="edit_btn_text"
                    @click="edit(item)">
                    编辑
                  </el-button>
                  <el-popover
                    v-show="teacherInfo.adviser != 0"
                    v-if="item.status != 2"
                    placement="right"
                    width="200"
                    trigger="click">
                    <div class="set_btn">
                      <el-button
                        type="primary"
                        @click="leave(item, 1)">
                        转校
                      </el-button>
                      <el-button
                        type="primary"
                        @click="
                          (showLeaveDialog = true),
                            (userInfo = item),
                            (selectClassId = null)
                        ">
                        转班
                      </el-button>
                      <el-button
                        type="primary"
                        @click="leave(item, 2)">
                        休学
                      </el-button>
                      <el-button
                        type="primary"
                        @click="graduate(item, 1)">
                        毕业
                      </el-button>
                      <el-button
                        type="primary"
                        @click="del(item)">
                        删除
                      </el-button>
                      <el-button
                        type="primary"
                        @click="reset(item)">
                        密码重置
                      </el-button>
                    </div>
                    <el-button
                      slot="reference"
                      class="edit_btn_text">
                      其他操作
                    </el-button>
                  </el-popover>
                  <el-button
                    v-show="teacherInfo.adviser != 0"
                    class="edit_btn_text"
                    v-if="item.status == 2"
                    @click="leave(item, 0)">
                    返校
                  </el-button>
                </div>
              </Student>
            </div>
            <div>
              <el-empty v-if="studentList.length == 0"></el-empty>
            </div>
            <div class="flex just_center m_t_10">
              <el-pagination
                v-if="paginationShow"
                background
                hide-on-single-page
                layout="prev, pager, next"
                ref="pagination"
                :total="pageTotal"
                :page-size="pramas.pageSize"
                @current-change="handleChange($event)"></el-pagination>
            </div>
          </div>
        </div>
<!--      </el-tab-pane>-->
      <!-- <el-tab-pane label="班级体锻">2</el-tab-pane>
      <el-tab-pane label="工具">3</el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
import male from '@/assets/avatar/maleTeacher.png';
import female from '@/assets/avatar/femaleTeacher.png';
import {
  baseStageList,
  getBaseGradeList,
  getBaseClassList,
  addBaseGrade,
  editBaseGrade,
  delBaseGrade,
  getTeacherList,
  getStudentList,
  addStudent,
  editStudent,
  getProfileExist,
  getInfoByidcard,
  addTeacher,
  delTeacher,
  editGradeMange,
  editClassMange,
  setTeacherSchool,
  teacherInfo,
  graduation,
  addBaseClass,
  editBaseClass,
  delBaseClass,
  getBaseClass,
  transferOut,
  delStudent,
  getStudentInfo,
} from '@/api/youjiao';
import { resetPwd } from '@/api/login';
import { getListByClass, outClass } from '@/api/classes';
import studentLeave from '@/components/studentLeave';
import Title from '@/components/Title.vue';
import SchoolOrg from '@/components/SchoolOrg.vue';
import Student from '@/components/Student.vue';
import MyDialog from '@/components/MyDialog.vue';
import MyForms from '@/components/MyForms';
import uploadStudent from '@/components/uploadStudent';
import studentGraduate from '@/components/studentGraduate';
import AddStudent from '@/components/AddStudent';
import { mapGetters } from 'vuex';
export default {
  dicts: ['idcard_type', 'kinship_type'],
  // provide() {
  //   return {
  //     reload: this.reload,
  //   };
  // },
  data() {
    return {
      male,
      female,
      teacherInfo: '',
      showDeputyDialog: false,
      showMessageDialog: false,
      showStudentDialog: false,
      showLeaveDialog: false,
      showUploadStudent: false,
      showReturnStudent: false,
      showChangeDialog: false,
      dialogState: 'add',
      componentKey: 0,

      pramas: {
        pageNum: 1,
        pageSize: 12,
      },
      adviser: [],
      studentList: [],
      teacherList: [],
      teacherAdviserList: [], //副班主任
      paginationShow: false,
      pageTotal: 0,
      loading: false,
      userInfo: '',
      message: '',
      selectClassId: '',
      showMessageError: false,
    };
  },
  computed: {
    ...mapGetters(['role']),
  },
  components: {
    Title,
    SchoolOrg,
    Student,
    MyDialog,
    MyForms,
    uploadStudent,
    studentGraduate,
    studentLeave,
    AddStudent,
  },
  created() {
    //获取第一个班的教师角色
    if (this.role.gTree.length > 0) {
      if (this.role.gTree[0].children.length > 0) {
        this.teacherInfo = this.role.gTree[0].children[0];
        this.pramas.gradeId = this.role.gTree[0].id;
        this.pramas.classId = this.teacherInfo.id;

        this.getStudentLists();
        this.getListByClass();
        this.getAdviserByClass();
      }
    }
  },
  methods: {
    selectClass(e, datas) {
      this.pramas.gradeId = e[0];
      this.pramas.classId = e[1];

      this.teacherInfo = datas;
      this.getStudentLists();

      this.getListByClass();
      this.getAdviserByClass();
    },
    //获取班级老师
    async getListByClass() {
      const { rows: list } = await getListByClass({
        classId: this.teacherInfo.id,
        pageNum: 1,
        pageSize: 100,
      });
      this.teacherList = list;
    },
    //获取副班主任
    async getAdviserByClass() {
      //班主任角色进行查询
      if (this.teacherInfo.adviser > 0) {
        const { rows: list } = await getListByClass({
          classId: this.teacherInfo.id,
          pageNum: 1,
          adviser: 2,
          pageSize: 100,
        });
        this.teacherAdviserList = list;
      }
    },
    async getStudentLists() {
      this.loading = true;

      const { rows: list, total } = await getStudentList(this.pramas);
      this.pageTotal = total;
      this.paginationShow = true;
      this.studentList = list;
      this.loading = false;
    },
    handleChange(event) {
      this.pramas.pageNum = event;
      this.getStudentLists();
    },
    //删除副班主任
    delClassMange(item) {
      this.$modal.confirm('是否删除班主任？').then(() => {
        editClassMange({
          editType: 2,
          classId: this.pramas.classId,
          gradeId: this.pramas.gradeId,
          teacherId: item.teacherId,
        }).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('删除成功');
            this.getAdviserByClass();
          }
        });
      });
    },
    //设置副班主任
    setClassMange() {
      if (this.adviser) {
        editClassMange({
          editType: 2,
          classId: this.pramas.classId,
          gradeId: this.pramas.gradeId,
          teacherId: this.adviser,
        }).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('设置成功');
            this.showDeputyDialog = false;
            this.adviser = '';
            this.getAdviserByClass();
          }
        });
      } else {
        this.$modal.msgWarning('请选择老师');
      }
    },
    //学生毕业
    graduate(res, type) {
      var map = {},
        result = [];

      //单个学生毕业
      if (type == 1) {
        result = [
          {
            gradeId: res.gradeId,
            classs: [
              {
                classId: res.classId,
                studentIds: [res.studentId],
              },
            ],
          },
        ];
      } else {
        if (res.length > 0) {
          let arr = res.map((item) => {
            return {
              gradeId: item[0],
              classId: item[1],
              studentId: item[2],
            };
          });

          for (var i in arr) {
            var ai = arr[i];
            if (!map[ai.gradeId]) {
              result.push({
                gradeId: ai.gradeId,
                classs: [
                  {
                    classId: ai.classId,
                    studentIds: [ai.studentId],
                  },
                ],
              });
              map[ai.gradeId] = ai;
            } else {
              result.map((item) => {
                if (item.gradeId == ai.gradeId) {
                  item.classs.map((item1) => {
                    if (item1.classId == ai.classId) {
                      item1.studentIds.push(ai.studentId);
                    } else {
                      if (!map[ai.classId]) {
                        item.classs.push({
                          classId: ai.classId,
                          studentIds: ai.studentId ? [ai.studentId] : [],
                        });
                        map[ai.classId] = ai;
                      } else {
                        item.classs.map((item) => {
                          if (item.classId == ai.classId) {
                            map[ai.studentId] = ai;
                            if (!map[ai.studentId]) {
                              item.studentIds.push(ai.studentId);
                            }
                          }
                        });
                      }
                    }
                  });
                }
              });
            }
          }
        } else {
          this.$modal.msgWarning('请选择毕业学生');
          return;
        }
      }
      this.$modal.confirm('是否毕业操作？').then(() => {
        graduation(result).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('操作成功');
            this.getStudentLists();
          }
        });
      });
    },
    //学生删除
    del(e) {
      this.$modal.confirm('是否删除？').then(() => {
        delStudent(e.studentId).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('操作成功');
            this.getStudentLists();
          }
        });
      });
    },
    //学生转校休学返校
    leave(e, type) {
      let ids = [];

      ids = [e.studentId];

      if (ids.length > 0) {
        this.$modal
          .confirm(type == 1 ? '是否转校' : type == 2 ? '是否休学' : '是否返校')
          .then(() => {
            transferOut(ids.toString(), type).then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess('操作成功');
                this.getStudentLists();
              }
            });
          });
      } else {
        this.$modal.msgWarning('请选择学生');
      }
    },
    //留言
    leaveMessage(item) {
      this.userInfo = item;
      this.showMessageDialog = true;
    },
    sendMessage() {
      if (this.message) {
      } else {
        this.$modal.msgWarning('请填写内容');
      }
    },
    //保存学生
    saveStudent(r) {
      r.stageId = this.role.stage_id;
      r.gradeId = this.pramas.gradeId;
      r.classId = this.pramas.classId;
      if (this.dialogState == 'add') {
        addStudent(r).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('添加成功');
            this.showStudentDialog = false;
            this.getStudentLists();
          }
        });
      } else {
        editStudent(r).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('修改成功');
            this.showStudentDialog = false;
            this.getStudentLists();
          }
        });
      }
    },
    //添加学生
    addStudent() {
      this.showStudentDialog = true;
      this.dialogState = 'add';
    },

    //转班
    changeClass() {
      if (this.selectClassId) {
        outClass({
          studentId: this.userInfo.studentId,
          classId: this.selectClassId[1],
        }).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('转班成功');
            this.showLeaveDialog = false;
            this.getStudentLists();
          }
        });
      } else {
        this.$modal.msgWarning('请选择班级');
      }
    },
    //导出学生
    exportStudents() {
      this.$download.file('学生信息', '/portal/student/export', this.pramas);
    },
    //修改学生
    async edit(items) {
      this.userInfo = items;
      this.showStudentDialog = true;

      this.dialogState = 'edit';
    },
    //重置密码
    reset(item) {
      this.$modal.confirm('是否重置密码？').then(() => {
        resetPwd(item.userId, 'student').then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('重置成功');
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-empty {
  padding: 0;
}
::v-deep .el-tabs__content {
  border-top: 0px !important;
  padding: 0 !important;
}
::v-deep .el-tabs__nav-scroll {
  padding: 0 !important;
}
.set_btn {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .el-button {
    width: 100% !important;
    margin: 5px 0;
  }
}
.warp {
  margin-bottom: 30px;
}
.items {
  padding: 0 20px 30px 40px;
  // height: 180px;
  // margin-bottom: 20px;
  // padding: 5px;
  // overflow: auto;
  // border: 1px #ccc solid;
  // .list {
  //   font-size: 1.8rem;
  //   padding: 5px;
  // }
}
.teachers {
  .avatar img {
    width: 77px;
    height: 77px;
    object-fit: cover;
  }
  .item {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    opacity: 0.8;
    padding: 20px;
    border-radius: 6px;
    background-color: #f6fafb;
    justify-content: space-between;
    margin: 10px 0;
    .text > div {
      margin-right: 50px;
    }
  }
}
.students {
  display: grid;
  margin-top: 30px;
  grid-template-columns: repeat(4, 280px);
  grid-auto-rows: 190px;
  justify-content: space-between;
}
.box {
  .search {
    margin-right: 20px;
  }
  ::v-deep .el-input__inner {
    border-radius: 20px !important;
  }
}
</style>
