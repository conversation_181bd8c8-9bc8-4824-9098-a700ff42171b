<!-- 机构设置 -->
<template>
  <div
    class="container"
    v-loading="loading">
    <MyDialog
      title="新增机构"
      v-if="showNewOrgan"
      @close="showNewOrgan = !showNewOrgan"
      @confirm="$refs.forms.getFormsDatas()">
      <Forms
        :columns="newOrganForm"
        :editable="true"
        ref="forms"
        @formsDatas="addNewOrgan"
        labelWidth="12rem"></Forms>
    </MyDialog>
    <MyDialog
      :title="dialogState == 'add' ? '新增机构' : '编辑机构'"
      v-if="showAddBranchSchool"
      @close="showAddBranchSchool = !showAddBranchSchool"
      @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms
          :columns="schoolInfoForms"
          :editable="true"
          ref="forms"
          @formsDatas="AddBranchSchool"
          labelWidth="15rem"></Forms>
      </div>
    </MyDialog>
    <div class="content box">
      <!--   机构设置   -->
      <div class="">
        <div class="head">
          <el-select
            v-model="parameter.agencyType"
            placeholder="全部"
            style="width: 110px"
            clearable
            @clear="clearSelect">
            <el-option
              @click.native="search"
              v-for="dict in dict.type['agency_type']"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
          <div>
            <el-input
              @change="search"
              placeholder="输入名称进行搜索"
              style="width: 200px; margin-right: 2rem"
              v-model="parameter.fullName"
              suffix-icon="el-icon-search"
              @click-suffix.native="search"></el-input>
            <el-button
              class="edit_btn"
              @click="newOrgan">
              新增
            </el-button>
          </div>
        </div>
        <div class="baseInfo gradeWarp">
          <div
            class="lists"
            v-for="(item, index) in contents.rows"
            :key="index">
            <el-row>
              <el-col
                :span="21"
                class="grade"
                :class="gradeActive != index ? 'grade' : 'grade active'"
                @click.native="
                  showStage(item),
                    gradeActive == index
                      ? (gradeActive = null)
                      : (gradeActive = index)
                ">
                <!--              <i class="dot"></i>-->
                <i
                  :class="
                    item.stageNums != 0
                      ? gradeActive != index
                        ? 'el-icon-arrow-right'
                        : 'el-icon-arrow-down'
                      : 'dot'
                  "
                  style="
                    font-weight: bold;
                    color: #0f4444;
                    border: #0f4444;
                  "></i>
                {{ item.fullName }}
              </el-col>
              <!--              <el-col :span="21">-->
              <!--                {{item.introduce}}-->
              <!--              </el-col>-->
              <!-- <el-col :span="11">
                <span class="opacity_6">备注：</span>
                谢谢谢谢谢
              </el-col> -->
              <el-col
                :span="3"
                class="flex just_around">
                <el-button
                  type="text"
                  class="edit_btn_text"
                  @click="gotoDetail(item, index)">
                  详情
                </el-button>
                <el-button
                  type="text"
                  class="edit_btn_text"
                  v-if="item.agencyType == '2'"
                  @click="showAddBranchCampus(item)">
                  新增下级机构
                </el-button>
                <el-button
                  type="text"
                  class="del_btn_text"
                  @click="delBaseGrade(item)">
                  删除
                </el-button>
              </el-col>
            </el-row>
            <el-row
              class="class_item"
              v-for="(item, index1) in stageList"
              :key="item.stageId"
              v-show="gradeActive == index">
              <el-col
                :span="10"
                class="class">
                <i class="dot"></i>
                {{ item.fullName }}
              </el-col>
              <!--              <el-col :span="12">-->
              <!--                <span class="opacity_6">别名：</span>-->
              <!--                {{ item.anotherName }}-->
              <!--              </el-col>-->
              <!-- <el-col :span="6">
                <span class="opacity_6">备注：</span>
                谢谢谢谢谢
              </el-col> -->
              <el-col
                :span="2"
                class="flex just_around">
                <el-button
                  type="text"
                  class="edit_btn_text"
                  @click="editBranchSchool(item)">
                  编辑
                </el-button>
                <el-button
                  type="text"
                  class="del_btn_text"
                  @click="DeleteBranchSchool(item)">
                  删除
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="contents flex">
          <!--          <div-->
          <!--            style="margin-bottom: 10px"-->
          <!--            v-for="(item, index) in contents.rows"-->
          <!--            :key="index">-->
          <!--            <SchoolList :info="item"></SchoolList>-->
          <!--          </div>-->
          <div class="paging">
            <el-pagination
              :page-size="parameter.pageSize"
              background
              :total="contents.total"
              :current-page="parameter.pageNum"
              @current-change="handleCurrentChange"></el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  addAgency,
  deleteAgency,
  getBaseAgencyList,
  addBaseDept,
  addBaseSatge,
  deleteBaseStage,
  deleteBaseDept,
  getTeacherList,
  getTeacher,
  getSchooList,
  addTeacher,
  getStaffList,
  getBaseAgency,
} from '@/api/zhili/index';
import { baseStageList, updateBaseStage } from '@/api/youjiao/index';
import SchoolList from '@/components/SchoolList.vue';
import MyDialog from '@/components/MyDialog.vue';
import Forms from '@/components/Forms.vue';
import Title from '@/components/Title.vue';
import { getBaseClass } from '@/api/youjiao';
// 新增学校信息表单
export function schoolInfo() {
  return [
    {
      prop: 'agencyName',
      label: '机构简称',
      placeholder: '请输入机构简称',
      type: 'input',
      default: '',
      required: true,
    },
    {
      prop: 'fullName',
      label: '机构全称',
      placeholder: '请输入机构全称',
      type: 'input',
      default: '',
      required: true,
    },
    {
      prop: 'address',
      label: '地址',
      placeholder: '请输入地址',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsFountschoolDate',
      label: '成立时间',
      placeholder: '请输入时间',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'introduce',
      label: '简介',
      placeholder: '请输入简介',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLegalPerson',
      label: '法人姓名',
      placeholder: '请输入法人姓名',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLegalpersonIdcard',
      label: '法人身份证号',
      placeholder: '请输入法人身份证号',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalName',
      label: '负责人姓名',
      placeholder: '请输入负责人姓名',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalIdcard',
      label: '负责人身份证号',
      placeholder: '请输入负责人身份证号',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalPhone',
      label: '负责人联系电话',
      placeholder: '请输入负责人联系电话',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'agencyLogo',
      label: 'logo地址',
      placeholder: '请输入logo地址',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLongitude',
      label: '经纬度',
      placeholder: '请输入经纬度',
      type: 'input',
      default: '',
      required: false,
    },
    // {
    //   prop: 'subtopic',
    //   label: '子主题',
    //   placeholder: '请输入子主题',
    //   type: 'input',
    //   default: '',
    //   required: false,
    // },
    {
      prop: 'cdsSiteArea',
      label: '用地面积',
      placeholder: '请输入用地面积',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsCoveredArea',
      label: '建筑面积',
      placeholder: '请输入建筑面积',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsUsableArea',
      label: '使用面积',
      placeholder: '请输入使用面积',
      type: 'input',
      default: '',
      required: false,
    },
  ];
}
// 新增分校信息表单
export function branchSchoolInfo() {
  return [
    // {
    //   prop: 'agencyName',
    //   label: '机构简称',
    //   placeholder: '请输入机构简称',
    //   type: 'input',
    //   default: '',
    //   required: true,
    // },
    {
      prop: 'fullName',
      label: '机构全称',
      placeholder: '请输入机构全称',
      type: 'input',
      default: '',
      required: true,
    },
    {
      prop: 'address',
      label: '地址',
      placeholder: '请输入地址',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsFountschoolDate',
      label: '成立时间',
      placeholder: '请输入时间',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'introduce',
      label: '简介',
      placeholder: '请输入简介',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLegalPerson',
      label: '法人姓名',
      placeholder: '请输入法人姓名',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLegalpersonIdcard',
      label: '法人身份证号',
      placeholder: '请输入法人身份证号',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalName',
      label: '负责人姓名',
      placeholder: '请输入负责人姓名',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalIdcard',
      label: '负责人身份证号',
      placeholder: '请输入负责人身份证号',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalPhone',
      label: '负责人联系电话',
      placeholder: '请输入负责人联系电话',
      type: 'input',
      default: '',
      required: false,
    },
    // {
    //   prop: 'agencyLogo',
    //   label: 'logo地址',
    //   placeholder: '请输入logo地址',
    //   type: 'input',
    //   default: '',
    //   required: false,
    // },
    {
      prop: 'cdsLongitude',
      label: '经纬度',
      placeholder: '请输入经纬度',
      type: 'input',
      default: '',
      required: false,
    },
    // {
    //   prop: 'subtopic',
    //   label: '子主题',
    //   placeholder: '请输入子主题',
    //   type: 'input',
    //   default: '',
    //   required: false,
    // },
    {
      prop: 'cdsSiteArea',
      label: '用地面积',
      placeholder: '请输入用地面积',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsCoveredArea',
      label: '建筑面积',
      placeholder: '请输入建筑面积',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsUsableArea',
      label: '使用面积',
      placeholder: '请输入使用面积',
      type: 'input',
      default: '',
      required: false,
    },
  ];
}
export default {
  dicts: ['agency_type', 'school_setup'],
  data() {
    return {
      loading: false,
      gradeActive: null,
      chooseAgencyId: '',
      stageList: [], //班级列表
      //   新增弹窗
      showNewOrgan: false,
      showAddOragns: false,
      showAddUnit: false,
      showAddSchool: false,
      showAddmember: false,
      showAddBranchSchool: false,
      showAddCourse: false,
      teachingDialog: false,
      showAddTeacherDialog: false,
      parameter: {
        pageSize: 10,
        pageNum: 1,
        fullName: '',
        agencyType: '',
      },
      contents: '', //学校数据
      schoolInfoForms: [],
      dialogState: 'add',
      //   弹窗数据
      newOrganForm: [],
      // 提交的信息
      campus: {
        agencyType: '',
      },
    };
  },
  components: {
    // SchoolList,
    MyDialog,
    Forms,
  },
  created() {
    this.getList();
  },
  methods: {
    // 选择器清空刷新
    clearSelect() {
      this.parameter.agencyType = '';
      this.getList();
    },
    // 搜索
    search() {
      this.getList();
    },

    // 机构设置页面新增机构弹窗
    newOrgan() {
      this.newOrganForm = [
        {
          prop: 'agencyType',
          label: '机构类型',
          placeholder: '请选择机构类型',
          type: 'select',
          multiple: false,
          default: '',
          options: [
            { value: 2, label: '学校' },
            { value: 3, label: '培训机构' },
          ],
          required: true,
        },
        {
          prop: 'schoolType',
          label: '学校类型',
          placeholder: '请选择学校类型',
          type: 'dict',
          dictType: 'school_type',
          default: '',
          options: [],
          required: true,
        },
        {
          prop: 'schoolSetup',
          label: '办学类型',
          placeholder: '请选择办学类型',
          type: 'dict',
          dictType: 'school_setup',
          default: '',
          options: [],
          required: true,
        },
      ].concat(schoolInfo());
      this.showNewOrgan = true;
    },
    // 页数跳转
    handleCurrentChange(page) {
      // sessionStorage.setItem('SchoolListPage',page);
      this.parameter.pageNum = page;
      this.getList();
    },
    //  获取机构信息
    async getList() {
      this.loading = true;
      const data = await getBaseAgencyList(this.parameter);
      this.contents = data;
      console.log(data, '机构信息');
      this.loading = false;
    },

    // // 新增学校实现
    // AddCampus(res) {
    //   res = Object.assign(res, this.campus);
    //   let formData = {
    //     label: res.fullName,
    //     parentId: this.nodeParentId,
    //     addDept: null,
    //     // id:"03137AFB29814DD5B60CE7D6321C88FF"
    //   };
    //   this.$refs.customtree.treeAddNode(formData);
    //   console.log(res,'xinde');
    //   addAgency(res)
    //     .then(() => {
    //       this.$message.success('添加成功！');
    //       // const data=this.getOrganControllList();
    //       // console.log(data,'添加后')
    //     })
    //     .catch(() => {});
    //   this.showAddSchool = false;
    //   this.campus.agencyType = '';
    // },
    // 机构设置页面新增机构实现
    addNewOrgan(res) {
      console.log(res, '新增');
      addAgency(res).then(() => {
        this.$message.success('机构添加成功!');
        this.getList();
      });
      this.showNewOrgan = false;
    },
    gotoDetail(item, index) {
      this.$router.push({
        path: '/zhili/organization/detail',
        query: {
          id: item.agencyId,
        },
      });
    },
    //显示分校
    showStage(item) {
      //this.loading = true;
      console.log(item);
      if (this.chooseAgencyId == item.agencyId || item.stageNums == 0) {
        this.stageList = [];
        this.chooseAgencyId = '';
      } else {
        baseStageList({ agencyId: item.agencyId }).then((res) => {
          //this.loading = false;
          this.stageList = res;
        });
        this.chooseAgencyId = item.agencyId;
      }
    },
    // 新增分校
    showAddBranchCampus(data) {
      console.log(data);
      this.dialogState = 'add';
      this.campus.agencyType = '2'; //学校
      this.campus.agencyId = data.agencyId;
      this.schoolInfoForms = [
        {
          prop: 'schoolType',
          label: '学校类型',
          placeholder: '请选择学校类型',
          type: 'dict',
          dictType: 'school_type',
          default: '',
          options: [],
          required: true,
        },
        {
          prop: 'schoolSetup',
          label: '办学类型',
          placeholder: '请选择办学类型',
          type: 'dict',
          dictType: 'school_setup',
          default: '',
          options: [],
          required: true,
        },
      ].concat(branchSchoolInfo());
      this.showAddBranchSchool = true;
    },
    //编辑年级
    editBranchSchool(data) {
      this.dialogState = 'edit';
      this.campus.agencyType = '2'; //学校
      this.campus.agencyId = data.agencyId;
      this.campus.stageId = data.stageId;
      this.schoolInfoForms = [
        {
          prop: 'schoolType',
          label: '学校类型',
          placeholder: '请选择学校类型',
          type: 'dict',
          dictType: 'school_type',
          default: '',
          options: [],
          required: true,
        },
        {
          prop: 'schoolSetup',
          label: '办学类型',
          placeholder: '请选择办学类型',
          type: 'dict',
          dictType: 'school_setup',
          default: '',
          options: [],
          required: true,
        },
      ].concat(branchSchoolInfo());
      this.schoolInfoForms.forEach((item) => {
        let prop = item.prop;
        item.default = data[prop];
      });
      //this.schoolInfoForms=item
      this.showAddBranchSchool = true;
    },
    // 新增分校实现
    AddBranchSchool(res) {
      res = Object.assign(res, this.campus);
      // console.log(res)
      let stageId = res.stageId;
      if (stageId == null) {
        addBaseSatge(res)
          .then(() => {
            // this.getOrganControllList();
            this.$message.success('添加成功!');
            //关闭
            this.showStage(this.campus);
            //打开
            this.showStage(this.campus);
          })
          .catch(() => {});
      } else {
        updateBaseStage(res)
          .then(() => {
            // this.getOrganControllList();
            this.$message.success('修改成功!');
            //关闭
            this.showStage(this.campus);
            //打开
            this.showStage(this.campus);
          })
          .catch(() => {});
      }

      this.showAddBranchSchool = false;
    },
    // 删除分校
    DeleteBranchSchool(data) {
      console.log(data, '删除分校');
      this.$modal
        .confirm('确定删除机构吗？')
        .then(() => {
          deleteBaseStage(data.stageId).then(() => {
            this.$modal.msgSuccess('删除成功！');
            //关闭
            this.showStage(data);
            //打开
            this.showStage(data);
            // this.getOrganControllList();
          });
        })
        .catch(() => {});
    },
    // 删除学校
    delBaseGrade(data) {
      // console.log(item,'删除学校')
      this.$modal
        .confirm('确定删除机构吗？')
        .then(() => {
          deleteAgency(data.agencyId).then(() => {
            this.$modal.msgSuccess('删除成功！');
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  .box {
    border-radius: 8px;
    padding: 0 20px;
    background-color: #fff;
    display: flex;
  }
  .content-box {
    margin: 3rem 0;
    height: 50px;
    align-items: center;
    .btn_anniu {
      margin: 0 1rem;
      height: 3.7rem;
      font-size: 1.8rem;
      font-weight: bold;
      border: 0 solid #fff;
      color: #0f4440;
      opacity: 0.5;
      cursor: pointer;
      outline: none;
      background: #fff;
    }
    .newStyle {
      position: relative;
      color: #0f4440;
      font-size: 1.8rem;
      font-weight: bold;
      opacity: 0.9;
    }
    .newStyle:before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 50%;
      height: 1px;
      border-bottom: 0.4rem solid #0f4440;
      box-sizing: border-box;
      opacity: 0.9;
    }
  }
  .content {
    //background-color:#fff;
    margin-bottom: 3rem;
    .head {
      width: 1100px;
      margin: 2rem 0;
      wdith: 50px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .contents {
      display: flex;
      flex-direction: row;
      //justify-content: space-between;
      flex-wrap: wrap;
      .paging {
        margin-top: 30px;
        width: 100%;
        text-align: center;
        margin-bottom: 20px;
      }
    }
    .gradeWarp {
      display: flex;
      flex-direction: column;

      .lists {
        margin: 10px 0;

        background-color: #f6fafb;
        /*height: 40px;*/
        line-height: 40px;
        border-radius: 6px;
        padding: 10px 30px;
        color: #44506a;
        font-size: 1.5rem;
        .grade {
          font-size: 2rem;
          color: #3b8989;
          font-weight: 700;
          display: flex;
          align-items: center;
        }
        .class_item {
          line-height: 40px;
          padding: 0 30px;
          border-radius: 6px;
          background-color: #f6fafb;
          margin: 10px 0;
        }
        .class {
          font-size: 2rem;
          color: #3b8989;

          font-weight: 700;
          display: flex;
          align-items: center;
        }
        .active {
          color: #3b8989;
        }
      }
    }
  }
}
.schoolDialog {
  .el-select {
    display: flex;
    flex: 1;
  }
}
::v-deep .head .el-select .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;
  font-size: 16px;
  font-weight: 700;

  &::placeholder {
    color: #0f4444;
  }
}

::v-deep .head .el-select .el-icon-arrow-up:before {
  content: '\e78f';
  color: #0f4444;
}

::v-deep .head .el-select .el-input.is-focus .el-input__inner {
  border-color: #0f4444;
}

::v-deep .head .el-input .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;
}
::v-deep .body .el-tree-node__label {
  font-size: 1.6rem;
  color: #0f4444;
  opacity: 0.8;
  font-weight: 600;
}
::v-deep .head .el-select-dropdown__item.selected {
  color: #0f4444;
}
::v-deep .el-collapse-item__arrow {
  display: none;
}
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
</style>
