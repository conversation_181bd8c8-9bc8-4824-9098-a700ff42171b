<!-- 学科管理 -->
<template>
  <div
    class="container"
    v-loading="loading">
    <!--    新增学科弹窗-->
    <MyDialog
      title="新增学科"
      v-if="showAddCourse"
      dialogWidth="380px"
      @close="showAddCourse = !showAddCourse"
      @confirm="Courseconfirm">
      <div
        style="
          display: flex;
          justify-content: space-between;
          height: 40px;
          line-height: 40px;
        ">
        <div>学科名称:</div>
        <el-input
          placeholder="请输入学科名称"
          style="width: 233px"
          v-model="infos.courseName"></el-input>
      </div>
      <div
        style="
          display: flex;
          justify-content: space-between;
          height: 40px;
          line-height: 40px;
          margin: 2rem 0;
        ">
        <div>学段:</div>
        <el-select
          style="width: 233px"
          v-model="infos.gradeLevel">
          <el-option
            v-for="item in stageOptions"
            :key="item.gradeLevel"
            :label="item.label"
            :value="item.gradeLevel"></el-option>
        </el-select>
      </div>
    </MyDialog>
    <div class="content box">
      <div class="">
        <div class="head">
          <Title title="学科列表"></Title>
          <div>
            <el-input
              @change="searchCourse"
              placeholder="输入名称进行搜索"
              suffix-icon="el-icon-search"
              @click-suffix.native="searchCourse"
              style="width: 200px; margin-right: 2rem"
              v-model="CourseParameter.courseName"></el-input>
            <el-button
              class="edit_btn"
              @click="AddCourse">
              新增
            </el-button>
          </div>
        </div>
        <div
          v-for="(item, index) in CourseList.rows"
          :key="index">
          <CourseList :info="item"></CourseList>
        </div>
        <div class="course-paging">
          <el-pagination
            :page-size="CourseParameter.pageSize"
            background
            :total="CourseList.total"
            :current-page="CourseParameter.pageNum"
            @current-change="handleCourseChange"></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getTeacherList,
  getSchooList,
  getCourseList,
  addCourse,
} from '@/api/zhili/index';
import MyDialog from '@/components/MyDialog.vue';
import Title from '@/components/Title.vue';
import CourseList from '@/components/CourseList.vue';
export default {
  dicts: ['school_type', 'school_setup'],
  data() {
    return {
      info: [],
      loading: false,
      //   新增弹窗
      showNewOrgan: false,
      showAddOragns: false,
      showAddUnit: false,
      showAddSchool: false,
      showAddmember: false,
      showAddBranchSchool: false,
      showAddCourse: false,
      teachingDialog: false,
      showAddTeacherDialog: false,
      CourseName: '', //学科名称
      schoolInfoForms: [],
      schoolOption: [],
      //   弹窗数据
      newOrganForm: [],
      infos: {
        courseName: '',
        gradeLevel: '',
      },
      option: this.schoolOption,
      CourseList: [], //学科列表
      CourseParameter: {
        pageSize: 10,
        pageNum: 1,
        courseName: '', //学科名称
      },
      stageOptions: [
        {
          label: '幼儿园',
          gradeLevel: '01',
        },
        {
          label: '小学',
          gradeLevel: '02',
        },
        {
          label: '初中',
          gradeLevel: '03',
        },
        {
          label: '高中',
          gradeLevel: '04',
        },
      ],
    };
  },
  components: {
    MyDialog,
    CourseList,
    Title,
  },
  created() {
    // this.getTeacherLists();
    this.getCourseLists(); //获取学科列表
    // 处理学校列表 保留fullName stageId
    getSchooList().then((res) => {
      res.forEach((item) => {
        let obj = {
          label: '',
          value: '',
        };
        obj.label = item.fullName;
        obj.value = item.stageId;
        this.schoolOption.push(obj);
      });
    });
  },
  methods: {
    // 获取教师列表
    // async getTeacherLists(parameter) {
    //   this.loading = true;
    //   if (parameter == null) {
    //     this.teacherContents = await getTeacherList(this.teacherParamter);
    //   } else {
    //     this.teacherParamter = parameter;
    //     this.teacherContents = await getTeacherList(this.teacherParamter);
    //   }
    //   this.loading = false;
    //   // this.loading = true;
    //   // const data= await getTeacherList(this.teacherParamter);
    //   // this.teacherContents=data;
    //   // console.log(data,'教师列表')
    //   // this.loading = false;
    // },
    // 获取学科列表
    async getCourseLists() {
      this.loading = true;
      const data = await getCourseList(this.CourseParameter);
      console.log(data.rows, '学科列表');
      this.CourseList = data;
      this.loading = false;
    },
    // 学科搜索
    searchCourse() {
      this.getCourseLists();
    },
    // 学科列表跳转
    handleCourseChange(page) {
      this.CourseParameter.pageNum = page;
      this.getCourseLists();
    },
    // 新增学科弹窗
    AddCourse() {
      this.showAddCourse = true;
    },
    // 新增学科弹窗确认 实现
    Courseconfirm() {
      console.log(this.infos);
      addCourse(this.infos).then(() => {
        this.$message.success('添加成功!');
        this.getCourseLists();
      });
      this.showAddCourse = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  .box {
    border-radius: 8px;
    // padding: 0 20px;

    background-color: #fff;
    display: flex;
  }
  .content-box {
    margin: 3rem 0;
    height: 50px;
    align-items: center;
    .btn_anniu {
      margin: 0 1rem;
      height: 3.7rem;
      font-size: 1.8rem;
      font-weight: bold;
      border: 0 solid #fff;
      color: #0f4440;
      opacity: 0.5;
      cursor: pointer;
      outline: none;
      background: #fff;
    }
    .newStyle {
      position: relative;
      color: #0f4440;
      font-size: 1.8rem;
      font-weight: bold;
      opacity: 0.9;
    }
    .newStyle:before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 50%;
      height: 1px;
      border-bottom: 0.4rem solid #0f4440;
      box-sizing: border-box;
      opacity: 0.9;
    }
  }
  .content {
    //background-color:#fff;
    margin-bottom: 3rem;
    .head {
      width: 1100px;
      margin: 2rem 0;
      wdith: 50px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .contents {
      display: flex;
      flex-direction: row;
      //justify-content: space-between;
      flex-wrap: wrap;
      .paging {
        margin-top: 30px;
        width: 100%;
        text-align: center;
        margin-bottom: 20px;
      }
    }
  }
}
.course-paging {
  margin-top: 30px;
  width: 100%;
  text-align: center;
  margin-bottom: 20px;
}
.schoolDialog {
  .el-select {
    display: flex;
    flex: 1;
  }
}
::v-deep .head .el-select .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;
  font-size: 16px;
  font-weight: 700;

  &::placeholder {
    color: #0f4444;
  }
}

::v-deep .head .el-select .el-icon-arrow-up:before {
  content: '\e78f';
  color: #0f4444;
}

::v-deep .head .el-select .el-input.is-focus .el-input__inner {
  border-color: #0f4444;
}

::v-deep .head .el-input .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;
}
::v-deep .body .el-tree-node__label {
  font-size: 1.6rem;
  color: #0f4444;
  opacity: 0.8;
  font-weight: 600;
}
::v-deep .head .el-select-dropdown__item.selected {
  color: #0f4444;
}
::v-deep .el-collapse-item__arrow {
  display: none;
}
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
</style>
