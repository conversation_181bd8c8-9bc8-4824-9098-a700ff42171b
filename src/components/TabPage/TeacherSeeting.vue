<!-- 老师设置 -->
<template>
  <div v-loading="loading">
    <!-- 新增教师-->
    <MyDialog
      v-if="showTeacherDialog"
      dialogWidth="550px"
      @close="(showTeacherDialog = false), (showMessageError = false)"
      @confirm="$refs.teacher.getFormsDatas()"
      :title="dialogState == 'add' ? '新增教师' : '修改信息'">
      <div class="schoolDialog">
        <MyForms
          :columns="teacherDatas"
          editable
          ref="teacher"
          :showMessageError="showMessageError"
          :modify="modify"
          :key="componentKey"
          :dialogState="dialogState"
          labelWidth="90px"
          @change="getTeacherInfo"
          @formsDatas="saveTeacher"></MyForms>
      </div>
    </MyDialog>
    <!-- 分校选择-->
    <div
      class="stage_warp"
      v-show="role.is_branch == false">
      <div
        @click="selectStage_2(item, index)"
        :class="actived == index ? 'item active' : 'item'"
        v-for="(item, index) in stageList"
        :key="item.stageId">
        {{ item.fullName }}
      </div>
    </div>
    <div class="box">
      <div class="flex aligin_center">
        <SchoolOrg
          ref="schoolTree1"
          @change="selectOrg"></SchoolOrg>
        <!-- <Title title="学生管理"></Title> -->
      </div>
      <div class="flex">
        <el-input
          @change="getTeacherList()"
          v-model="pramas.userName"
          type="search"
          class="search"
          suffix-icon="el-icon-search"
          @click-suffix="getTeacherList"
          placeholder="输入老师姓名进行搜索"></el-input>
        <el-button
          class="edit_btn"
          @click="addTeacher">
          新增
        </el-button>
      </div>
      <div class="student_warp">
        <div class="flex aligin_center">
          <!-- <Title title="老师设置"></Title>
          <div
            class="m_l_20 fs_15"
            style="color: rgb(106, 148, 255)">
            共计：{{ pageTotal }}人
          </div> -->
          <div
            class="m_l_20 fs_15 fw_700"
            style="color: #0f4444">
            共计：{{ pageTotal }}人
          </div>
        </div>
        <div class="students">
          <Teacher
            v-for="item in teacherList"
            :key="item.teacherId"
            :userInfo="item"
            @del="delTeacher"
            @set="setRole"
            @edit="editTeacher"
            :stageList="stageList"
            ref="teachers"></Teacher>
        </div>
        <div>
          <el-empty v-if="teacherList.length == 0"></el-empty>
        </div>
        <div class="flex just_center">
          <el-pagination
            v-if="paginationShow"
            background
            hide-on-single-page
            :page-size="pramas.pageSize"
            layout="prev, pager, next"
            ref="pagination"
            :total="pageTotal"
            @current-change="handleChange($event)"></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import SchoolOrg from '@/components/SchoolOrg';
import MyDialog from '@/components/MyDialog';
import MyForms from '@/components/MyForms';
import Teacher from '@/components/Teacher';
import Title from '@/components/Title';
import {
  baseStageList,
  getBaseGradeList,
  getBaseClassList,
  addBaseGrade,
  editBaseGrade,
  delBaseGrade,
  getTeacherList,
  getStudentList,
  addStudent,
  getProfileExist,
  getInfoByidcard,
  addTeacher,
  editTeacher,
  delTeacher,
  editGradeMange,
  editClassMange,
  setTeacherSchool,
  teacherInfo,
  graduation,
  addBaseClass,
  editBaseClass,
  delBaseClass,
  getBaseClass,
  getTeacherByPhone,
} from '@/api/youjiao';

import { create } from 'lodash';
export default {
  data() {
    return {
      loading: false,
      dialogState: 'add',
      showTeacherDialog: false,
      stageList: [],
      teacherList: [],
      actived: 0,
      componentKey: 0,
      modify: true,
      paginationShow: false,
      showMessageError: false,
      pageTotal: 0,
      pramas: {
        pageNum: 1,
        pageSize: 12,
      }, //查询参数
      teacherDatas: [
        {
          prop: 'headIcon',
          label: '教师头像',
          placeholder: '请上传教师头像',
          type: 'photo',
          default: '',
          required: false,
        },
        {
          prop: 'phone',
          label: '手机号码',
          placeholder: '请输入手机号码',
          type: 'idcard',
          default: '',
          required: true,
        },

        {
          prop: 'userName',
          label: '教师姓名',
          placeholder: '请输入教师姓名',
          type: 'input',
          default: '',
          required: true,
        },

        {
          prop: 'sex',
          label: '教师性别',
          placeholder: '请选择教师性别',
          type: 'dict',
          dictType: 'sys_user_sex',
          multiple: false,
          default: '',
          options: [],
          required: true,
        },
        {
          prop: 'idcardType',
          label: '证件类型',
          placeholder: '请选择证件类型',
          type: 'dict',
          dictType: 'idcard_type',
          options: [],
          default: '',
          required: false,
        },
        {
          prop: 'userId',
          label: '',
          placeholder: '请输入证件号码',
          type: 'hidden',
          default: '',
        },
        {
          prop: 'teacherId',
          label: '',
          placeholder: '请输入证件号码',
          type: 'hidden',
          default: '',
        },
        {
          prop: 'idcard',
          label: '证件号码',
          placeholder: '请输入证件号码',
          type: 'input',
          default: '',
          required: false,
        },
        {
          prop: 'oicq',
          label: 'qq号码',
          placeholder: '请输入qq号码',
          type: 'input',
          default: '',
        },
        {
          prop: 'wechat',
          label: '微信号码',
          placeholder: '请输入微信号码',
          type: 'input',
          default: '',
        },
        {
          prop: 'email',
          label: '邮箱号码',
          placeholder: '请输入邮箱号码',
          type: 'input',
          default: '',
        },
      ],
    };
  },
  computed: {
    ...mapGetters(['role', 'school']),
  },
  components: {
    Teacher,
    MyDialog,
    SchoolOrg,
    MyForms,
    Title,
  },
  async created() {
    //获取分校
    let stageList = await baseStageList({
      agencyId: this.role.agency_id,
    });
    //总校管理员
    if (this.role.is_branch == false) {
      //如果总校只有1个，当做分校处理
      if (stageList.length > 1) {
        stageList.map((item) => {
          //排除总校数据
          if (item.schoolRelated == '1') {
            this.stageList.push(item);
          }
        });
      } else {
        this.stageList = stageList;
      }
    } else {
      this.stageList = stageList;
    }
    //获取第一个分校的老师
    this.pramas.stageId = this.stageList[0].stageId;

    //获取第一个分校的班级
    this.$refs.schoolTree1.getGrade(this.stageList[0].stageId);
    this.$store.dispatch('SetStageId', stageList[0].stageId);
    this.getTeacherList();
  },
  methods: {
    //选择学校
    selectOrg(r) {
      this.pramas.pageNum = 1;
      this.pramas.gradeId = r[0];
      this.pramas.classId = r[1];
      this.$store.dispatch('SetGradeId', r[0]);
      this.$store.dispatch('SetClassId', r[1]);
      this.getTeacherList();

      this.paginationShow = false;
    },
    //填写身份证后获取教师信息
    getTeacherInfo(e) {
      if (e) {
        getTeacherByPhone({ phone: e }).then((res) => {
          this.modify = false;
          this.componentKey += 1;

          if (res.data) {
            this.showMessageError = false;
            //重组数据

            this.teacherDatas.forEach((item) => {
              item.default = res.data[item.prop];
              // if (item.prop != 'idcardType') {
              //   if (item.prop == 'idcard') {
              //     item.default = e;
              //   } else {
              //     item.default = res.rows[0][item.prop];
              //   }
              // }
            });
          } else {
            this.showMessageError = true;
            this.teacherDatas.forEach((item) => {
              if (item.prop == 'phone') {
                item.default = e;
              } else {
                if (item.prop != 'idcardType') {
                  item.default = '';
                }
              }
            });
          }
        });
      } else {
        this.$modal.msgWarning('请输入手机号码');
      }
    },
    //添加老师
    addTeacher() {
      this.modify = true;
      this.showTeacherDialog = true;
      this.dialogState = 'add';
    },
    //保存教师
    saveTeacher(r) {
      r.stageId = this.school.stageId;
      if (this.dialogState == 'add') {
        addTeacher(r).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('添加成功');
            this.showTeacherDialog = false;
            this.getTeacherList();
          }
        });
      } else {
        delete r.headIcon;
        editTeacher(r).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('修改成功');
            this.showTeacherDialog = false;
            this.getTeacherList();
          }
        });
      }
    },
    //教师管理中选择分校
    selectStage_2(item, index) {
      this.actived = index;
      this.pramas.stageId = item.stageId;
      this.$refs.schoolTree1.getGrade(item.stageId);
      this.$store.dispatch('SetStageId', item.stageId);
      this.getTeacherList();
    },
    //获取老师列表
    async getTeacherList() {
      this.loading = true;
      this.pramas.stageId = this.school.stageId;
      this.pramas.gradeId = this.school.gradeId;
      this.pramas.classId = this.school.classId;
      const { rows: list, total } = await getTeacherList(this.pramas);
      this.pageTotal = total;
      this.paginationShow = true;
      this.teacherList = list;
      this.loading = false;
    },
    //删除教师
    delTeacher(id) {
      this.$modal.confirm('是否删除教师？').then(() => {
        delTeacher(id).then(() => {
          this.$modal.msgSuccess('删除成功');
          this.getTeacherList();
        });
      });
    },
    //编辑教师
    async editTeacher(info) {
      //获取教师信息
      const { data } = await teacherInfo(info.teacherId);
      //将信息存入临时表
      this.$store.dispatch('SetTemporary', data.userId);
      this.teacherDatas.forEach((item) => {
        if (item.prop == 'headIcon') {
          item.default = info.headIcon;
        } else {
          item.default = data[item.prop];
        }
      });
      this.dialogState = 'edit';
      this.modify = false;
      this.showTeacherDialog = true;
    },
    handleChange(event) {
      this.pramas.pageNum = event;
      this.getTeacherList();
    },
    //设置老师角色
    setRole(type, uid, ids) {
      if (type == 3) {
        if (_.isEmpty(ids)) {
          this.$modal.msgWarning('请选择班级');
        } else {
          editClassMange({
            editType: 1,
            classId: ids[1],
            gradeId: ids[0],
            teacherId: uid,
          }).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess('设置成功');
              this.getTeacherList();
              this.teacherList.map((item, index) => {
                this.$refs.teachers[index].resetAll();
              });
            }
          });
        }
      } else if (type == 4) {
        if (_.isEmpty(ids)) {
          this.$modal.msgWarning('请选择班级');
        } else {
          editClassMange({
            editType: 2,
            classId: ids[1],
            gradeId: ids[0],
            teacherId: uid,
          }).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess('设置成功');
              this.getTeacherList();
              this.teacherList.map((item, index) => {
                this.$refs.teachers[index].resetAll();
              });
            }
          });
        }
      } else if (type == 2) {
        if (_.isEmpty(ids)) {
          this.$modal.msgWarning('请选择年级');
        } else {
          editGradeMange({
            teacherId: uid,
            stageId: this.school.stageId,
            gradeId: ids[0],
          }).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess('设置成功');
              this.getTeacherList();
              this.teacherList.map((item, index) => {
                this.$refs.teachers[index].resetAll();
              });
            }
          });
        }
      } else if (type == 5) {
        if (_.isEmpty(ids)) {
          this.$modal.msgWarning('请选择校区');
        } else {
          setTeacherSchool({ userId: uid, stageId: ids }).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess('设置成功');
              this.getTeacherList();
              this.teacherList.map((item, index) => {
                this.$refs.teachers[index].resetAll();
              });
            }
          });
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.student_warp {
  display: flex;
  margin-top: 20px;
  width: 100%;
  flex-direction: column;

  .gradeList {
    display: flex;
    font-size: 1.8rem;
    color: #6f8f8f;
    font-weight: 700;
    .list {
      margin-right: 30px;
      cursor: pointer;
    }
    .active {
      color: #0f4444;
    }
    .active::after {
      content: '⮟';
    }
  }
  .classes {
    // display: flex;
    font-size: 1.6rem;
    color: #6f8f8f;
    font-weight: 700;

    .item {
      margin-top: 12px;

      margin-right: 30px;
      float: left;
      cursor: pointer;
      position: relative;
    }
    .active {
      color: #0f4444;
    }
    .active::after {
      content: '';
      border-bottom: 4px #0f4444 solid;
      position: absolute;
      width: 20px;
      bottom: -2px;
      left: 0;
    }
  }
  .students {
    display: grid;
    margin-top: 30px;
    grid-template-columns: repeat(4, 280px);
    grid-auto-rows: 190px;
    justify-content: space-between;
  }
}
.stage_warp {
  padding: 23px 0;
  display: flex;
  color: #0f4444;
  font-size: 1.8rem;
  font-weight: 700;
  .item {
    margin-right: 30px;
    position: relative;
    cursor: pointer;
  }
  .active::after {
    content: '';
    position: absolute;
    width: 40px;
    border-bottom: 4px #0f4444 solid;

    bottom: -3px;
    left: 0;
  }
}
.box {
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .search {
    margin-right: 20px;
  }
  ::v-deep .el-input__inner {
    border-radius: 20px !important;
  }

  .baseInfo {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin-top: 20px;
  }
  .classWarp {
    display: flex;
    flex-direction: column;

    .lists {
      margin: 10px 0;

      // height: 40px;
      // line-height: 40px;
      border-radius: 6px;
      // padding: 10px 0px;
      color: #44506a;
      font-size: 1.5rem;
      .grade {
        font-size: 2rem;
        color: #0f4444;
        cursor: pointer;
        font-weight: 700;
        display: flex;
        align-items: center;
      }
      .class_item {
        line-height: 40px;
        padding: 0 30px;
        border-radius: 6px;
        background-color: #f6fafb;
        margin: 10px 0;
      }
      .class {
        font-size: 2rem;
        color: #3b8989;

        font-weight: 700;
        display: flex;
        align-items: center;
      }
      .active {
        color: #3b8989;
      }
    }
  }
  .gradeWarp {
    display: flex;
    flex-direction: column;

    .lists {
      margin: 10px 0;

      background-color: #f6fafb;
      height: 40px;
      line-height: 40px;
      border-radius: 6px;
      padding: 10px 30px;
      color: #44506a;
      font-size: 1.5rem;
      .grade {
        font-size: 2rem;
        color: #3b8989;
        font-weight: 700;
        display: flex;
        align-items: center;
      }
    }
  }
  .schools {
    display: grid;

    .item {
      background-color: #f6fafb;
      margin-bottom: 10px;
    }
    .title {
      font-size: 1.5rem;
      opacity: 0.6;
      font-weight: 500;
    }
    .name {
      font-size: 1.5rem;
      opacity: 0.8;
    }
    .classes {
      padding: 10px;
      height: 103px;
      overflow: scroll;
    }
    .bar {
      border-top: 1px #dee1e2 solid;
      padding: 5px;
      border-radius: 6px;
      display: flex;
      button {
        flex: 1;
        height: 40px;
        border: none;
      }
    }
    grid-template-columns: repeat(3, 30%);
  }
}
.el-table .success-row {
  background: #f0f9eb;
}
</style>
