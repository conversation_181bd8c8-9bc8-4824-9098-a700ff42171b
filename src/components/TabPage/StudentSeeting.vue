<!-- 学生设置 -->
<template>
  <div v-loading="loading">
    <!--撤销毕业-->
    <studentGraduate
      v-if="showReturnStudent"
      @close="showReturnStudent = !showReturnStudent"></studentGraduate>
    <!--上传学生-->
    <uploadStudent
      v-if="showUploadStudent"
      @close="showUploadStudent = !showUploadStudent"></uploadStudent>
    <!-- 新增or修改学生-->
    <addStudent
      v-if="showStudentDialog"
      @close="showStudentDialog = !showStudentDialog"
      :showStudentDialog="showStudentDialog"
      :showMessageError="showMessageError"
      @studentInfo="saveStudent"
      :dialogState="dialogState"
      :userInfo="userInfo"
      :showSchool="true"></addStudent>
    <!-- 新增学生-->
    <!-- <MyDialog
      v-if="showStudentDialog"
      dialogWidth="550px"
      @confirm="$refs.student.getFormsDatas()"
      @close="(showStudentDialog = false), (showMessageError = false)"
      :title="dialogState == 'add' ? '新增学生' : '修改学生'">
      <div class="schoolDialog">
        <MyForms
          ref="student"
          :columns="studentDatas"
          editable
          :showMessageError="showMessageError"
          :modify="modify"
          :key="componentKey"
          labelWidth="142px"
          @change="getStudentInfo"
          @formsDatas="saveStudent"></MyForms> -->
    <!-- 监护人信息-->
    <!-- <el-form
          label-width="130px"
          :model="elders"
          :rules="eldersRules"
          ref="elder">
          <el-form-item
            label="监护人姓名:"
            prop="userName">
            <el-input
              v-model="elders.userName"
              placeholder="请填写监护人姓名"></el-input>
          </el-form-item>
          <el-form-item
            label="监护人证件类型:"
            prop="idcardType">
            <el-select
              v-model="elders.idcardType"
              placeholder="请选择证件类型">
              <el-option
                v-for="dict in dict.type['idcard_type']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="监护人证件号:"
            prop="idcard">
            <el-input
              v-model="elders.idcard"
              placeholder="请填写证件号"></el-input>
          </el-form-item>
          <el-form-item
            label="监护人手机:"
            prop="phone">
            <el-input
              v-model="elders.phone"
              placeholder="请填写手机号"></el-input>
          </el-form-item>
          <el-form-item
            label="监护人亲属关系:"
            prop="kinship">
            <el-select
              v-model="elders.kinship"
              placeholder="请选择监护人亲属关系">
              <el-option
                v-for="dict in dict.type['kinship_type']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="elders.kinship == 99"
            label="亲属关系备注:"
            prop="kinshipOther">
            <el-input
              placeholder="请填写关系"
              v-model="elders.kinshipOther"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </MyDialog> -->
    <!-- 学生毕业弹窗-->
    <MyDialog
      v-if="showGraduateDialog"
      @close="showGraduateDialog = false"
      dialogWidth="320px"
      title="毕业"
      @confirm="$refs.students.getFormsDatas()">
      <el-form label-width="80px">
        <el-form-item label="选择年级:">
          <SchoolStudentOrg
            :stageId="school.stageId"
            ref="students"
            @getFormsDatas="graduate"></SchoolStudentOrg>
        </el-form-item>
      </el-form>
    </MyDialog>
    <!-- 学生转校弹窗-->
    <studentLeave
      v-if="showLeaveDialog"
      @close="showLeaveDialog = false"
      @confirm="leave($event, 2)"></studentLeave>
    <!-- <MyDialog
      v-if="showLeaveDialog"
      @close="showLeaveDialog = false"
      dialogWidth="620px"
      title="转校"
      @confirm="$refs.students.getFormsDatas()">
      <el-form label-width="80px">
        <el-form-item label="选择年级:">
          <SchoolStudentOrg
            :stageId="school.stageId"
            ref="students"
            @getFormsDatas="leave($event, 2)"></SchoolStudentOrg>
        </el-form-item>
      </el-form>
    
    </MyDialog> -->
    <!-- 分校选择-->
    <div
      class="stage_warp"
      v-show="role.is_branch == false">
      <div
        @click="selectStage_3(item, index)"
        :class="actived == index ? 'item active' : 'item'"
        v-for="(item, index) in stageList"
        :key="item.stageId">
        {{ item.fullName }}
      </div>
    </div>
    <div class="box">
      <div class="flex aligin_center">
        <SchoolOrg
          ref="schoolTree2"
          @change="selectOrg"></SchoolOrg>
      </div>
      <div class="flex">
        <el-input
          @change="getStudentList()"
          v-model="pramas.userName"
          suffix-icon="el-icon-search"
          @click-suffix="getStudentList"
          type="search"
          class="search"
          placeholder="输入学生姓名进行搜索"></el-input>

        <el-button
          class="edit_btn"
          @click="addStudent">
          新增
        </el-button>
        <el-button
          class="edit_btn"
          @click="showUploadStudent = true">
          导入学生
        </el-button>
        <el-button
          class="edit_btn"
          @click="exportStudents">
          导出学生
        </el-button>
        <el-button
          class="edit_btn"
          @click="showLeaveDialog = true">
          转校
        </el-button>
        <el-button
          class="edit_btn"
          @click="showGraduateDialog = true">
          毕业
        </el-button>
        <el-button
          class="edit_btn"
          @click="showReturnStudent = true">
          毕业记录
        </el-button>
      </div>

      <div class="student_warp">
        <div class="flex aligin_center">
          <!-- <Title title="学生管理"></Title> -->
          <div
            class="m_l_20 fs_15 fw_700"
            style="color: #0f4444">
            共计：{{ pageTotal }}人
          </div>
        </div>
        <div class="students">
          <Student
            v-for="item in studentList"
            :key="item.studentId"
            :userInfo="item">
            <div
              class="flex just_around"
              style="width: 100%">
              <el-button
                class="edit_btn_text"
                @click="graduate(item, 1)">
                毕业
              </el-button>

              <el-button
                class="edit_btn_text"
                @click="leave(item)">
                转校
              </el-button>
              <el-button
                class="edit_btn_text"
                @click="edit(item)">
                编辑
              </el-button>

              <el-button
                class="del_btn_text"
                @click="del(item)">
                删除
              </el-button>
              <el-button
                class="edit_btn_text"
                @click="reset(item)">
                密码重置
              </el-button>
            </div>
          </Student>
        </div>
        <div>
          <el-empty v-if="studentList.length == 0"></el-empty>
        </div>
        <div class="flex just_center p_tb_10">
          <el-pagination
            v-if="paginationShow"
            background
            hide-on-single-page
            layout="prev, pager, next"
            ref="pagination"
            :page-size="pramas.pageSize"
            :total="pageTotal"
            @current-change="handleChange($event)"></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { tansParams } from '@/utils/tools';
import MyDialog from '@/components/MyDialog';
import MyForms from '@/components/MyForms';
import { mapGetters } from 'vuex';
import SchoolOrg from '@/components/SchoolOrg';
import uploadStudent from '@/components/uploadStudent';
import studentGraduate from '@/components/studentGraduate';
import Student from '@/components/Student';
import studentLeave from '@/components/studentLeave';
import SchoolStudentOrg from '@/components/SchoolStudentOrg';
import AddStudent from '@/components/AddStudent';
import Title from '@/components/Title';
import { saveAs } from 'file-saver';
import {
  baseStageList,
  getBaseGradeList,
  getBaseClassList,
  addBaseGrade,
  editBaseGrade,
  delBaseGrade,
  getTeacherList,
  getStudentList,
  addStudent,
  editStudent,
  getProfileExist,
  getInfoByidcard,
  addTeacher,
  delTeacher,
  editGradeMange,
  editClassMange,
  setTeacherSchool,
  teacherInfo,
  graduation,
  addBaseClass,
  editBaseClass,
  delBaseClass,
  getBaseClass,
  transferOut,
  delStudent,
  getStudentInfo,
} from '@/api/youjiao';
import { resetPwd } from '@/api/login';
export default {
  dicts: ['idcard_type', 'kinship_type'],
  data() {
    return {
      userInfo: '',
      loading: false,
      dialogState: 'add',
      showStudentDialog: false,
      showGraduateDialog: false,
      showLeaveDialog: false,
      showUpload: false,
      showMessageError: false,
      showReturnStudent: false,
      stageList: [],
      studentList: [],
      showUploadStudent: false,
      actived: 0,
      componentKey: 0,
      modify: true,
      paginationShow: false,
      pageTotal: 0,
      pramas: {
        pageNum: 1,
        pageSize: 12,
      }, //查询参数
    };
  },
  computed: {
    ...mapGetters(['role', 'school']),
  },
  components: {
    Student,
    MyDialog,
    SchoolOrg,
    MyForms,
    studentLeave,
    uploadStudent,
    studentGraduate,
    SchoolStudentOrg,
    Title,
    AddStudent,
  },
  async created() {
    //获取分校
    let stageList = await baseStageList({
      agencyId: this.role.agency_id,
    });
    //总校管理员
    if (this.role.is_branch == false) {
      //如果总校只有1个，当做分校处理
      if (stageList.length > 1) {
        stageList.map((item) => {
          //排除总校数据
          if (item.schoolRelated == '1') {
            this.stageList.push(item);
          }
        });
      } else {
        this.stageList = stageList;
      }
    } else {
      // this.gradeDatas.stageId = stageList[0].stageId;
      this.stageList = stageList;
    }
    //获取第一个分校的学生
    this.pramas.stageId = this.stageList[0].stageId;
    //获取第一个分校的班级
    this.$refs.schoolTree2.getGrade(this.stageList[0].stageId);
    this.$store.dispatch('SetStageId', stageList[0].stageId);
    this.getStudentList();
  },
  methods: {
    //学生转校
    leave(e, type) {
      let ids = [];
      //批量毕业
      if (type == 2) {
        e.map((item) => {
          ids.push(item[2]);
        });
      } else {
        ids = [e.studentId];
      }
      if (ids.length > 0) {
        this.$modal.confirm('是否转校？').then(() => {
          transferOut(ids.toString(), 1).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess('操作成功');
              this.getStudentList();
              //this.showLeaveDialog = false;
            }
          });
        });
      } else {
        this.$modal.msgWarning('请选择学生');
      }
    },
    //学生删除
    del(e) {
      this.$modal.confirm('是否删除？').then(() => {
        delStudent(e.studentId).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('操作成功');
            this.getStudentList();
          }
        });
      });
    },
    //选择学校
    selectOrg(r) {
      this.pramas.pageNum = 1;
      this.pramas.gradeId = r[0];
      this.pramas.classId = r[1];
      this.$store.dispatch('SetGradeId', r[0]);
      this.$store.dispatch('SetClassId', r[1]);
      this.getStudentList();
      this.paginationShow = false;
    },

    //学生毕业
    graduate(res, type) {
      var map = {},
        result = [];
      //单个学生毕业
      if (type == 1) {
        result = [
          {
            gradeId: res.gradeId,
            classs: [
              {
                classId: res.classId,
                studentIds: [res.studentId],
              },
            ],
          },
        ];
      } else {
        if (res.length > 0) {
          let arr = res.map((item) => {
            return {
              gradeId: item[0],
              classId: item[1],
              studentId: item[2],
            };
          });

          for (var i in arr) {
            var ai = arr[i];
            if (!map[ai.gradeId]) {
              result.push({
                gradeId: ai.gradeId,
                classs: [
                  {
                    classId: ai.classId,
                    studentIds: [ai.studentId],
                  },
                ],
              });
              map[ai.gradeId] = ai;
            } else {
              result.map((item) => {
                if (item.gradeId == ai.gradeId) {
                  item.classs.map((item1) => {
                    if (item1.classId == ai.classId) {
                      item1.studentIds.push(ai.studentId);
                    } else {
                      if (!map[ai.classId]) {
                        item.classs.push({
                          classId: ai.classId,
                          studentIds: ai.studentId ? [ai.studentId] : [],
                        });
                        map[ai.classId] = ai;
                      } else {
                        item.classs.map((item) => {
                          if (item.classId == ai.classId) {
                            map[ai.studentId] = ai;
                            if (!map[ai.studentId]) {
                              item.studentIds.push(ai.studentId);
                            }
                          }
                        });
                      }
                    }
                  });
                }
              });
            }
          }
        } else {
          this.$modal.msgWarning('请选择毕业学生');
          return;
        }
      }
      this.$modal.confirm('是否毕业操作？').then(() => {
        graduation(result).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('操作成功');
            this.showGraduateDialog = false;
            this.getStudentList();
          }
        });
      });
    },
    //获取学生列表
    async getStudentList() {
      this.loading = true;
      this.pramas.stageId = this.school.stageId;
      this.pramas.gradeId = this.school.gradeId;
      this.pramas.classId = this.school.classId;
      const { rows: list, total } = await getStudentList(this.pramas);
      this.pageTotal = total;
      this.paginationShow = true;
      this.studentList = list;
      this.loading = false;
    },
    //分页
    handleChange(event) {
      this.pramas.pageNum = event;
      this.getStudentList();
    },
    //学生管理中选择分校
    selectStage_3(item, index) {
      this.actived = index;
      this.pramas.stageId = item.stageId;
      //获取学校数
      this.$refs.schoolTree2.getGrade(item.stageId);

      this.$store.dispatch('SetStageId', item.stageId);
      this.getStudentList();
    },
    //保存学生
    saveStudent(r) {
      if (this.dialogState == 'add') {
        addStudent(r).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('添加成功');
            this.showStudentDialog = false;
            this.getStudentLists();
          }
        });
      } else {
        editStudent(r).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('修改成功');
            this.showStudentDialog = false;
            this.getStudentLists();
          }
        });
      }
    },
    //添加学生
    addStudent() {
      this.showStudentDialog = true;
      this.dialogState = 'add';
    },
    //导出学生
    exportStudents() {
      this.$download.file('学生信息', '/portal/student/export', this.pramas);
    },
    //修改学生
    async edit(items) {
      this.userInfo = items;
      this.showStudentDialog = true;

      this.dialogState = 'edit';
    },
    //重置密码
    reset(item) {
      this.$modal.confirm('是否重置密码？').then(() => {
        resetPwd(item.userId, 'student').then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess('重置成功');
          }
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.btns {
  margin-left: 10px;
  // border: 1px #000 solid;
  // width: 100%;
}
.student_warp {
  display: flex;
  margin-top: 20px;
  width: 100%;
  flex-direction: column;

  .gradeList {
    display: flex;
    font-size: 1.8rem;
    color: #6f8f8f;
    font-weight: 700;
    .list {
      margin-right: 30px;
      cursor: pointer;
    }
    .active {
      color: #0f4444;
    }
    .active::after {
      content: '⮟';
    }
  }
  .classes {
    // display: flex;
    font-size: 1.6rem;
    color: #6f8f8f;
    font-weight: 700;

    .item {
      margin-top: 12px;

      margin-right: 30px;
      float: left;
      cursor: pointer;
      position: relative;
    }
    .active {
      color: #0f4444;
    }
    .active::after {
      content: '';
      border-bottom: 4px #0f4444 solid;
      position: absolute;
      width: 20px;
      bottom: -2px;
      left: 0;
    }
  }
  .students {
    display: grid;
    margin-top: 30px;
    grid-template-columns: repeat(4, 280px);
    grid-auto-rows: 190px;
    justify-content: space-between;
  }
}
.stage_warp {
  padding: 23px 0;
  display: flex;
  color: #0f4444;
  font-size: 1.8rem;
  font-weight: 700;
  .item {
    margin-right: 30px;
    position: relative;
    cursor: pointer;
  }
  .active::after {
    content: '';
    position: absolute;
    width: 40px;
    border-bottom: 4px #0f4444 solid;

    bottom: -3px;
    left: 0;
  }
}
.box {
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .search {
    margin-right: 20px;
  }
  ::v-deep .el-input__inner {
    border-radius: 20px !important;
  }

  .baseInfo {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin-top: 20px;
  }
  .classWarp {
    display: flex;
    flex-direction: column;

    .lists {
      margin: 10px 0;

      // height: 40px;
      // line-height: 40px;
      border-radius: 6px;
      // padding: 10px 0px;
      color: #44506a;
      font-size: 1.5rem;
      .grade {
        font-size: 2rem;
        color: #0f4444;
        cursor: pointer;
        font-weight: 700;
        display: flex;
        align-items: center;
      }
      .class_item {
        line-height: 40px;
        padding: 0 30px;
        border-radius: 6px;
        background-color: #f6fafb;
        margin: 10px 0;
      }
      .class {
        font-size: 2rem;
        color: #3b8989;

        font-weight: 700;
        display: flex;
        align-items: center;
      }
      .active {
        color: #3b8989;
      }
    }
  }
  .gradeWarp {
    display: flex;
    flex-direction: column;

    .lists {
      margin: 10px 0;

      background-color: #f6fafb;
      height: 40px;
      line-height: 40px;
      border-radius: 6px;
      padding: 10px 30px;
      color: #44506a;
      font-size: 1.5rem;
      .grade {
        font-size: 2rem;
        color: #3b8989;
        font-weight: 700;
        display: flex;
        align-items: center;
      }
    }
  }
  .schools {
    display: grid;

    .item {
      background-color: #f6fafb;
      margin-bottom: 10px;
    }
    .title {
      font-size: 1.5rem;
      opacity: 0.6;
      font-weight: 500;
    }
    .name {
      font-size: 1.5rem;
      opacity: 0.8;
    }
    .classes {
      padding: 10px;
      height: 103px;
      overflow: scroll;
    }
    .bar {
      border-top: 1px #dee1e2 solid;
      padding: 5px;
      border-radius: 6px;
      display: flex;
      button {
        flex: 1;
        height: 40px;
        border: none;
      }
    }
    grid-template-columns: repeat(3, 30%);
  }
}
</style>
