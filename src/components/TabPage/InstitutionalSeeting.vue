<!-- 组织机构设置 -->
<template>
  <div
    class="container"
    v-loading="loading">
    <div class="content box">
      <!--   机构设置   -->
      <div class="">
        <div
          style="
            display: flex;
            margin-bottom: 2rem;
            justify-content: space-between;
          ">
          <div style="width: 366px; border-radius: 8px">
            <Title
              style="margin-top: 2rem"
              title="组织架构"></Title>
            <Tree
              ref="customtree"
              :treeData="treeData"
              :tree-node-key="treeNodeKey"
              :isForm="true"
              @AddDepartment="showAddDepartment"
              @Delete="Delete"
              @Edit="Edit"
              @AddUnit="showAddUnits"
              @AddCampus="showAddCampus"
              @AddBranchCampus="showAddBranchCampus"
              @deleteOrgan="deleteOrgan"
              @handleNodeClick="handleNodeClick"></Tree>
          </div>
          <div
            style="width: 78rem; margin-left: 2rem; border-radius: 8px"
            v-if="showStaff">
            <div
              style="
                margin-top: 2rem;
                font-size: 1.8rem;
                opacity: 0.8;
                font-weight: 800;
                display: flex;
                justify-content: space-between;
                color: #0f4444;
                align-items: center;
              ">
              <div>职员信息</div>
              <div
                style="cursor: pointer"
                @click="showAddMember">
                新增成员
              </div>
            </div>
            <div style="margin-top: 2rem">
              <div
                style="margin-bottom: 2rem"
                v-for="(item, index) in staffList.rows"
                :key="index">
                <my-staff :info="item"></my-staff>
              </div>
              <div class="paging">
                <el-pagination
                  :page-size="staffParameter.pageSize"
                  background
                  :total="staffList.total"
                  :current-page="staffParameter.pageNum"
                  @current-change="StaffChange"></el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--    // 机构设置页面新增机构-->
    <!--    <MyDialog title="新增机构" v-if="showNewOrgan"-->
    <!--              @close="showNewOrgan=!showNewOrgan"-->
    <!--              @confirm="$refs.forms.getFormsDatas()">-->
    <!--      <Forms :columns="newOrganForm"-->
    <!--             :editable="true"-->
    <!--             ref="forms"-->
    <!--             @formsDatas="addNewOrgan"-->
    <!--             labelWidth="12rem"></Forms>-->
    <!--    </MyDialog>-->

    <MyDialog
      title="新增科室"
      v-if="showAddOragns"
      @close="showAddOragns = !showAddOragns"
      @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms
          :columns="baseDeptsInfo"
          :editable="true"
          ref="forms"
          @formsDatas="AddDepartment"
          labelWidth="15rem"></Forms>
      </div>
    </MyDialog>

    <!--    <MyDialog title="新增直属单位" v-if="showAddUnit"-->
    <!--              @close="showAddUnit=!showAddUnit"-->
    <!--              @confirm="$refs.forms.getFormsDatas()">-->
    <!--      <div>-->
    <!--        <Forms :columns="schoolInfoForms"-->
    <!--               :editable="true"-->
    <!--               ref="forms"-->
    <!--               @formsDatas="AddUnit"-->
    <!--               labelWidth="15rem"></Forms>-->
    <!--      </div>-->
    <!--    </MyDialog>-->

    <!--    <MyDialog title="新增学校" v-if="showAddSchool"-->
    <!--              @close="showAddSchool=!showAddSchool"-->
    <!--              @confirm="$refs.forms.getFormsDatas()">-->
    <!--      <div>-->
    <!--        <Forms :columns="schoolInfoForms"-->
    <!--               :editable="true"-->
    <!--               ref="forms"-->
    <!--               @formsDatas="AddCampus"-->
    <!--               labelWidth="15rem"></Forms>-->
    <!--      </div>-->
    <!--    </MyDialog>-->

    <MyDialog
      title="新增成员"
      v-if="showAddmember"
      @close="showAddmember = !showAddmember"
      @confirm="$refs.forms.getFormsDatas()"
      dialogWidth="880px">
      <div>
        <Forms
          :columns="personalInfo"
          :editable="true"
          ref="forms"
          labelWidth="80px"
          @formsDatas="addStaff"></Forms>
      </div>
    </MyDialog>

    <MyDialog
      title="新增分校"
      v-if="showAddBranchSchool"
      @close="showAddBranchSchool = !showAddBranchSchool"
      @confirm="$refs.forms.getFormsDatas()">
      <div>
        <Forms
          :columns="schoolInfoForms"
          :editable="true"
          ref="forms"
          @formsDatas="AddBranchSchool"
          labelWidth="15rem"></Forms>
      </div>
    </MyDialog>
  </div>
</template>

<script>
import {
  deleteAgency,
  getOrganController,
  addBaseDept,
  deleteBaseStage,
  deleteBaseDept,
  getStaffList,
  addStaff,
  addAgency,
  getBaseDeptList,
} from '@/api/zhili/index';
import MyDialog from '@/components/MyDialog.vue';
import Forms from '@/components/Forms.vue';
import Title from '@/components/Title.vue';
import Tree from '@/components/Tree.vue';
import MyStaff from '@/components/Staff.vue';
export function schoolInfo() {
  return [
    {
      prop: 'agencyName',
      label: '机构简称',
      placeholder: '请输入机构简称',
      type: 'input',
      default: '',
      required: true,
    },
    {
      prop: 'fullName',
      label: '机构全称',
      placeholder: '请输入机构全称',
      type: 'input',
      default: '',
      required: true,
    },
    {
      prop: 'address',
      label: '地址',
      placeholder: '请输入地址',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsFountschoolDate',
      label: '成立时间',
      placeholder: '请输入时间',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'introduce',
      label: '简介',
      placeholder: '请输入简介',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLegalPerson',
      label: '法人姓名',
      placeholder: '请输入法人姓名',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLegalpersonIdcard',
      label: '法人身份证号',
      placeholder: '请输入法人身份证号',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalName',
      label: '负责人姓名',
      placeholder: '请输入负责人姓名',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalIdcard',
      label: '负责人身份证号',
      placeholder: '请输入负责人身份证号',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsPrincipalPhone',
      label: '负责人联系电话',
      placeholder: '请输入负责人联系电话',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'agencyLogo',
      label: 'logo地址',
      placeholder: '请输入logo地址',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsLongitude',
      label: '经纬度',
      placeholder: '请输入经纬度',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'subtopic',
      label: '子主题',
      placeholder: '请输入子主题',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsSiteArea',
      label: '用地面积',
      placeholder: '请输入用地面积',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsCoveredArea',
      label: '建筑面积',
      placeholder: '请输入建筑面积',
      type: 'input',
      default: '',
      required: false,
    },
    {
      prop: 'cdsUsableArea',
      label: '使用面积',
      placeholder: '请输入使用面积',
      type: 'input',
      default: '',
      required: false,
    },
  ];
}
export function baseDeptInfo() {
  return [
    {
      prop: 'deptName',
      label: '部门名称',
      placeholder: '请输入部门名称',
      type: 'input',
      default: '',
      required: true,
    },
  ];
}
export default {
  dicts: ['school_type', 'school_setup'],
  components: {
    Tree,
    Title,
    MyStaff,
    MyDialog,
    Forms,
  },
  data() {
    return {
      loading: false,
      //   新增弹窗
      showNewOrgan: false,
      showAddOragns: false,
      showAddUnit: false,
      showAddSchool: false,
      showAddmember: false,
      showAddBranchSchool: false,
      showAddCourse: false,
      teachingDialog: false,
      showAddTeacherDialog: false,
      treeNodeKey: 'id', //树组件的节点id key
      user: this.$store.getters.role.manager_id,
      parameter: {
        pageSize: 9,
        pageNum: 1,
        fullName: '',
        schoolType: '',
      },
      contents: '', //学校数据
      // 新增部门的表单存储
      newUnitForms: {
        parentId: '',
        // stageId:''
      },
      // 显示
      showStaff: false,
      // 提交的信息
      campus: {
        agencyType: '',
      },
      schoolInfoForms: [],
      //   弹窗数据
      newOrganForm: [],
      baseDeptsInfo: [],
      treeData: [],
      staffList: [], //员工列表
      personalInfo: [
        {
          prop: 'headIcon',
          label: '头像',
          placeholder: '',
          type: 'photo',
          default: '',
          required: false,
        },
        {
          prop: 'userName',
          label: '姓名',
          placeholder: '请输入姓名',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'sex',
          label: '性别',
          placeholder: '请选择性别',
          type: 'dict',
          dictType: 'sys_user_sex',
          multiple: false,
          default: '',
          options: [],
          required: true,
        },
        {
          prop: 'idcardType',
          label: '证件类型',
          placeholder: '请输入类型',
          type: 'dict',
          default: '',
          dictType: 'idcard_type',
          multiple: false,
          options: [],
          required: true,
        },
        {
          prop: 'idcard',
          label: '证件号',
          placeholder: '请输入证件号',
          type: 'input',
          default: '',
          required: '',
        },
        {
          prop: 'date',
          label: '出生日期',
          placeholder: '请输入出生日期',
          type: 'input',
          default: '',
          required: '',
        },
        {
          prop: 'phone',
          label: '手机号',
          placeholder: '请输入手机号',
          type: 'input',
          default: '',
          required: '',
        },
        // {
        //   prop: 'QQnumber',
        //   label: 'QQ号',
        //   placeholder: '请输入QQ号',
        //   type: 'input',
        //   default: '1956569955',
        //   required: '',
        // },
        {
          prop: 'wechat',
          label: '微信号',
          placeholder: '请输入微信号',
          type: 'input',
          default: '',
          required: '',
        },
        // {
        //   prop: 'email',
        //   label: '邮箱号码',
        //   placeholder: '请输入邮箱号码',
        //   type: 'input',
        //   default: '',
        //   required: '',
        // },
        // {
        //   prop: 'deptName',
        //   label: '所属部门',
        //   placeholder: '请输入所属部门',
        //   type: 'input',
        //   default: '',
        //   required: '',
        // },
        {
          prop: 'email',
          label: '职务',
          placeholder: '请输入职务',
          type: 'input',
          default: '',
          required: '',
        },
        {
          prop: 'duties',
          label: '职称',
          placeholder: '请输入职称',
          type: 'input',
          default: '',
          required: '',
        },
      ], // 职员提交信息
      commitStaff: {
        deptId: '',
      },
      staffParameter: {
        pageSize: 10,
        pageNum: 1,
        deptId: '', //当前节点deptId
        userName: '',
      },
      depatAgencyId: '', // 新增部门id
    };
  },

  created() {
    this.getOrganControllList();
    this.baseDeptsInfo = baseDeptInfo();
  },
  methods: {
    // 获取组织架构管理 20460WER5F884BB989367D3452ADC3F2
    async getOrganControllList() {
      let manId = '';
      let manType = 999;
      console.log(this.$store.getters.roles);
      this.$store.getters.roles.forEach((item) => {
        let tManType = item.manager_type;
        if (tManType != null && tManType < manType) {
          manType = tManType;
          manId = item.manager_id;
        }
      });
      console.log(manId);
      if (manId != '') {
        this.loading = true;
        const { data } = await getOrganController(manId);
        this.treeData = [data[0].children[0]];
        console.log([data[0].children[0]], '组织架构');
        this.loading = false;
      }
    },
    // 职员列表切换
    StaffChange(page) {
      this.staffParameter.pageNum = page;
      this.getStaffLists();
    },
    // 新增职员
    showAddMember() {
      this.showAddmember = true;
    },
    // 新增成员实现
    addStaff(data) {
      let res = Object.assign(data, this.commitStaff);
      addStaff(res).then(() => {
        this.$message.success('添加成功!');
        this.getStaffLists();
      });
      this.commitStaff.deptId = ''; //清除部门id
      this.showAddmember = false;
    },
    // 新增部门弹窗
    showAddDepartment(node, data) {
      console.log(data);
      this.showAddOragns = true;
      this.newUnitForms.parentId = '';
      this.newUnitForms.agencyId = data.agencyId;
      this.depatAgencyId = data.agencyId;
      // if (data.stageId) {
      //   this.newUnitForms.stageId = data.stageId;
      // } else {
      //   this.newUnitForms.stageId = '';
      // }
      if (data.id === 'dept') {
        this.newUnitForms.parentId = null;
      } else {
        this.newUnitForms.parentId = data.deptId;
      }
    },
    // 新增部门实现
    AddDepartment(data) {
      let res = Object.assign(this.newUnitForms, data);
      console.log(res, '提交的部门');
      // let formData = {
      //   addDept: true,
      //   addUser: true,
      //   type: 'dept',
      //   label: res.deptName,
      //   parentId: this.nodeParentId,
      //   // id:'' //需要添加成功后返回的id
      // };
      // this.$refs.customtree.treeAddNode(formData);
      addBaseDept(res)
        .then(() => {
          this.$message.success('添加成功!');
        })
        .catch(() => {});
      this.showAddOragns = false;
      this.nodeParentId = '';
      this.depatAgencyId = '';
    },
    // 删除机构
    Delete(node, data) {
      if (node.parent.data.id === 'stage') {
        this.$modal
          .confirm('确定删除吗？')
          .then(() => {
            this.$refs.customtree.treeDeleteNode(data); // 删除节点
            deleteBaseStage(data.id).then(() => {
              this.$modal.msgSuccess('删除成功！');
              // this.getOrganControllList();
            });
          })
          .catch(() => {});
      }
      if (
        node.parent.data.id === 'shcool' ||
        node.parent.data.id === 'kindergarten' ||
        node.parent.data.id === 'agen'
      ) {
        this.$modal
          .confirm('确定删除吗？')
          .then(() => {
            this.$refs.customtree.treeDeleteNode(data); //删除树节点 避免全局刷新
            deleteAgency(data.id).then(() => {
              this.$modal.msgSuccess('删除成功');
              // this.getOrganControllList();
            });
          })
          .catch(() => {});
      }
    },
    // 编辑
    Edit(node, data) {
      // 如果是学校跳转至详情/detail
      console.log(data);
      this.$router.push({
        path: '/zhili/organization/detail',
        query: {
          id: data.id,
        },
      });
      // 如果是职员 跳转至/personaldetial
    },
    // 新增直属单位弹窗
    showAddUnits(node, data) {
      this.campus.agencyType = '1'; //直属单位
      this.nodeParentId = data.id;
      this.showAddUnit = true;
    },
    // 新增直属单位实现
    AddUnit(res) {
      res = Object.assign(res, this.campus);
      console.log(res);
      let formData = {
        label: res.agencyName,
        parentId: this.nodeParentId,
      };
      this.$refs.customtree.treeAddNode(formData);
      addAgency(res)
        .then(() => {
          this.$message.success('添加成功!');
        })
        .catch(() => {});
      this.showAddUnit = false;
      this.nodeParentId = '';
    },
    // 新增学校弹窗
    showAddCampus(node, data) {
      this.campus.agencyType = '2'; //学校
      this.nodeParentId = data.id;
      if (data.id === 'shcool') {
        this.schoolInfoForms = [
          {
            prop: 'schoolType',
            label: '学校类型',
            placeholder: '请选择学校类型',
            type: 'dict',
            dictType: 'school_type',
            default: '',
            options: [],
            required: true,
          },
          {
            prop: 'schoolSetup',
            label: '办学类型',
            placeholder: '请选择办学类型',
            type: 'dict',
            dictType: 'school_setup',
            default: '',
            options: [],
            required: true,
          },
        ].concat(schoolInfo());
      } else if (data.id === 'kindergarten') {
        this.campus.schoolType = '01'; //幼儿园
        this.schoolInfoForms = [
          {
            prop: 'schoolSetup',
            label: '办学类型',
            placeholder: '请选择办学类型',
            type: 'dict',
            dictType: 'school_setup',
            default: '',
            options: [],
            required: true,
          },
        ].concat(schoolInfo());
      }
      this.showAddSchool = true;
    },
    // 新增分校
    showAddBranchCampus(node, data) {
      console.log(node, data);
      this.campus.agencyType = '2'; //学校
      this.campus.agencyId = data.agencyId;
      this.schoolInfoForms = [
        {
          prop: 'schoolType',
          label: '学校类型',
          placeholder: '请选择学校类型',
          type: 'dict',
          dictType: 'school_type',
          default: '',
          options: [],
          required: true,
        },
        {
          prop: 'schoolSetup',
          label: '办学类型',
          placeholder: '请选择办学类型',
          type: 'dict',
          dictType: 'school_setup',
          default: '',
          options: [],
          required: true,
        },
      ].concat(schoolInfo());
      this.showAddBranchSchool = true;
    },

    // 删除部门单独实现
    deleteOrgan(node, data) {
      this.$modal
        .confirm('确定删除该部门吗？')
        .then(() => {
          this.$refs.customtree.treeDeleteNode(data);
          deleteBaseDept(data.id).then(() => {
            this.$modal.msgSuccess('删除成功！');
            // this.getOrganControllList();
          });
        })
        .catch(() => {});
    },
    // 点击树节点
    async handleNodeClick(data) {
      if (data.deptId) {
        this.showStaff = true;
        this.staffParameter.deptId = data.deptId; // 点击节点后获取部门id deptId
        this.commitStaff.deptId = data.deptId; // 获取部门id 用于新增成员
        await this.getStaffLists(); //获取职员列表
      }
    },
    // 获取职员列表
    async getStaffLists() {
      const res = await getStaffList(this.staffParameter);
      this.staffList = res;
      this.staffParameter.deptId = ''; // 清空部门id
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  //width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  .box {
    border-radius: 8px;
    //padding: 0 20px;
    background-color: #fff;
    //display: flex;
  }
  .content-box {
    margin: 3rem 0;
    height: 50px;
    align-items: center;
    .btn_anniu {
      margin: 0 1rem;
      height: 3.7rem;
      font-size: 1.8rem;
      font-weight: bold;
      border: 0 solid #fff;
      color: #0f4440;
      opacity: 0.5;
      cursor: pointer;
      outline: none;
      background: #fff;
    }
    .newStyle {
      position: relative;
      color: #0f4440;
      font-size: 1.8rem;
      font-weight: bold;
      opacity: 0.9;
    }
    .newStyle:before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 50%;
      height: 1px;
      border-bottom: 0.4rem solid #0f4440;
      box-sizing: border-box;
      opacity: 0.9;
    }
  }
  .content {
    //background-color:#fff;
    margin-bottom: 3rem;
    .head {
      width: 1100px;
      margin: 2rem 0;
      wdith: 50px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .contents {
      display: flex;
      flex-direction: row;
      //justify-content: space-between;
      flex-wrap: wrap;
      .paging {
        margin-top: 30px;
        width: 100%;
        text-align: center;
        margin-bottom: 20px;
      }
    }
  }
}
.schoolDialog {
  .el-select {
    display: flex;
    flex: 1;
  }
}
::v-deep .head .el-select .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;
  font-size: 16px;
  font-weight: 700;

  &::placeholder {
    color: #0f4444;
  }
}

::v-deep .head .el-select .el-icon-arrow-up:before {
  content: '\e78f';
  color: #0f4444;
}

::v-deep .head .el-select .el-input.is-focus .el-input__inner {
  border-color: #0f4444;
}

::v-deep .head .el-input .el-input__inner {
  border-radius: 25px;
  border: 1px solid #0f4444;
}
::v-deep .body .el-tree-node__label {
  font-size: 1.6rem;
  color: #0f4444;
  opacity: 0.8;
  font-weight: 600;
}
::v-deep .head .el-select-dropdown__item.selected {
  color: #0f4444;
}
::v-deep .el-collapse-item__arrow {
  display: none;
}
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
</style>
