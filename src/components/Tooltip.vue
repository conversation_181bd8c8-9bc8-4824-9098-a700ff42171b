<template>
  <div class="tooltip-container" :style="{width:boxWidth+'px'}">
    <el-tooltip :disabled="showTooltip" :content="title">
      <p ref="box" :class="showForward?'text-box-class':'text-box'">
        <img v-if="showForward" style="width: 16px;height:16px;margin-right: 2px"
             src="https://whkj.wuhousmartedu.com/s3/zhihuiyunkongjian/miniprogram/forward_icon_s.svg">
        <span ref="titles">{{ title }}</span>
      </p>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: "myTooltip",
  props: {
    title: {
      type: String,
      default: () => ""
    },
    showForward: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showTooltip: true,
      boxWidth: 0
    }
  },
  watch: {
    title: {
      handler() {
        this.$nextTick(() => this.checkWidth());
      },
      immediate: true
    },

  },
  methods: {
    checkWidth() {
      const boxWidth = this.$refs['box'].offsetWidth;
      this.boxWidth = boxWidth
      const titleWidth = this.$refs['titles'].offsetWidth;
      this.$emit('getTitleWidth', titleWidth);
      this.showTooltip = boxWidth > titleWidth;
      this.$emit('getInfo', {
        boxWidth,
        titleWidth,
        isShow: this.showTooltip
      })
    },
  }
};
</script>
<style scoped lang="scss">
.tooltip-container {
  width: 100%;

  .text-box {
    //display: flex;
    //align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .text-box-class {
    display: flex;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
