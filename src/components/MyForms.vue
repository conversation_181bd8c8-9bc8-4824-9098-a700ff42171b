<template>
  <div class="info-warp">
    <el-form
        :label-width="labelWidth"
        label-suffix=":"
        :model="formDatas"
        :rules="rules"
        ref="forms">
      <div
          class="infos"
          v-if="editable == false">
        <div
            class="photo"
            v-for="item in columns"
            :key="item.prop"
            v-show="item.type == 'photo'">
          <img
              :src="
              item.default
                ? item.default
                : require('@/assets/images/Snipaste_2023-06-15_15-28-05.png')
            "/>
        </div>
        <div class="items">
          <el-form-item
              :label="item.label"
              v-for="item in columns"
              :key="item.prop"
              v-show="item.type != 'photo'">
            <span v-if="item.type == 'dict'">
              <dict-tag
                  :options="dict.type[item.dictType]"
                  :value="(item.default || '').split(';')"
                  myTag="phrases"/>
            </span>
            <span v-else-if="item.type == 'date'">
              {{ item.default }}
            </span>
            <span v-else>
              {{ item.default }}
            </span>
          </el-form-item>
        </div>
      </div>
      <div
          class="infos"
          v-else>
        <div>
          <el-form-item
              :label="item.label"
              v-for="item in columns"
              :key="item.prop"
              :prop="item.prop"
              v-if="item.type != 'hidden'">
            <Avatar
                :defaultImg="item.default"
                @success="uploadSuccess"
                :dialogState="dialogState"
                v-if="item.type == 'photo'"></Avatar>
            <!--身份证号-->
            <div
                v-if="item.type == 'idcard'"
                class="flex just_center aligin_center"
                style="position: relative">
              <el-input
                  :placeholder="item.placeholder"
                  v-model="formDatas[item.prop]"></el-input>
              <el-button
                  v-show="dialogState == 'add'"
                  type="primary"
                  size="mini"
                  style="width: 100px; height: 40px"
                  @click="$emit('change', formDatas[item.prop])">
                获取用户信息
              </el-button>
              <div
                  style="position: absolute; top: 40px; left: 0; color: green"
                  v-show="showMessageError">
                请手动填写用户信息
              </div>
            </div>
            <!-- 文本输入-->
            <el-input
                :placeholder="item.placeholder"
                v-if="item.type == 'input'"
                v-model="formDatas[item.prop]"></el-input>
            <!-- 下拉选择-->
            <el-select
                v-if="item.type == 'select'"
                :placeholder="item.placeholder"
                v-model="formDatas[item.prop]"
                :multiple="item.multiple ? item.multiple : false"
                filterable>
              <el-option
                  v-for="option in item.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"></el-option>
            </el-select>
            <!--隐藏表单-->
            <el-input
                type="hidden"
                v-if="item.type == 'hidden'"
                v-model="formDatas[item.prop]"></el-input>
            <!--密码-->
            <el-input
                type="password"
                show-password
                clearable
                :placeholder="item.placeholder"
                v-if="item.type == 'password'"
                v-model="formDatas[item.prop]"></el-input>
            <!-- 单选-->
            <!-- 多选-->
            <!-- tree-->
            <!--学校多级选择-->
            <SchoolOrg
                v-if="item.type == 'school'"
                :stageId="school.stageId"
                :classes="classes"
                @change="selectSchool"></SchoolOrg>
            <!-- 日期-->
            <el-date-picker
                v-if="item.type == 'date'"
                value-format="yyyy-MM-dd"
                :placeholder="item.placeholder"
                v-model="formDatas[item.prop]"></el-date-picker>
            <!-- 字典-->
            <el-select
                v-if="item.type == 'dict'"
                :placeholder="item.placeholder"
                v-model="formDatas[item.prop]"
                :multiple="item.multiple ? item.multiple : false">
              <el-option
                  v-for="dict in dict.type[item.dictType]"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import SchoolOrg from '@/components/SchoolOrg';
import Avatar from '@/components/Upload';
import {mapGetters} from 'vuex';

export default {
  name: 'MyForms',
  dicts: [
    'user_nation',
    'sys_user_sex',
    'kinship_type',
    'grade_period',
    'stage_name',
    'school_type',
    'school_setup',
    'agency_type',
    'idcard_type',
    'politics_status',
  ],
  data: () => {
    return {
      formDatas: {},
      rules: {},
    };
  },
  components: {
    Avatar,
    SchoolOrg,
  },
  props: {
    columns: [],
    //是否新增或修改true新增，false修改
    modify: {
      type: Boolean,
      default: false,
    },
    labelWidth: {
      type: String,
      default: '12rem',
    },
    editable: {
      type: Boolean,
      default: false,
    },
    showMessageError: {
      type: Boolean,
      default: false,
    },
    dialogState: '',
    classes: '',
  },

  computed: {
    ...mapGetters(['school']),
  },
  created() {
    if (this.modify) {
      this.columns.map((item) => {
        //证件类型特殊处理（默认选中身份证）
        if (item.prop == 'idcardType') {
          item.default = '01';
        } else {
          item.default = '';
        }
      });
    }
    //验证数据规则
    if (this.editable) {
      this.columns.map((item) => {
        this.rules[item.prop] = [
          {
            required: item.required ? item.required : false,
            message: item.placeholder,
            trigger: item.type == 'input' ? 'blur' : 'change',
          },
        ];
      });
    }

    //获取默认值
    this.columns.forEach((item) => {
      this.formDatas[item.prop] = item.default;
      //如果有学校选择特殊处理
      if (item.type == 'school') {
        if (this.classes) {
          this.formDatas.stageId = this.classes[0];
          this.formDatas.gradeId = this.classes[1];
          this.formDatas.classId = this.classes[2];
        } else {
          this.formDatas.stageId = this.school.stageId;
          this.formDatas.gradeId = this.school.gradeId;
          this.formDatas.classId = this.school.classId;
        }
      }
    });

    this.formDatas = _.clone(this.formDatas);
  },
  methods: {
    getFormsDatas() {
      this.$refs.forms.validate((valid) => {
        if (valid) {
          this.$emit('formsDatas', this.formDatas);
        }
      });
    },
    uploadSuccess(r) {
      if (this.dialogState == 'add') {
        this.formDatas.headIcon = r.data.ossFilePath;
      } else {
        this.formDatas.headIcon = r.data.ossFileUrl;
      }
    },
    //学校选择
    selectSchool(e) {
      this.formDatas.gradeId = e[0];
      this.formDatas.classId = e[1];
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .avatar-uploader-icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: #f9f9f9;
}

::v-deep .el-form-item__content {
  line-height: 20px;
}

::v-deep .avatar-uploader-icon {
  line-height: 60px;
}

.info-warp {
  .el-input,
  .el-select {
    width: 100%;
  }

  .infos {
    // justify-content: space-around;

    // padding: 2rem;

    img {
      height: 23rem;
      width: 19rem;
      object-fit: cover;
    }
  }
}
</style>
