import {getInfo, getRole, login, loginTri, logout} from '@/api/login';
// import { getToken, setToken, removeToken } from '@/utils/auth';
import {sessionClear, sessionGet, sessionSet} from '@/utils/local';
import {getAvatar} from '@/utils/tools';
import {bindUserInfo} from "@/api/guojia";

const user = {
    state: {
        token: '',
        name: '',
        avatar: '',
        roles: [],
        role: [],
        defRole: [],
        permissions: [],
        userInfo: {},
        menus: [],
        temporary: '',
        router: [],
        always: false,
        school: {
            stageId: '',
            gradeId: '',
            classId: '',
        },
        backInfo: '',
        timeDifference: ''
    },

    mutations: {
        SET_TIME: (state, timeDifference) => {
            state.timeDifference = timeDifference
        },
        SET_TOKEN: (state, token) => {
            state.token = token;
        },
        SET_NAME: (state, name) => {
            state.name = name;
        },
        SET_AVATAR: (state, avatar) => {
            state.avatar = avatar;
        },
        SET_MENUS: (state, menus) => {
            state.menus = menus;
        },
        SET_ROLES: (state, roles) => {
            state.roles = roles;
        },
        SET_ROLE_CURRENT: (state, role) => {
            state.role = role;
        },
        SET_DEF_ROLE: (state, defRole) => {
            state.defRole = defRole;
        },
        SET_PERMISSIONS: (state, permissions) => {
            state.permissions = permissions;
        },
        SET_USERINFO: (state, userInfo) => {
            state.userInfo = userInfo;
        },
        SET_STAGEID: (state, stageId) => {
            state.school.stageId = stageId;
            state.school.gradeId = '';
            state.school.classId = '';
        },
        SET_GRADEID: (state, gradeId) => {
            console.log(gradeId);
            state.school.gradeId = gradeId;
            state.school.classId = '';
        },
        SET_CLASSID: (state, classId) => {
            state.school.classId = classId;
        },
        SET_NULL: (state) => {
            Object.keys(state).map((item) => {
                state[item] = [];
            });
        },
        SET_TEMPORARY: (state, info) => {
            state.temporary = info;
        },
        SET_ROUTER: (state, route) => {
            state.router = route;
        },
        SET_ALWAYS: (state, always) => {
            state.always = always;
        },
        SET_BACK_INFO: (state, backInfo) => {
            state.backInfo = backInfo
        }
    },

    actions: {
        // 登录
        Login({commit}, userInfo, data) {
            const username = userInfo.username.trim();
            const password = userInfo.password;
            const code = userInfo.code;
            const uuid = userInfo.uuid;
            const captchaVerification = userInfo.captchaVerification
            return new Promise((resolve, reject) => {
                login(username, password, code, uuid, captchaVerification)
                    .then((res) => {
                        // sessionSet('sessionToken', res.token)
                        // window.sessionStorage.setItem('sessionToken', JSON.stringify(res.token))
                        commit('SET_TOKEN', res.token);
                        resolve(res);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        loginTri({commit}, token) {
            return new Promise((resolve, reject) => {
                loginTri(token)
                    .then((res) => {
                        commit('SET_TOKEN', res.token);
                        resolve();
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        SetToken({commit}, token) {
            // return new Promise((resolve, reject) => {
            commit('SET_TOKEN', token);
            // resolve()
            // })
        },
        setSessionStorage({commit}, token) {
            sessionSet('sessionToken', token)
        },
        // 获取用户信息
        GetInfo({commit, state}) {
            return new Promise((resolve, reject) => {
                getInfo()
                    .then((res) => {
                        commit('SET_USERINFO', res.user);
                        const user = res.user;
                        const avatar = user.headIcon;
                        commit('SET_NAME', user.userName);
                        if (avatar) {
                            // commit('SET_AVATAR', avatar);
                        }
                        resolve(res);
                    })
                    .catch((error) => {
                        reject(error);
                    });

                var bindStatus = sessionGet('bindStatus');
                var access_token = sessionGet('access_token');
                if (bindStatus != null && bindStatus != '' && access_token != null && access_token != '') {
                    bindUserInfo(bindStatus, access_token).then((res) => {
                        sessionClear('bindStatus');
                        sessionClear('access_token');
                    })
                }
            });
        },
        //设置当前用户角色
        SetRole({commit, state}, role) {
            //当用户是校管理员时判断是分校管理员还是总管理员
            if (role.role_key == 'school_man') {
                if (role.stage_id) {
                    role.is_branch = true;
                } else {
                    role.is_branch = false;
                }
            }
            commit('SET_ROLE_CURRENT', role);
            commit('SET_AVATAR', getAvatar(state.userInfo.sex, role.role_type));
            //设置角色对应用户头像
            // if (_.isEmpty(get('user').user.userInfo.headIcon)) {
            //   commit('SET_AVATAR', getAvatar(state.userInfo.sex, role.role_type));
            // }

            // //获取角色菜单
            // return new Promise((resolve, reject) => {
            //   getMenus(role.role_key)
            //     .then((res) => {
            //       commit('SET_MENUS', res.data);
            //       resolve(res);
            //     })
            //     .catch((error) => {
            //       reject(error);
            //     });
            // });
        },
        //获取用角色
        GetRole({commit, state}) {
            return new Promise((resolve, reject) => {
                getRole()
                    .then((res) => {
                        commit('SET_ROLES', res.data);
                        //设置用户默认角色
                        // console.log("res.data::::::"+res.data);
                        if (res.data && res.data.length > 0) {
                            let defRole = res.data.filter((item) => item.def == true);
                            if (defRole.length > 0) {
                                commit('SET_DEF_ROLE', defRole[0]);
                                commit('SET_ROLE_CURRENT', defRole[0]);
                            } else {
                                commit('SET_DEF_ROLE', res.data[0]);
                                commit('SET_ROLE_CURRENT', res.data[0]);
                            }
                        } else {
                            // this.$modal.msgWarning('当前用户无角色，请联系管理员');
                        }
                        resolve(res);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        // personal触发getRole
        setBackInx({commit, state}, res) {
            commit('SET_BACK_INFO', res)
        },
        clearBackInx({commit, state}) {
            commit('SET_BACK_INFO', '')
        },
        //设置默认角色
        UpdateRole({commit}, res) {
            commit('SET_DEF_ROLE', res);
        },
        //更新用户信息
        UpdateInfo({commit}, userInfo) {
            commit('SET_USERINFO', userInfo);
            commit('SET_NAME', userInfo.userName);
            if (userInfo.headIcon) {
                commit('SET_AVATAR', userInfo.headIcon);
            }
        },
        //保存路由信息
        SetRouter({commit}, route) {
            commit('SET_ROUTER', route);
        },
        //保存路由信息
        setAlways({commit}, always) {
            commit('SET_ALWAYS', always);
        },
        //保存当前操作的一些数据
        SetStageId({commit}, id) {
            commit('SET_STAGEID', id);
        },
        SetGradeId({commit}, id) {
            commit('SET_GRADEID', id);
        },
        SetClassId({commit}, id) {
            commit('SET_CLASSID', id);
        },
        SetTemporary({commit}, info) {
            commit('SET_TEMPORARY', info);
        },
        // 退出系统
        LogOut({commit, state}) {
            return new Promise((resolve, reject) => {
                logout(state.token)
                    .then((res) => {
                        commit('SET_NULL', []);
                        sessionClear('sessionToken')
                        resolve(res)
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
    },
};

export default user;
