import Vue from 'vue';
import Vuex from 'vuex';
import createPersistedstate from 'vuex-persistedstate';
import getters from './getters';
import app from './modules/app';
import user from './modules/user';
import dict from './modules/dict';
import sessionStoragePlugin from "@/pages/utils/cryptStorage";

Vue.use(Vuex);
export default new Vuex.Store({
    modules: {
        app,
        user,
        dict,
    },
    getters,
    plugins: [sessionStoragePlugin]
    // plugins: [
    //     createPersistedstate({
    //         key: 'user',
    //         // storage: window.sessionStorage,
    //         storage: {
    //             // getItem: (key) => {
    //             //     SecureLsStorage.get(key)
    //             //     console.log(SecureLsStorage.get(key))
    //             // },
    //             // setItem: (key, value) => SecureLsStorage.set(key, value),
    //             // removeItem: (key) => SecureLsStorage.remove(key),
    //             getItem: key => {
    //                 console.log(key)
    //                 console.log(secureSessionStorage.getItem(key))
    //                 secureSessionStorage.getItem(key)
    //                 // sessionStorage.getItem(SM4.decrypt(key))
    //             },
    //             setItem: (key, value) => {
    //                 // sessionStorage.setItem(SM4.encrypt(key), SM4.encrypt(value))
    //                 secureSessionStorage.encrypt(key, value)
    //             },
    //             removeItem: key => {
    //                 secureSessionStorage.removeItem(key)
    //                 // sessionStorage.removeItem(SM4.encrypt(key))
    //             }
    //         }
    //         // paths: ['loginModule'] // 要把那些模块加入缓存
    //     }),
    // ],
});
