const getters = {
    sidebar: state => state.app.sidebar,
    size: state => state.app.size,
    device: state => state.app.device,
    title: state => state.app.title,
    sysType: state => state.app.sysType,
    token: (state) => state.user.token,
    avatar: (state) => state.user.avatar,
    name: (state) => state.user.name,
    introduction: (state) => state.user.introduction,
    roles: (state) => state.user.roles,
    role: (state) => state.user.role,
    defRole: (state) => state.user.defRole,
    dict: (state) => state.dict.dict,
    menus: (state) => state.user.menus,
    permissions: (state) => state.user.permissions,
    userInfo: (state) => state.user.userInfo,
    permission_routes: (state) => state.permission.routes,
    school: (state) => state.user.school,
    temporary: (state) => state.user.temporary,
    router: (state) => state.user.router,
    always: (state) => state.user.always,
    backInfo: (state) => state.user.backInfo,
    timeDifference: (state) => state.user.timeDifference
};
export default getters;
