import request from '@/utils/request';

// 登录方法
export function login(username, password, code, uuid, captchaVerification) {
    const data = {
        username,
        password,
        code,
        uuid,
        captchaVerification
    };
    return request({
        url: '/login',
        headers: {
            isToken: false,
        },
        method: 'post',
        data: data,
    });
}

// 三方token登录方法
export function loginTri(token) {
    const data = {
        token
    }
    return request({
        url: '/loginTri',
        headers: {
            isToken: false
        },
        method: 'post',
        data: data
    })
}

// 退出方法
export function logout() {
    return request({
        url: '/logout',
        method: 'post',
    });
}

// 注册方法
export function register(data) {
    return request({
        url: '/register',
        headers: {
            isToken: false,
        },
        method: 'post',
        data: data,
    });
}

// 获取登录用户信息
export function getInfo() {
    return request({
        url: '/getInfo',
        method: 'get',
    });
}

//获取用户菜单信息
export function getMenus(roleKey) {
    return request({
        url: '/pur/getMenus?roleKey=' + roleKey,
        method: 'get',
    });
}

//获取用户身份信息
export function getRole() {
    return request({
        url: '/pur/getRole',
        method: 'get',
    });
}

//获取登录二维码
export function getWxqr() {
    return request({
        url: '/getWxqr',
        method: 'get',
    });
}

//查询用户登录信息
export function checkLogin(data) {
    return request({
        url: '/checkLogin',
        method: 'post',
        params: data,
    });
}

//绑定微信用户
export function bindUser(data) {
    return request({
        url: '/bindUser',
        method: 'post',
        params: data,
    });
}

//检查登录过期
export function profile() {
    return request({
        url: '/system/user/profile',
        method: 'get',
    });
}

//密码重置
export function resetPwd(userIds, type) {
    return request({
        url: '/portal/user/resetPwd/' + userIds + '?type=' + type,
        method: 'put',
    });
}

//密码重置
export function resetPwd2(params) {
    return request({
        url: '/reset-pwd',
        method: 'post',
        data: params
    });
}

// 获取验证码
export function getCodeImg() {
    return request({
        url: '/captchaImage',
        headers: {
            isToken: false
        },
        method: 'get',
        timeout: 20000
    })
}

// 是否开启验证码
export function isEnableCaptcha() {
    return request({
        url: '/captcha/isEnableCaptcha',
        headers: {
            isToken: false
        },
        method: 'get',
        timeout: 20000
    })
}

// 获取验证码
export function getCode(data) {
    return request({
        url: '/captcha/get',
        headers: {
            isToken: false
        },
        data: data,
        method: 'post',
    })
}

// 滑动或者点选验证
export function reqCheck(data) {
    return request({
        url: '/captcha/check',
        method: 'post',
        data
    })
}
