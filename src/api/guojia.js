import request from '@/utils/request';

//国家智慧教育平台对接

//绑定验证
export function getListByClass() {
  return request({
    url: '/guojia/validBindStatus',
    method: 'get',
  });
}

//获取跳转登录页面参数
export function toLogin() {
  return request({
    url: '/guojia/toLogin',
    method: 'get'
  });
}

export function setLoginToken(ecs){
  return request({
    url: '/guojia/setLoginToken',
    method: 'get',
    params: {ecs:ecs}
  });
}

export function getLoginTicket(){
  return request({
    url: '/guojia/getLoginTicket',
    method: 'get'
  });
}

export function oauthToken(code){
  return request({
    url: '/guojia/oauthToken',
    method: 'get',
    params: {code:code}
  });
}

export function bindUserInfo(smartEduCard,access_token){
  return request({
    url: '/guojia/bindUserInfo',
    method: 'get',
    params: {smartEduCard:smartEduCard,accessToken:access_token}
  });
}

