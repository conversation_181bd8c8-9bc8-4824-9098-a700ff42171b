import Request from '@/utils/request'
import request from "@/utils/request";

export function getCode(params) {
  return Request({
    url: '/bindCode/getCode',
    method: 'post',
    data: {
      ...params
    }
  })
}

export function getStage(id) {
  return request({
    url: '/bindCode/getStage/' + id,
    method: 'get',
  });
}

export function getStageList(id) {
  return request({
    url: '/bindCode/getStageList/' + id,
    method: 'get',
  });
}

//获取年级
export function getGradeList(data) {
  return request({
    url: '/bindCode/gradeList',
    method: 'get',
    params: data,
  });
}

//获取班级
export function getClassList(data) {
  return request({
    url: '/bindCode/classList',
    method: 'get',
    params: data,
  });
}

//获取班级
export function getStuList(data) {
  return request({
    url: '/bindCode/stuList',
    method: 'get',
    params: data,
  });
}

//绑定家长
export function bindElder(params) {
  return Request({
    url: '/bindCode/bindElder',
    method: 'post',
    data: {
      ...params
    }
  })
}
