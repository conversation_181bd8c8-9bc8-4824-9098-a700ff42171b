import request from '@/utils/request';

// 获取空间应用
export function getAppByspace(spaceCode) {
    return request({
        url: '/baseAppInfo/getAppByspace?spaceCode=' + spaceCode,
        method: 'get',
    });
}

//获取新闻资讯
export function getNewsList(data) {
    return request({
        url: '/portal/news/list',
        method: 'get',
        params: data,
    });
}

//新闻详情
export function getNewsInfo(id) {
    return request({
        url: '/portal/news/' + id,
        method: 'get',
    });
}

// 获取空间应用
export function getAppSpace(params) {
    return request({
        url: '/pur/getApps',
        method: 'get',
        params: params,
    });
}

// 获取空间应用
export function getAppSpaceToken(params) {
    return request({
        url: '/pur/getAppToken',
        method: 'get',
        params: params,
    });
}

// 获取活动列表
export function getThemes() {
    return request({
        url: '/pur/getThemes',
        method: 'get'
    });
}

//获取常用应用
export function getOftenApp(params) {
    return request({
        url: '/pur/getOftenApps',
        method: 'get',
        params: params,
    });
}

// 获取全部应用
export function getUserAllApp(params) {
    return request({
        url: '/pur/getAllApps',
        method: 'get',
        params: params,
    });
}

// 用户设置或隐藏应用
export function setUseApp(params) {
    return request({
        url: '/pur/setApps',
        method: 'get',
        params: params,
    });
}

// 用户当前角色
export function setThisRole(params) {
    return request({
        url: '/pur/setThisRole',
        method: 'get',
        params: params,
    });
}

//首页工具以及资源
export function getToolsAndResource(params) {
    return request({
        url: '/portal/news/listSelect',
        method: 'get',
        params: params,
    });
}

// 设置应用顺序
export function setAppSort(params, data) {
    return request({
        url: '/pur/setAppsSort',
        method: 'post',
        params: params,
        data: data
    });
}

// 获取验证码
export function getSmsCode(data) {
    return request({
        url: '/send-sms-code',
        method: 'post',
        data: data
    });
}

// 找回密码
export function resetPwdBySms(data) {
    return request({
        url: '/reset-pwd-by-sms',
        method: 'post',
        data: data
    });
}

// 切换身份
export function setThisRoleNow(data) {
    return request({
        url: '/pur/setThisRole',
        method: 'get',
        params: data
    });
}
