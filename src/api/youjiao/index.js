import request from '@/utils/request';
// 获取分校
export function baseStageList(data) {
  return request({
    url: '/baseStage/list',
    method: 'get',
    params: data,
  });
}
//新增年级
export function addBaseGrade(data) {
  return request({
    url: '/baseGrade',
    method: 'post',
    params: data,
  });
}
//修改年级
export function editBaseGrade(data) {
  return request({
    url: '/baseGrade',
    method: 'put',
    params: data,
  });
}
//删除年级
export function delBaseGrade(id) {
  return request({
    url: '/baseGrade/' + id,
    method: 'delete',
  });
}
//获取年级
export function getBaseGradeList(data) {
  return request({
    url: '/baseGrade/list',
    method: 'get',
    params: data,
  });
}
//获取班级
export function getBaseClassList(data) {
  return request({
    url: '/baseClass/list',
    method: 'get',
    params: data,
  });
}
//新增班级
export function addBaseClass(data) {
  return request({
    url: '/baseClass',
    params: data,
    method: 'post',
  });
}
//修改班级
export function editBaseClass(data) {
  return request({
    url: '/baseClass',
    params: data,
    method: 'put',
  });
}
//删除班级
export function delBaseClass(id) {
  return request({
    url: '/baseClass/' + id,
    method: 'delete',
  });
}
//查询班级
export function getBaseClass(data) {
  return request({
    url: '/baseClass/list',
    method: 'get',
    params: data,
  });
}

//获取分校详情
export function getBaseStage(id) {
  return request({
    url: '/baseStage/' + id,
    method: 'get',
  });
}
//新增分校
export function addBaseStage(data) {
  return request({
    url: '/baseStage/',
    data: data,
    method: 'post',
  });
}
//删除分校
export function delBaseStage(id) {
  return request({
    url: '/baseStage/' + id,
    method: 'delete',
  });
}
//修改分校
export function updateBaseStage(data) {
  return request({
    url: '/baseStage/',
    data: data,
    method: 'put',
  });
}
//获取学校列表
export function getBaseAgencyList(data) {
  return request({
    url: '/baseAgency/list',
    method: 'get',
    params: data,
  });
}
//获取老师列表
export function getTeacherList(data) {
  return request({
    url: '/portal/teacher/list',
    method: 'get',
    params: data,
  });
}
//新增老师
export function addTeacher(data) {
  return request({
    url: '/portal/teacher',
    data: data,
    method: 'post',
  });
}
//修改老师
export function editTeacher(data) {
  return request({
    url: '/portal/teacher',
    data: data,
    method: 'put',
  });
}
//删除老师
export function delTeacher(id) {
  return request({
    url: '/portal/teacher/' + id,

    method: 'delete',
  });
}
//设置年级组长
export function editGradeMange(data) {
  return request({
    url: '/portal/teacher/editGradeMange',
    params: data,
    method: 'post',
  });
}
//设置班主任
export function editClassMange(data) {
  return request({
    url: '/portal/teacher/editClassMange',
    params: data,
    method: 'post',
  });
}
//设置校区
export function setTeacherSchool(data) {
  return request({
    url: '/portal/admin/setTeacherSchool',
    params: data,
    method: 'post',
  });
}
//查询老师信息
export function teacherInfo(id) {
  return request({
    url: '/portal/teacher/' + id,
    method: 'get',
  });
}
//教师角色查询
export function teacherRoleList(data) {
  return request({
    url: '/portal/teacher/roleList',
    method: 'get',
    params: data,
  });
}

//获取学生列表
export function getStudentList(data) {
  return request({
    url: '/portal/student/list',
    method: 'get',
    params: data,
  });
}

//获取学校树结构
export function getGradeTree(data) {
  return request({
    url: '/baseGrade/listTree',
    method: 'get',
    params: data,
  });
}
//新增学生
export function addStudent(data) {
  return request({
    url: '/portal/student',
    data: data,
    method: 'post',
  });
}
//判断用户是否存在
export function getProfileExist(data) {
  return request({
    url: '/system/user/profile/exist',
    method: 'get',
    params: data,
  });
}
//根据身份证号查询学生详细信息
export function getInfoByidcard(data) {
  return request({
    url: '/portal/student/getInfoByidcard',
    method: 'get',
    params: data,
  });
}
//获取学生详细信息
export function getStudentInfo(id) {
  return request({
    url: '/portal/student/' + id,
    method: 'get',
  });
}
//设置默认角色
export function setRoleDef(data) {
  return request({
    url: '/sysRoleDef',
    method: 'put',
    params: data,
  });
}
//学生毕业
export function graduation(data) {
  return request({
    url: '/portal/student/graduation',
    method: 'post',
    data: data,
  });
}
//查询所有分校
export function getAllBaseStage(data) {
  return request({
    url: '/baseStage/listAll',
    method: 'post',
    data: data,
  });
}
//老师任课设置
export function editCourse(data) {
  return request({
    // 单个年级
    url: '/portal/teacher/editCourses',
    method: 'put',
    data: data,
  });
}
/** 学科列表 */
export function getCourseList(data) {
  return request({
    url: '/system/course/list',
    method: 'get',
    params: data,
  });
}
//学生转校
export function transferOut(studentIds, status) {
  return request({
    url: '/portal/student/TransferOut/' + studentIds + '?status=' + status,
    method: 'post',
  });
}
//学生删除
export function delStudent(studentIds) {
  return request({
    url: '/portal/student/' + studentIds,
    method: 'delete',
  });
}
//修改任课信息
export function editTeacherCourse(data) {
  return request({
    url: '/portal/teacher/course/edit',
    method: 'post',
    data: data,
  });
}
//删除任课信息
export function delTeacherCourse(courseId) {
  return request({
    url: '/portal/teacher/course/' + courseId,
    method: 'delete',
  });
}
//学生导入
export function importData(data) {
  return request({
    url: '/portal/student/importData',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
  });
}
//导入结果查询
export function getLogList(data) {
  return request({
    url: '/portal/task/log/list',
    method: 'get',
    params: data,
  });
}
//导入结果详情
export function getLogTask(id) {
  return request({
    url: '/portal/task/log/' + id,
    method: 'get',
  });
}
//毕业操作记录
export function getgList(id) {
  return request({
    url: '/portal/student/gList',
    method: 'get',
  });
}
//毕业撤销
export function unGraduation(id) {
  return request({
    url: '/portal/student/unGraduation/' + id,
    method: 'post',
  });
}
//修改学生
export function editStudent(data) {
  return request({
    url: '/portal/student/',
    method: 'put',
    data: data,
  });
}
//通过手机号查询教师信息
export function getTeacherByPhone(data) {
  return request({
    url: '/portal/teacher/getTeacherByPhone',
    method: 'get',
    params: data,
  });
}
