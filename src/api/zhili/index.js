import request from '@/utils/request';

// 查询机构列表
export const getBaseAgencyList = (params) => {

    return request({
        url: '/baseAgency/list',
        method: 'get',
        params:{
            ...params
        }
    })
}

// 修改机构
export const updatabaseAgency=(params)=>{
    return request({
        url:'/baseAgency',
        method:'put',
        data:{
            ...params
        }
    })
}

// 新增机构
export const addAgency=(params)=>{
    return request({
        url:'/baseAgency',
        method:'post',
        data:{
            ...params
        }
    })
}

//查询机构
export const getBaseAgency=(id)=>{
    return request({
        url:'/baseAgency/' + id,
        method:'get'
    })
}
// 删除机构
export const deleteAgency=(id)=>{
    return request({
        url:'/baseAgency/'+id,
        method:'delete'
    })
}


/**-------------------------------------------------------------------*/
// 组织结构
export const getOrganController = (managerId)=>{

    return request({
        url:'/organ/getOrgan?managerId='+managerId,
        method:'get',
    })
}
/**-------------------部门管理----------------------------------------*/
// 查询部门列表
export const getBaseDeptList=(agencyId)=>{
    return request({
        url:'/baseDept/list?agencyId='+agencyId,
        method:'get'
    })
}

// 修改部门
export const updateBaseDept=(data)=>{
    return request({
        url:'/baseDept',
        method:'put',
        data:{
            ...data
        }
    })
}

// 新增部门
export const addBaseDept=(params)=>{
    return request({
        url:'/baseDept',
        method:'post',
        data:{
            ...params
        }
    })
}

// 查询部门
export const queryBaseDept=(id)=>{
    return request({
        url:'/baseDept/'+id,
        method:'get'
    })
}

// 删除部门
export const deleteBaseDept=(id)=>{
    return request({
        url:'/baseDept/'+id,
        method:'delete'
    })
}
/**---------------------分校管理------------------------*/

// 新增分校
export const addBaseSatge=(params)=>{
    return request({
        url:'/baseStage',
        method:'post',
        data:{
            ...params
        }
    })
}

// 删除分校
export const deleteBaseStage=(id)=>{
    return request({
        url:'/baseStage/'+id,
        method:'delete'
    })
}

/**-------------------老师任教设置---------------------*/
// 老师列表
export const getTeacherList=(data)=>{
    return request({
        url:'/portal/admin/teachers',
        method: 'get',
        params: data,
    })
}

// 设置老师任教学校
export const setTeacherSchool=(params)=>{
    return request({
        url:'/portal/admin/setTeacherSchool',
        method:'post',
        params:{
            ...params
        }
    })
}

// 删除老师任教学校
export const delTeacherSchool=(teacherId)=>{
    return request({
        url:'/portal/admin/delTeacherSchool/'+teacherId,
        method:'delete',
    })
}

// 老师列表 学校空间
export const getTeacher=(params)=>{
    return request({
        url:'/portal/teacher/list',
        method:'get',
        params:{
            ...params
        }
    })
}

// 新增老师
export const addTeacher=(params)=>{
    return request({
        url:'/portal/teacher',
        method:'post',
        data:{
            ...params
        }
    })
}

// 获取所有的学校列表
export const getSchooList=()=>{
    return request({
        url:'/baseStage/listAll',
        method:'get'
    })
}

/**--------------------------职工------------------------------------------*/
// 获取员工列表
export const getStaffList=(params)=>{
    return request({
        url:'/portal/staff/list',
        method:'get',
        params:{
            ...params
        }
    })
}

// 删除员工
export const deleteStaff=(staffIds)=>{
    return request({
        url:'/portal/staff/'+staffIds,
        method:'delete'
    })
}

// 员工详情
export const getStaffDetail=(staffId)=>{
    return request({
        url:'/portal/staff/'+staffId,
        method:'get'
    })
}

// 新增员工
export const addStaff=(params)=>{
    return request({
        url:'/portal/staff',
        method:'post',
        data:{
            ...params
        }
    })
}

// 修改员工
export const modifyStaff=(params)=>{
    return request({
        url:'/portal/staff',
        method:'put',
        data:{
            ...params
        }
    })
}

/**---------------学科设置-----------------------*/

//获取学科列表
export const getCourseList=(params)=>{
    return request({
        url:'/system/course/list',
        method:'get',
        params:{
            ...params
        }
    })
}

// 新增学科
export const addCourse=(params)=>{
    return request({
        url:'/system/course',
        method:'post',
        data:{
            ...params
        }
    })
}

//删除学科
export const deleteCourse=(courseIds)=>{
    return request({
        url:'/system/course/'+courseIds,
        method:'delete'
    })
}