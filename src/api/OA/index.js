import request from '@/utils/request';

//获取公文列表
export function getOaList(params) {
    return request({
        url: '/oa/content/receiveList',
        method: 'get',
        params: params,
    });
}

//附件上传
export const uploadUlrl = '/oa/file/put-file'; //上传
//新增公文
export function addOa(datas) {
    return request({
        url: '/oa/content',
        method: 'post',
        data: datas,
    });
}

//获取用户部门列表
export function getDept(datas) {
    return request({
        url: '/oa/content/dept',
        method: 'get',
        data: datas,
    });
}

//获取部门树
export function getDeptTree(datas) {
    return request({
        url: '/oa/content/deptTree',
        method: 'get',
        params: datas,
    });
}

//获取机构列表
export function agencyList(datas) {
    return request({
        url: '/oa/content/agencyList',
        method: 'get',
        params: datas,
    });
}

//获取部门员工
export function deptUserList(datas) {
    return request({
        url: '/oa/content/deptUserList',
        method: 'post',
        data: datas,
    });
}

//获取文号单位
export function beforeList(datas) {
    return request({
        url: '/oa/before/list',
        method: 'get',
        data: datas,
    });
}

//获取最新文号
export function getDocNo(datas) {
    return request({
        url: '/oa/content/getDocNo',
        method: 'get',
        params: datas,
    });
}

//获取公文详情
export function getContent(datas, params) {
    return request({
        url: '/oa/content/' + datas,
        method: 'get',
        params: params
    });
}

//修改公文
export function putContent(datas) {
    return request({
        url: '/oa/content',
        method: 'put',
        data: datas,
    });
}

//获取单位接收列表
export function getReceiveUnitList(params) {
    return request({
        url: '/oa/content/getReceiveUnitList',
        method: 'get',
        params: params,
    });
}

//获取接收单位人员信息
export function getReceiveDeptList(params) {
    return request({
        url: '/oa/content/getReceiveDeptList',
        method: 'get',
        params: params,
    });
}

//公文签收
export function signContent(datas) {
    return request({
        url: '/oa/content/sign',
        method: 'post',
        data: datas,
    });
}

//公文驳回
export function approve(datas) {
    return request({
        url: '/oa/content/approve',
        method: 'put',
        data: datas,
    });
}

//公文转发
export function forward(datas) {
    return request({
        url: '/oa/content/forward',
        method: 'post',
        data: datas,
    });
}

//获取教研组
export function groupList(datas) {
    return request({
        url: '/oa/content/groupList',
        method: 'get',
        data: datas,
    });
}

//获取tree
export function getUserTree(params) {
    return request({
        url: '/baseStage/tree',
        method: 'get',
        params: params,
    });
}

// 撤销公文
export function revokeOa(params) {
    return request({
        url: '/oa/content/revoke',
        method: 'post',
        params: params,
    });
}

//OA签收统计
export function OASignCount(params) {
    return request({
        url: '/oa/content/getReceiveAgencyCount',
        method: 'get',
        params: params,
    });
}

//OA提醒
export function pushNoticeSignMsg(params) {
    return request({
        url: '/oa/content/pushNoticeSignMsg',
        method: 'post',
        data: params,
    });
}

//打包下载
export function OADownloadZip(id) {
    return request({
        url: '/oa/content/' + id + '/download-zip',
        method: 'get',
    });
}

//获取打包记录
export function getZipRecord(id) {
    return request({
        url: '/oa/content/' + id + '/zip-record',
        method: 'get',
    });
}

//打包
export function handlePackageZip(id) {
    return request({
        url: '/oa/content/' + id + '/package-zip-all',
        method: 'get',
    });
}

//上传文件后选择删除文件
export function handleDeleteFile(params) {
    return request({
        url: '/minioFile/delete-file' + '?ossFilePaths=' + params,
        method: 'delete',
        // params: {ossFilePaths: params}
    });
}

//常用分组接口
export function getGroupsSchoolTree(params) {
    return request({
        url: '/baseGroup/get-group-school-tree',
        method: 'get',
        params: params
    });
}

// 发文单位
export function getUnitDataSelectOptions(params) {
    return request({
        url: '/oa/content/agencySelectOptions',
        method: 'get',
        params: params
    });
}

//接收人公文下载
export function oaDownload(params) {
    return request({
        url: '/oa/file/download',
        method: 'get',
        params: params,
        responseType: 'blob'
    });
}
