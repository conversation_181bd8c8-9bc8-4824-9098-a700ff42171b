import request from '@/utils/request';

// 用户用户个人信息
export function getProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get',
  });
}
//编辑用户信息
export function setProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data,
  });
}
//密码修改
export function updatePwd(data) {
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    data: data,
  });
}
//新增兴趣爱好
export function addHobby(data) {
  return request({
    url: '/system/user/profile/insertHobby',
    method: 'post',
    params: data,
  });
}
//删除兴趣爱好
export function delHobby(data) {
  return request({
    url: '/system/user/profile/deleteHobby',
    method: 'post',
    params: data,
  });
}
