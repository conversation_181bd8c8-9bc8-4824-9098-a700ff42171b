import request from '@/utils/request';

// 获取教师画像token
export function getToken(data) {
  return request({
    url: '/aouth/sys/portalLogin',
    method: 'post',
    dataType: 'json',
    headers: {
      isToken: false,
    },
    params: data,
  });
}
//工具推文与资源推文
export function getToolsOrResource(data) {
  return request({
    url: '/portal/news/list',
    method: 'get',
    params: data,
  });
}

//banner
export function getBanner(data) {
  return request({
    url: '/sys-banner/list',
    method: 'get',
    params: data,
  });
}
//获取教师画像数据
export function getTeacherEcharts(data) {
  return request({
    url: '/portal/teacher/teacher-portrait',
    method: 'get',
    params: data,
  });
}
