import axios from 'axios';
import { Loading, Message } from 'element-ui';
import errorCode from '@/utils/errorCode';
import { blobValidate, tansParams } from '@/utils/tools';
import { saveAs } from 'file-saver';
import store from '@/store';
import { aesDecrypt2, generateRandomStr, getAesString } from "@/pages/utils/tools";
import { getSessionToken } from "@/utils/local";
import { getFingerprint } from "@/utils/tools";

let downloadLoadingInstance;

//是否debug模式，不做请求加密和响应解密
let isDebug = process.env.VUE_APP_DEBUG
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
// 创建axios实例
const service = axios.create({
    // axios中请求配置有baseURL选项，表示请求URL公共部分
    baseURL: process.env.VUE_APP_TARGET,
    // 超时
    timeout: 100000,
});
const cancelToken = axios.CancelToken;
const source = cancelToken.source()
// 定义唯一提示框
const messagebox = Symbol('messagebox')

class selfMessage {
    success(options, single = true) {
        this[messagebox]('success', options, single)
    }

    warning(options, single = true) {
        this[messagebox]('warning', options, single)
    }

    info(options, single = true) {
        this[messagebox]('info', options, single)
    }

    error(options, single = true) {
        this[messagebox]('error', options, single)
    }

    [messagebox](type, options, single) {
        if (single) {
            // 判断是否已存在Message
            if (document.getElementsByClassName('el-message').length === 0) {
                Message[type](options)
            }
        } else {
            Message[type](options)
        }
    }
}

// request拦截器
service.interceptors.request.use(
    async (config) => {
        // 是否需要设置 token
        const isToken = (config.headers || {}).isToken === false;
        if (config.data && !isDebug) {
            let HeaderData = JSON.parse(JSON.stringify(config.data))
            config.data = getAesString(JSON.stringify(HeaderData), generateRandomStr(16), generateRandomStr(16))
        }
        //设置浏览器指纹
        const visitorId = await getFingerprint();
        config.headers.Uak = visitorId;
        config.headers.version = process.env.VUE_APP_VERSION;
        // 是否需要防止数据重复提交
        //const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
        const isRepeatSubmit = false;
        if (store.getters.token && (typeof store.getters.token == 'string') && !isToken) {
            config.headers['Authorization'] = 'Bearer ' + getSessionToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
        }
        // get请求映射params参数
        if (config.method === 'get' && config.params) {
            // console.log(tansParams(config.params))
            let url = config.url + '?' + tansParams(config.params);
            url = url.slice(0, -1);
            config.params = {};
            config.url = url;
        }
        config.cancelToken = source.token;
        return config;
    },
    (error) => {
        Promise.reject(error);
    }
);

// 响应拦截器
service.interceptors.response.use(
    (res) => {
        let result;
        if (res.data && !res.data.code && (typeof res.data == 'string')) {
            result = JSON.parse(aesDecrypt2(res.data));
        } else {
            result = res.data
        }
        // console.log(aesDecrypt2('C85j8nz4fbsY7rHebT4SGiDwzFnd7Io7TTFPFu+/Beay2RLtIOFjeJeXyGlpzYN6PMz1/cbRXxDRvv7TZFlRcsBfrOnwwhvOVxwq94YQ3w0hVY8tU75dV5AvIRke9FjC5pqE1jNviPhu3vvCAXEFb2Ptnq2vT4F3SnLe2oEXtaPcfLDzIH8BTXaFMiLBFftGwczfP2mb0h2+31SIJrwvXMU98MFSA4qIexpA4nkzD9hKW+UuWGptHj9WrDpDCgEMnZtI4hhe7gJRMYz5VjdarexFE8RXZbAb'))
        // 未设置状态码则默认成功状态
        // const code = res.data.code ? res.data.code : result.code;
        const code = result.code || 200
        // 获取错误信息
        const msg = errorCode[code] || (res.data.code ? res.data.msg : result.msg) || errorCode['default'];
        // 二进制数据则直接返回
        if (res.request.responseType === 'blob' ||
            res.request.responseType === 'arraybuffer') {
            return result;
        }
        if (code === 1 || code === 200) {
            return result;
        } else if (code === 401) {
            // Message({message: '登录状态已过期', type: 'warning'});
            let messageTip = new selfMessage();
            messageTip.warning('登录状态已过期');
            if (store.getters.token && typeof store.getters.token == 'string') {
                store.dispatch('LogOut').then(() => {
                    location.href = '/login';
                })
                sessionStorage.removeItem('sessionToken')
            } else {
                location.href = '/login';
            }
            return result;
        } else if (code === 500) {
            let messageTip = new selfMessage();
            messageTip.error(msg);
            return result
            // return Promise.reject(new Error(msg));
        } else if (code === 601) {
            Message({ message: msg, type: 'warning' });
            return result
            // return Promise.reject('error');
        } else if (code === 1006) {
            Message({ message: msg, type: 'error' });
            return result;
        } else if (code === 1008) {
            Message({ message: msg, type: 'error' });
            return result;
        } else if (code === 1009) {
            Message({ message: msg, type: 'error' });
            return result;
        } else if (code === 1010) {
            Message({ message: msg, type: 'error' });
            return result;
        } else if (code === 1007000006) {
            Message({ message: msg, type: 'error' });
            return result
            // return Promise.reject('error');
        } else if (code !== 200) {
            let messageTip = new selfMessage();
            messageTip.error(msg);
            return result
            // return Promise.reject('error');
        } else {
            return result;
        }
    },
    (error) => {
        let { message } = error;
        if (message == 'Network Error') {
            message = '后端接口连接异常';
        } else if (message.includes('timeout')) {
            message = '系统接口请求超时';
        } else if (message.includes('Request failed with status code')) {
            message = '系统接口' + message.substr(message.length - 3) + '异常';
        }
        Message({ message: message, type: 'error', duration: 5 * 1000 });
        return Promise.reject(error);
    }
);

// 通用下载方法
export function download(url, params, filename, config) {
    downloadLoadingInstance = Loading.service({
        text: '正在下载数据，请稍候',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
    });
    return service
        .post(url, params, {
            transformRequest: [
                (params) => {
                    return tansParams(params);
                },
            ],
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            responseType: 'blob',
            ...config,
        })
        .then(async (data) => {
            const isLogin = await blobValidate(data);
            if (isLogin) {
                const blob = new Blob([data]);
                saveAs(blob, filename);
            } else {
                const resText = await data.text();
                const rspObj = JSON.parse(resText);
                const errMsg =
                    errorCode[rspObj.code] || rspObj.msg || errorCode['default'];
                Message.error(errMsg);
            }
            downloadLoadingInstance.close();
        })
        .catch((r) => {
            console.error(r);
            Message.error('下载文件出现错误，请联系管理员！');
            downloadLoadingInstance.close();
        });
}

export default service;
