/**
 * 通用js方法封装处理
 *
 */
import {Loading} from 'element-ui';
import FingerprintJS from '@fingerprintjs/fingerprintjs';
// 获取浏览器指纹
export async function getFingerprint() {
    const fingerprint = await FingerprintJS.load();
    const visitorId = await fingerprint.get();
    return visitorId.visitorId;
  }
// 日期格式化
export function parseTime(time, pattern) {
    if (arguments.length === 0 || !time) {
        return null;
    }
    const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';
    let date;
    if (typeof time === 'object') {
        date = time;
    } else {
        if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
            time = parseInt(time);
        } else if (typeof time === 'string') {
            time = time
                .replace(new RegExp(/-/gm), '/')
                .replace('T', ' ')
                .replace(new RegExp(/\.[\d]{3}/gm), '');
        }
        if (typeof time === 'number' && time.toString().length === 10) {
            time = time * 1000;
        }
        date = new Date(time);
    }
    const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay(),
    };
    const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key];
        // Note: getDay() returns 0 on Sunday
        if (key === 'a') {
            return ['日', '一', '二', '三', '四', '五', '六'][value];
        }
        if (result.length > 0 && value < 10) {
            value = '0' + value;
        }
        return value || 0;
    });
    return time_str;
}

// 表单重置
export function resetForm(refName) {
    if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
    }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
    let search = params;
    search.params =
        typeof search.params === 'object' &&
        search.params !== null &&
        !Array.isArray(search.params)
            ? search.params
            : {};
    dateRange = Array.isArray(dateRange) ? dateRange : [];
    if (typeof propName === 'undefined') {
        search.params['beginTime'] = dateRange[0];
        search.params['endTime'] = dateRange[1];
    } else {
        search.params['begin' + propName] = dateRange[0];
        search.params['end' + propName] = dateRange[1];
    }
    return search;
}

// 回显数据字典
export function selectDictLabel(datas, value) {
    if (value === undefined) {
        return '';
    }
    var actions = [];
    Object.keys(datas).some((key) => {
        if (datas[key].value == '' + value) {
            actions.push(datas[key].label);
            return true;
        }
    });
    if (actions.length === 0) {
        actions.push(value);
    }
    return actions.join('');
}

// 回显数据字典（字符串、数组）
export function selectDictLabels(datas, value, separator) {
    if (value === undefined || value.length === 0) {
        return '';
    }
    if (Array.isArray(value)) {
        value = value.join(',');
    }
    var actions = [];
    var currentSeparator = undefined === separator ? ',' : separator;
    var temp = value.split(currentSeparator);
    Object.keys(value.split(currentSeparator)).some((val) => {
        var match = false;
        Object.keys(datas).some((key) => {
            if (datas[key].value == '' + temp[val]) {
                actions.push(datas[key].label + currentSeparator);
                match = true;
            }
        });
        if (!match) {
            actions.push(temp[val] + currentSeparator);
        }
    });
    return actions.join('').substring(0, actions.join('').length - 1);
}

// 字符串格式化(%s )
export function sprintf(str) {
    var args = arguments,
        flag = true,
        i = 1;
    str = str.replace(/%s/g, function () {
        var arg = args[i++];
        if (typeof arg === 'undefined') {
            flag = false;
            return '';
        }
        return arg;
    });
    return flag ? str : '';
}

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str) {
    if (!str || str == 'undefined' || str == 'null') {
        return '';
    }
    return str;
}

// 数据合并
export function mergeRecursive(source, target) {
    for (var p in target) {
        try {
            if (target[p].constructor == Object) {
                source[p] = mergeRecursive(source[p], target[p]);
            } else {
                source[p] = target[p];
            }
        } catch (e) {
            source[p] = target[p];
        }
    }
    return source;
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
    let config = {
        id: id || 'id',
        parentId: parentId || 'parentId',
        childrenList: children || 'children',
    };

    var childrenListMap = {};
    var nodeIds = {};
    var tree = [];

    for (let d of data) {
        let parentId = d[config.parentId];
        if (childrenListMap[parentId] == null) {
            childrenListMap[parentId] = [];
        }
        nodeIds[d[config.id]] = d;
        childrenListMap[parentId].push(d);
    }

    for (let d of data) {
        let parentId = d[config.parentId];
        if (nodeIds[parentId] == null) {
            tree.push(d);
        }
    }

    for (let t of tree) {
        adaptToChildrenList(t);
    }

    function adaptToChildrenList(o) {
        if (childrenListMap[o[config.id]] !== null) {
            o[config.childrenList] = childrenListMap[o[config.id]];
        }
        if (o[config.childrenList]) {
            for (let c of o[config.childrenList]) {
                adaptToChildrenList(c);
            }
        }
    }

    return tree;
}

/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params) {
    let result = '';
    for (const propName of Object.keys(params)) {
        const value = params[propName];
        var part = encodeURIComponent(propName) + '=';
        if (value !== null && value !== '' && typeof value !== 'undefined') {
            if (typeof value === 'object') {
                for (const key of Object.keys(value)) {
                    if (
                        value[key] !== null &&
                        value[key] !== '' &&
                        typeof value[key] !== 'undefined'
                    ) {
                        let params = propName + '[' + key + ']';
                        var subPart = encodeURIComponent(params) + '=';
                        result += subPart + encodeURIComponent(value[key]) + '&';
                    }
                }
            } else {
                result += part + encodeURIComponent(value) + '&';
            }
        }
    }
    return result;
}

// 验证是否为blob格式
export async function blobValidate(data) {
    try {
        const text = await data.text();
        JSON.parse(text);
        return false;
    } catch (error) {
        return true;
    }
}

//获取默认头像
export function getAvatar(sex, role_type) {
    let avatar = '';
    //男
    if (sex === 0 || sex === '0') {
        if (role_type === 'wor') {
            avatar = require('@/assets/avatar/maleAdministrators.png');
        } else if (role_type === 'tea') {
            avatar = require('@/assets/avatar/maleTeacher.png');
        } else if (role_type === 'stu') {
            avatar = require('@/assets/avatar/maleStudent.png');
        } else if (role_type === 'par') {
            avatar = require('@/assets/avatar/maleParents.png');
        } else {
            avatar = require('@/assets/images/profile.png');
        }
    } else if (sex === 1 || sex === '1') {
        if (role_type === 'wor') {
            avatar = require('@/assets/avatar/femaleAdministrators.png');
        } else if (role_type === 'tea') {
            avatar = require('@/assets/avatar/femaleTeacher.png');
        } else if (role_type === 'stu') {
            avatar = require('@/assets/avatar/femaleStudent.png');
        } else if (role_type === 'par') {
            avatar = require('@/assets/avatar/femaleParents.png');
        } else {
            avatar = require('@/assets/avatar/femaleAdministrators.png');
        }
    } else {
        avatar = require('@/assets/images/profile.png');
    }
    return avatar;
}

export function isValidPassword(str) {
    var reg =
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[~!@#$%^&*(){}\[\]<>?\\+])[A-Za-z\d~!@#$%^&*()\[\]{}<>?\\+]{8,20}$/;
    return reg.test(str)
}

/**
 * 通过url下载文件并对下载的文件更名
 * @param {*} url
 * @param {*} filename
 */
export const downloadFile = (url, filename) => {
    var loading = Loading.service({
        text: '正在下载文件，请稍等',
        background: 'rgba(0, 0, 0, 0.7)',
    })

    function getBlob(url) {
        return new Promise((resolve, reject) => {
            const XML = new XMLHttpRequest();
            XML.open('GET', url, true);
            XML.responseType = 'blob';
            XML.onload = () => {
                if (XML.status === 200) {
                    resolve(XML.response);
                } else {
                    reject(XML)
                }
            };
            XML.send();
        });
    }

    //下载文件
    function saveAs(blob, filename) {
        const elelink = document.createElement("a");
        elelink.style.display = 'none';
        elelink.download = filename;
        elelink.href = window.URL.createObjectURL(blob);
        document.body.appendChild(elelink);
        elelink.click();
        document.body.removeChild(elelink);
    }

    // 调用以上方法进行下载
    getBlob(url).then(blob => {
        saveAs(blob, filename);
        loading.close()
    }).finally(() => {
        loading.close()
    }).catch((err) => {
    })
}
