import axios from 'axios';
import {Loading, Message} from 'element-ui';
import {saveAs} from 'file-saver';
import errorCode from '@/utils/errorCode';
import {getSessionToken, sessionGet} from "@/utils/local";
import {blobValidate, tansParams} from '@/utils/tools';

const baseURL = process.env.VUE_APP_BASE_API;
let downloadLoadingInstance;
export default {
    file(name, url, api, params) {
        var reqUrl = baseURL + api + '?' + tansParams(params);
        downloadLoadingInstance = Loading.service({
            text: '正在下载数据，请稍候',
            background: 'rgba(0, 0, 0, 0.7)',
        });
        axios({
            method: 'get',
            url: reqUrl,
            responseType: 'blob',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                Authorization:
                    'Bearer ' + getSessionToken(),
            },
        })
            .then(async (res) => {
                const blob = new Blob([res.data], {
                    // 下载的文件格式自己在这边更改type的值就好了
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                });
                this.saveAs(blob, name);
                downloadLoadingInstance.close();
            })
            .catch((r) => {
                console.error(r);
                Message.error('下载文件出现错误，请联系管理员！');
                downloadLoadingInstance.close();
            });
    },
    resource(resource) {
        var url =
            baseURL +
            '/common/download/resource?resource=' +
            encodeURIComponent(resource);
        axios({
            method: 'get',
            url: url,
            responseType: 'blob',
            headers: {Authorization: 'Bearer ' + sessionGet()},
        }).then(async (res) => {
            const isLogin = await blobValidate(res.data);
            if (isLogin) {
                const blob = new Blob([res.data]);
                this.saveAs(blob, decodeURIComponent(res.headers['download-filename']));
            } else {
                this.printErrMsg(res.data);
            }
        });
    },
    zip(url, params, name) {
        var url = baseURL + url;
        downloadLoadingInstance = Loading.service({
            text: '正在下载，请稍候',
            background: 'rgba(0, 0, 0, 0.7)',
        });
        axios({
            method: 'get',
            url: url,
            params: params,
            responseType: 'blob',
            headers: {Authorization: 'Bearer ' + getSessionToken()},
        }).then(async (res) => {
            const isLogin = await blobValidate(res.data);
            if (isLogin) {
                const blob = new Blob([res.data], {type: 'application/zip'});
                this.saveAs(blob, name);
            } else {
                await this.printErrMsg(res.data);
            }
        }).finally(() => {
            downloadLoadingInstance.close();
        });
    },
    saveAs(text, name, opts) {
        saveAs(text, name, opts);
    },
    async printErrMsg(data) {
        const resText = await data.text();
        const rspObj = JSON.parse(resText);
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default'];
        Message.error(errMsg);
    },
    downloadExport(fileUri, name) {
        // var nowTime = new Date().UTF2()
        let url = baseURL + encodeURI(fileUri)
        const config = {
            methods: 'get',
            url: url,
            responseType: 'blob'
        }
        axios(config).then((res) => {
            const link = document.createElement('a')
            const url = window.URL.createObjectURL(new Blob([res.data], {type: 'application/vnd.ms-excel'}))
            link.setAttribute("href", url);
            link.setAttribute('download', name)
            link.click()
        })
    }
};
