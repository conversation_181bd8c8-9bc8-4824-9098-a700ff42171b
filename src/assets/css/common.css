/* 字体引入 */
@font-face {
    font-family: PingFangSC;
    src: url('../../assets/fonts/PingFangHeiTC-W4 简 常规.ttf');
}

@font-face {
    font-family: YouSheBiaoTiHei;
    src: url('../../assets/fonts/YouSheBiaoTiHei-2.ttf');
}

@font-face {
    font-family: OPPOSans-B;
    src: url('../../assets/fonts/OPPOSans-B.ttf');
}

@font-face {
    font-family: OPPOSans-H;
    src: url('../../assets/fonts/OPPOSans-H.ttf');
}

@font-face {
    font-family: OPPOSans-L;
    src: url('../../assets/fonts/OPPOSans-L.ttf');
}

@font-face {
    font-family: OPPOSans-M;
    src: url('../../assets/fonts/OPPOSans-M.ttf');
}

@font-face {
    font-family: OPPOSans-R;
    src: url('../../assets/fonts/OPPOSans-R.ttf');
}

@font-face {
    font-family: BEBAS;
    src: url('../../assets/fonts/BEBAS-1.ttf');
}

@font-face {
    font-family: STHupo;
    src: url('../../assets/fonts/STHupo.ttf');
}

@font-face {
    font-family: BebasNeue;
    src: url('../../assets/fonts/BebasNeue Regular.ttf');
}

html, body {
    height: 100%;
}

.el-message-box__btns {
    text-align: center !important;
}

.el-cascader .el-input .el-input__inner:focus,
.el-cascader .el-input.is-focus .el-input__inner {
    border-color: #4d7473 !important;
}

.el-tabs--border-card
> .el-tabs__header
.el-tabs__item:not(.is-disabled):hover {
    /*color: #0f4444 !important;*/
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
    /*color: #4d7473 !important;*/
}

i.el-input__icon.el-icon-search {
    cursor: pointer;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', 'SimHei', 'Arial', 'SimSun', serif;
}

.family_Pf {
    /*font-family: PingFangSC;*/
}

.edit_btn {
    border-radius: 21px !important;
    display: inline-flex !important;
    border: 1px #3888F7 solid !important;
    /* min-width: 10rem !important; */
    height: 4rem;
    opacity: 0.8;
    /* padding: 9px 35px !important; */
    color: #3888F7 !important;
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    width: auto;
    display: flex;
    cursor: pointer;
    text-align: center;
    justify-content: center;
    align-items: center;
}

.edit_btn_text {
    border: none !important;
    font-size: 1.5rem !important;
    /*color: #0f4444 !important;*/
    background: transparent !important;
    cursor: pointer;
    width: auto !important;
    padding: 0 !important;
}

.edit_btn_text:hover {
    background: none !important;
}

.del_btn_text {
    border: none !important;
    color: #ff6262 !important;
    font-size: 1.5rem !important;
    background: transparent !important;
    cursor: pointer;
    width: auto !important;
    padding: 0 !important;
}

.del_btn_text:hover {
    background: none !important;
}

.kj_btn {
    border: none !important;
    color: #ff6262 !important;
    font-size: 1.5rem !important;
    border-radius: 2px !important;
    padding: 8px 10px;
    font-weight: 700;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.4) !important;
}

.c_11471D {
    color: #11471d !important;
}

.c_fff {
    color: #fff !important;
}

.c_title {
    color: #1B77F5
}

.c_666 {
    color: #666;
}

.c_999 {
    color: #999;
}

.c_333 {
    color: #333;
}

.c_revoke {
    color: #FC5353
}

.bg_fff {
    background: #fff;
}

.br_5 {
    border-radius: 5px;
}

.bg_f0 {
    background: #F0F0F0;
}

.p_tb_10 {
    padding-top: 10px;
    padding-bottom: 10px;
}

.p_tb_20 {
    padding-top: 20px;
    padding-bottom: 20px;
}

.fw_blod {
    font-weight: bold;
}

.m_b_5 {
    margin-bottom: 5px;
}

.m_b_10 {
    margin-bottom: 10px;
}

.m_b_20 {
    margin-bottom: 20px;
}

.m_b_30 {
    margin-bottom: 30px;
}

.m_t_5 {
    margin-top: 5px;
}

.m_t_10 {
    margin-top: 10px;
}

.m_t_20 {
    margin-top: 20px;
}

.m_20 {
    margin: 20px 0;
}

.m_10 {
    margin: 10px 0;
}

.m_r_10 {
    margin-right: 10px;
}

.m_r_20 {
    margin-right: 20px;
}

.m_r_30 {
    margin-right: 30px;
}

.m_t_30 {
    margin-top: 30px;
}

.p_20 {
    padding: 20px;
}

.p_20_10 {
    padding: 10px 20px;
}

.p_10 {
    padding: 10px;
}

.el-tabs {
    /*background: #fff;*/
}

.el-tabs__content {
    /*border-top: 20px #f6fdfc solid !important;*/
    /*padding: 12px 28px !important;*/
}

.el-tabs__nav-scroll {
    padding: 12px 28px !important;
}

.el-tabs__item {
    font-size: 1.8rem !important;
    /*color: #0f4444 !important;*/
    /*opacity: 0.6 !important;*/
}

.is-top .is-active {
    /*color: #0f4444 !important;*/
    /*font-weight: 700 !important;*/
    /*opacity: 1 !important;*/
}

.el-tabs__item:hover {
    /*color: #0f4444 !important;*/
    opacity: 1 !important;
}

.el-tabs__nav-wrap::after {
    background-color: #fff !important;
}

.el-tabs__active-bar {
    /*background-color: #0f4444 !important;*/
    /*height: 4px !important;*/
    /*width: 40px !important;*/
}

.dot {
    display: flex;
    border-radius: 20px;
    height: 8px;
    margin-right: 10px;
    width: 8px;
    background-color: #3f8b89;
}

.opacity_3 {
    opacity: 0.3;
}

.opacity_4 {
    opacity: 0.4;
}

.opacity_5 {
    opacity: 0.5;
}

.opacity_6 {
    opacity: 0.6;
}

.opacity_7 {
    opacity: 0.7;
}

.opacity_8 {
    opacity: 0.8;
}

.fs_10 {
    font-size: 10px;
}

.fs_12 {
    font-size: 1.2rem;
}

.fs_14 {
    font-size: 1.4rem;
}

.fs_15 {
    font-size: 1.5rem;
}

.fs_17 {
    font-size: 17px;
}

.fs_16 {
    font-size: 1.6rem;
}

.fs_18 {
    font-size: 1.8rem;
}

.fs_20 {
    font-size: 2rem;
}

.fs_22 {
    font-size: 2.2rem;
}

.fs_24 {
    font-size: 2.4rem;
}

.fs_34 {
    font-size: 3.4rem;
}

.fs_40 {
    font-size: 4rem;
}

.fw_700 {
    font-weight: 700;
}

.fw_500 {
    font-weight: 500;
}

.fs_bold {
    font-weight: bold
}

.m_l_10 {
    margin-left: 10px;
}

.m_l_5 {
    margin-left: 8px;
}

.m_l_20 {
    margin-left: 20px;
}

.m_l_40 {
    margin-left: 40px;
}

.w_100 {
    width: 100%;
}

.w_50 {
    width: 50%;
}

.bg_linear {
    background: #1B77F5;
}

.bg_revoke {
    background: #FEEDED;
}

.click_opacity:active {
    opacity: .7;
}

.c_revoke {
    color: #FC5353
}

.br_b_l_6 {
    border-radius: 0 0 6px 6px;
}

.br_l_6 {
    border-radius: 0 0 0 6px;
}

.br_r_6 {
    border-radius: 0 0 6px 0;
}

.flex {
    display: flex;
}

.flex_c {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flex_alAndJsCenter {
    display: flex;
    justify-content: center;
    align-items: center;
}

.flex_center {
    display: flex;
    align-items: center;
}

.flex_end {
    justify-content: flex-end;
}

.flex_space {
    display: flex;
    justify-content: space-between;
}

.flex_column {
    flex-direction: column;
}

.flex_column_space {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
}

.just_around {
    justify-content: space-around;
}

.just_between {
    justify-content: space-between;
}

.just_center {
    justify-content: center;
}

.aligin_center {
    align-items: center;
}

.ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.flex_grid {
    display: grid;
}

.flex_none {
    display: none;
}

.pointer {
    cursor: pointer;
}

.default {
    cursor: default;

}

.c_tip {
    color: rgb(175, 182, 193)
}

.username {
    width: 75px;
    height: 31px;
    line-height: 31px;
    /*padding-right: 10px;*/
    text-align: justify;
}

.username::after {
    display: inline-block;
    width: 100%;
    content: "";
}

.username2 {
    width: 60px;
    height: 31px;
    line-height: 31px;
    /*padding-right: 10px;*/
    text-align: justify;
}

.username2::after {
    display: inline-block;
    width: 100%;
    content: "";
}

.w_80 {
    width: 80px;
}

.teaBackClass {
    background-color: #3888F7
}

.stuBackClass {
    background-color: #FA8D2E
}

.famBackClass {
    background-color: #F35C5B
}

.schBackClass {
    background-color: #10B27B
}

.manBackClass {
    background-color: #4E72EF
}

.teaClass {
    background-color: #3888F7
}

.stuClass {
    background-color: #FA8D2E
}

.famClass {
    background-color: #F35C5B
}

.schClass {
    background-color: #10B27B
}

.manClass {
    background-color: #4E72EF
}

.tea_color_cancel {
    background: #fff !important;
    border-color: #3888F7 !important;
    color: #3888F7 !important;
    /*font-family: PingFangSC;*/
}

.tea_color_submit {
    background: #3888F7 !important;
    border-color: #3888F7 !important;
    color: #fff !important;
    /*font-family: PingFangSC;*/
}

.stu_color_cancel {
    background: #fff !important;
    border-color: #FA8D2E !important;
    color: #FA8D2E !important;
    /*font-family: PingFangSC;*/
}

.stu_color_submit {
    background: #FA8D2E !important;
    border-color: #FA8D2E !important;
    color: #fff !important;
    /*font-family: PingFangSC;*/
}

.fam_color_cancel {
    background: #fff !important;
    border-color: #F35C5B !important;
    color: #F35C5B !important;
    /*font-family: PingFangSC;*/
}

.fam_color_submit {
    background: #F35C5B !important;
    border-color: #F35C5B !important;
    color: #fff !important;
    /*font-family: PingFangSC;*/
}

.man_color_cancel {
    background: #fff !important;
    border-color: #4E72EF !important;
    color: #4E72EF !important;
    /*font-family: PingFangSC;*/
}

.man_color_submit {
    background: #4E72EF !important;
    border-color: #4E72EF !important;
    color: #fff !important;
    /*font-family: PingFangSC;*/
}

.sch_color_cancel {
    background: #fff !important;
    border-color: #10B27B !important;
    color: #10B27B !important;
    /*font-family: PingFangSC;*/
}

.sch_color_submit {
    background: #10B27B !important;
    border-color: #10B27B !important;
    color: #fff !important;
    /*font-family: PingFangSC;*/
}

.cancel {
    background: #fff !important;
    border-color: #3888F7 !important;
    color: #3888F7 !important;
    /*font-family: PingFangSC;*/
}

.cancel:hover {
    /*background: #fff !important;*/
}

.cancel:active {
    opacity: .7;
}

.submit {
    background: #3888F7 !important;
    border-color: #3888F7 !important;
    color: #fff !important;
    /*font-family: PingFangSC;*/
}

.submit:hover {
    background: #3888F7 !important;
}

.submit:active {
    opacity: .7;
}

/*oa标签*/
.bg_b {
    background: #2A80F6;
}

.p_l_20 {
    padding-left: 20px;
}

.p_l_10 {
    padding-left: 10px;
}

.tag-icon-agree {
    height: 12px;
    width: 12px;
    border-radius: 1px;
    line-height: 12px
}

.bg_999 {
    background: #999;
}

.tag-icon-un-agree {
    height: 10px;
    width: 10px;
    border-radius: 1px;
    border: 1px solid #CCCCCC;
}


.tag-icon {
    width: 10px;
    height: 10px;
    border-radius: 100px
}

.is-tag-icon {
    width: 12px;
    height: 12px;
    border-radius: 2px
}

.role_tag {
    width: 120px;
    height: auto;
    display: -webkit-box;
    overflow: hidden; /*超出隐藏*/
    text-overflow: ellipsis; /*隐藏后添加省略号*/
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.text-overflow_hidden {
    display: -webkit-box;
    overflow: hidden; /*超出隐藏*/
    text-overflow: ellipsis; /*隐藏后添加省略号*/
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.box_shadow {
    box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.change_page:hover {
    color: #1B77F5
}

.cursor-move {
    cursor: move;
}

.txt-center {
    text-align: center;
}

.sub-page-container {
    width: 1200px;
    margin: auto;
    /*padding-top: 75px*/
    padding-bottom: 50px;
}

.el-message-box__close {
    display: none !important;
}

.txt-select {
    user-select: none;
}

.primary-btn:active {
    opacity: .7;
}

.user-un-select {
    user-select: none;
}
