import Vue from 'vue';
import App from './App.vue';
import router from './router'; // before
import store from './store';
import dayjs from 'dayjs';
import {getConfigKey} from "@/api/system/config";
import {getDicts} from '@/api/system/dict/data';
// 字典标签组件
import DictTag from '@/components/DictTag';
// 字典数据组件
import DictData from '@/components/DictData';
import xiaobu from "vue2-water-marker"
/*element-ui*/
import ElementUI, {Message} from 'element-ui';
import _ from 'lodash';
// import {download} from "@/utils/request";
import {Base64} from 'js-base64'
// 引入水印文件地址
import '@/assets/css/common.css';
import './permission'
import plugins from './plugins'; // plugins

Vue.use(xiaobu)

import('element-ui/lib/theme-chalk/index.css');

Vue.directive('title', {//单个修改标题
    inserted: function (el, binding) {
        document.title = el.dataset.title
    }
})

Vue.use(Base64)
Vue.config.productionTip = false;
//全局方法挂载
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.getDicts = getDicts;
// Vue.prototype.download = download
Vue.prototype._ = _;
Vue.prototype.dayjs = dayjs; //可以全局使用dayjs
// 全局组件挂载
const showMessage = Symbol('showMessage')

// 提示信息自定义
class selfMessage {
    success(options, single = true) {
        this[showMessage]('success', options, single)
    }

    warning(options, single = true) {
        this[showMessage]('warning', options, single)
    }

    info(options, single = true) {
        this[showMessage]('info', options, single)
    }

    error(options, single = true) {
        this[showMessage]('error', options, single)
    }

    [showMessage](type, options, single) {
        if (single) {
            // 判断是否已存在Message
            if (document.getElementsByClassName('el-message').length === 0) {
                Message[type](options)
            }
        } else {
            Message[type](options)
        }
    }
}

Vue.prototype.$selfMessage = new selfMessage()
Vue.component('DictTag', DictTag);
DictData.install();
Vue.use(ElementUI);
Vue.use(plugins);
new Vue({
    router,
    store,
    render: (h) => h(App),
}).$mount('#app');
