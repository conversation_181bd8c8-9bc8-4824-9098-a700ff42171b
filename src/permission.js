import {Loading} from 'element-ui';
import router from './router/index'
import store from './store/index'
// const roleType=store.getters.role.role_type
import {sessionSet} from "@/utils/local";
import {getLoginTicket, oauthToken, setLoginToken} from "@/api/guojia";

let downloadLoadingInstance;

const whiteList = ['/login', '/']
const clearList = ['/oa', '/oa/audit']
router.beforeEach((to, from, next) => {

    //国家平台返回参数，接收后需跳转国家平台
    const ecs = to.query.ecs;
    if (ecs != null && ecs != '') {
        // console.log(ecs);
        setLoginToken(ecs).then(param => {
            if (param.code = 200) {
                //获取临时票据
                getLoginTicket().then(ticket => {
                    // console.log(ticket);
                    window.location.href = "https://auth.smartedu.cn/uias/collect/toView?tab=5&clientId=3iR37M3ARO2TkTJnVEekb7ZOya1lfynY&ticket=" + ticket.msg;
                })
            }
        });
    }

    //国家平台登录跳转授权码，获取后通过接口获取访问令牌
    const code = to.query.code;
    // console.log(code);
    if (code != null && code != '') {
        oauthToken(code).then(accessToken => {
            // console.log(accessToken);
            var bindStatus = accessToken.bindStatus;
            if ("1" == bindStatus) {//已绑定
                var token = accessToken.token;
                // console.log(token);
                if (token == '') {
                } else {
                    downloadLoadingInstance = Loading.service({
                        text: '登录中，请稍候',
                        // spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)',
                    });
                    // sessionSet('sessionToken', token);
                    store.dispatch('SetToken', token).then(r => {
                    })
                    store.dispatch('GetInfo').then(() => {
                        //获取用户角色
                        store.dispatch('GetRole').then((res) => {
                            //设置第一个角色作为默认角色
                            if (res.data.length > 0) {
                                store.dispatch('SetRole', res.data[0]);
                                var item = res.data[0];
                                if (item.role_type === 'wor') {
                                    next({path: '/school'})
                                } else if (item.role_type === 'tea') {
                                    next({path: '/teacher'})
                                } else if (item.role_type === 'stu') {
                                    next({path: '/student'})
                                } else if (item.role_type === 'par') {
                                    next({path: '/parents'})
                                } else {
                                    next({path: '/manage'})
                                }
                            } else {
                                next({path: '/manage'})
                            }

                        })
                    }).finally(() => {
                        downloadLoadingInstance.close();
                    })
                }
            } else {//未绑定
                var smartEduCard = accessToken.smartEduCard;
                if (smartEduCard != null && smartEduCard != '') {
                    sessionSet('bindStatus', smartEduCard);
                    sessionSet('access_token', accessToken.access_token);
                }
            }
            // console.log(accessToken);
        })
    }

    // 清除OA首页与详情的tab inx和pageSize本地存储
    if (clearList.indexOf(to.path) == -1) {
        sessionStorage.removeItem('temporaryInxOfOa')
        sessionStorage.removeItem('temporaryPageSize');
        sessionStorage.removeItem('currentShowType')
    }
    if (store.getters.token && typeof store.getters.token == 'string') {
        let roleType = store.getters.role.role_type
        if (to.fullPath === '/login' || to.fullPath === '/') {
            // next({
            //     path: from.fullPath
            // })
            //先全部跳转oa
            next({path: "/oa"})

        } else {
            if (to.meta.roles && to.meta.roles.includes(roleType)) {
                next()
            } else {
                //先全部跳转oa
                next({path: "/oa"})
                // if (roleType == 'tea') {
                //     next({path: '/teacher'})
                // } else if (roleType == 'wor') {
                //     next({path: '/school'})
                // } else if (roleType == 'stu') {
                //     next({path: '/student'})
                // } else if (roleType == 'par') {
                //     next({path: '/parents'})
                // } else {
                //     next({path: '/manage'})
                // }
            }
        }

    } else {
        if (whiteList.indexOf(to.path) !== -1) {
            next()
        } else {
            next('/login')
        }
    }
})
