import Vue from 'vue';
import VueRouter from 'vue-router';
// import Youjiao from '../views/youjiao/personal';
// import Zhili from '../views/zhili/organization';
// import Lexue from '../views/lexue/index';
// import School from '../views/school/personal';
// import Heji<PERSON> from '../views/hejia/index';
Vue.use(VueRouter);
const routes = [
    /* 首页资源*/
    // {
    //     path: '/index',
    //     name: 'index',
    //     component: () => import('@/pages/layout/Layout.vue'),
    //     redirect: '/index',
    //     children: [
    //         {
    //             path: '/index',
    //             name: 'index',
    //             meta: {
    //                 // roles:['stu','man','wor','tea','par',undefined]
    //             },
    //         },
    //     ],
    // },
    {
        path: '/',
        redirect: '/login'
    },
    {
        path: '/login',
        name: 'login',
        // redirect: '/login',
        meta: {
            // roles:['stu','man','wor','tea','par']
        },
        component: () => import('@/pages/Login.vue'),
        // children: [
        //     {
        //         path:'/login',
        //         meta:{
        //             roles:['stu','man','wor','tea','par']
        //         },
        //         component: () => import('@/pages/Login.vue'),
        //     },
        // ],
    },
    {
        path: '/teacher',
        name: 'teacher',
        component: () => import('@/pages/layout/subpageLayout/Layout.vue'),
        redirect: '/teacher',
        children: [
            {
                path: '/teacher',
                name: 'teacher',
                meta: {
                    roles: ['tea']
                },
                component: () => import('@/pages/teacher/index'),
            },
        ],
    },
    {
        path: '/oa',
        name: 'OA',
        redirect: '/oa',
        component: () => import('@/pages/layout/pageLayout/Layout.vue'),
        children: [
            {
                path: '/oa',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par'],
                    keepAlive: true,
                    isBack: false
                },
                component: () => import('@/pages/oa/index.vue')
            },
            {
                path: '/oa/audit',
                name: 'Audit',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par']
                },
                component: () => import("@/pages/oa/oaAudit.vue")
            },
            {
                path: '/oa/detail',
                name: 'Detail',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par']
                },
                component: () => import("@/pages/oa/detail.vue")
            },
        ]
    },
    //   和家
    {
        path: '/parents',
        name: 'parents',
        component: () => import('@/pages/layout/subpageLayout/Layout.vue'),
        redirect: '/parents',
        children: [
            {
                path: '/parents',
                name: 'parents',
                meta: {
                    roles: ['par']
                },
                component: () => import('@/pages/parents/index'),
            },
        ],
    },
    //   管理员
    {
        path: '/manage',
        name: 'manage',
        component: () => import('@/pages/layout/subpageLayout/Layout.vue'),
        redirect: '/manage',
        children: [
            {
                path: '/manage',
                name: 'manage',
                meta: {
                    roles: ['man', undefined]
                },
                component: () => import('@/pages/manage/index'),
            },
        ],
    },
    // {
    //     path: '/more/app',
    //     name: 'More',
    //     component: () => import('@/pages/layout/subpageLayout/Layout.vue'),
    //     redirect: '/more/app',
    //     children: [
    //         {
    //             path: '/more/app',
    //             name: 'More',
    //             meta: {
    //                 roles: ['stu', 'man', 'wor', 'tea', 'par']
    //             },
    //         },
    //     ],
    // },
    {
        path: '/apps',
        name: 'More',
        component: () => import('@/pages/layout/pageLayout/Layout.vue'),
        redirect: '/apps',
        children: [
            {
                path: '/apps',
                name: 'Apps',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par', undefined]
                },
                component: () => import('@/pages/apps/index.vue'),
            },
        ],
    },
    {
        path: '/resources',
        name: 'More',
        component: () => import('@/pages/layout/pageLayout/Layout.vue'),
        redirect: '/resources',
        children: [
            {
                path: '/resources',
                name: 'Resources',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par', undefined]
                },
                component: () => import('@/pages/resource/index.vue'),
            },
        ],
    },
    // {
    //     path: '/news/detail',
    //     name: 'news',
    //     component: () => import('@/pages/layout/subpageLayout/Layout.vue'),
    //     // redirect: '/more/app',
    //     children: [
    //         {
    //             path: '/news/detail',
    //             name: 'news',
    //             meta:{
    //                 roles:['stu','man','wor','tea','par']
    //             },
    //             component: () => import('@/pages/news/index.vue'),
    //         },
    //     ],
    // },
    {
        path: '/file/view',
        name: 'fileView',
        component: () => import('@/pages/layout/Layout.vue'),
        children: [
            {
                path: '/file/view',
                name: 'fileView',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par']
                },
                component: () => import('@/pages/file/index.vue'),
            },
        ]
    },
    {
        path: '/detail',
        name: 'detail',
        component: () => import('@/pages/layout/pageLayout/Layout.vue'),
        children: [
            // {
            //     path: '/news',
            //     name: 'news',
            //     meta:{
            //         roles:['stu','man','wor','tea','par']
            //     },
            //     component: () => import('@/pages/news/newsList.vue'),
            // },
            {
                path: '/detail',
                name: 'detail',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par']
                },
                component: () => import('@/pages/news/index.vue'),
            },
        ],
    },
    //   学生
    //   管理员
    {
        path: '/student',
        name: 'student',
        component: () => import('@/pages/layout/subpageLayout/Layout.vue'),
        redirect: '/student',
        children: [
            {
                path: '/student',
                name: 'student',
                meta: {
                    roles: ['stu']
                },
                component: () => import('@/pages/student/index'),
            },
        ],
    },
    //   学校
    //   管理员
    {
        path: '/school',
        name: 'school',
        component: () => import('@/pages/layout/subpageLayout/Layout.vue'),
        redirect: '/school',
        children: [
            {
                path: '/school',
                name: 'school',
                meta: {
                    roles: ['wor']
                },
                component: () => import('@/pages/school/index'),
            },
        ],
    },
    /* 子页面*/
    {
        path: '/youjiao/index',
        name: 'YouJiaoHome',
        component: () => import('@/views/youjiao/index'),
    },
    {
        path: '/lexue/index',
        name: 'LexueHome',
        component: () => import('@/views/lexue/index'),
    },
    {
        path: '/zhili/index',
        name: 'ZhiliHome',
        component: () => import('@/views/zhili/index'),
    },
    {
        path: '/hejia/index',
        name: 'HejiaHome',
        component: () => import('@/views/hejia/index'),
    },
    {
        path: '/school/index',
        name: 'SchoolHome',
        component: () => import('@/views/school/index'),
    },
    {
        path: '/personal',
        name: 'personal',
        component: () => import('@/pages/layout/subpageLayout/Layout'),
        redirect: '/personal',
        children: [
            {
                path: '/personal',
                name: 'personal',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par', undefined]
                },
                component: () => import('@/pages/personal'),
            },
            {
                path: '/setting',
                name: 'schoolSeeting',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par']
                },
                component: () => import('@/views/setting'),
            },
            {
                path: '/tools',
                name: 'tools',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par']
                },
                component: () => import('@/views/tools'),
            },
        ],
    },
    /* 个人中心(智汇云调用)*/
    {
        path: '/profile',
        name: 'profile',
        component: () => import('@/views/profile/layout/Layout'),
        redirect: '/profile',
        children: [
            {
                path: '/profile',
                meta: {
                    roles: ['stu', 'man', 'wor', 'tea', 'par']
                },
                name: 'profile',
                component: () => import('@/views/profile/personal'),
            },
        ],
    },
    /*家长绑定学生*/
    {
        path: '/bind',
        name: 'bind',
        // component: () => import('@/views/profile/bind'),
        component: () => import('@/views/profile/layout/bind/Layout'),
        redirect: '/profile',
        children: [
            {
                path: '/bind',
                name: 'bind',
                component: () => import('@/views/profile/bind'),
            },
        ],
    },
    /* 个人中心*/
    // {
    //   path: '/personal',
    //   name: 'personal',
    //   component: () => import('@/views/layout/subpageLayout/Layout'),
    //   redirect: '/personal',
    //   children: [
    //     {
    //       path: '/personal',

    //       // components: {
    //       //   youjiao: Youjiao,
    //       //   zhili: Zhili,
    //       //   school: School,
    //       //   hejia: Hejia,
    //       //   lexue: Lexue,
    //       // },
    //     },
    //   ],
    // },
    {
        path: '/index1',
        //component: () => import('@/index.vue'),
    },
    {
        path: '/subpage2',
        component: () => import('@/views/layout/subpageLayout/Layout'),
        redirect: '/zhili/index',
        children: [
            {
                path: '/zhili/index',
                component: () => import('@/views/zhili/index.vue'),
            },
            {
                path: '/zhili/organization',
                component: () => import('@/views/setting.vue'),
            },
            {
                path: '/zhili/organization/detail',
                component: () => import('@/views/zhili/SchoolDetail.vue'),
            },
            {
                path: '/zhili/organs/personaldetail',
                component: () => import('@/views/zhili/personaldetail.vue'),
            },
            {
                path: '/zhili/teacher',
                name: 'TeacherSchool',
                component: () => import('@/components/TeacherList.vue'),
            },
            {
                path: '/youjiao/personal',
                name: 'Youjiao',
                component: () => import('@/views/personal.vue'),
            },
        ],
    },
];
const router = new VueRouter({
    mode: 'history',
    base: process.env.BASE_URL,
    // routes: routes,
    routes: [...routes],
});
/* 重写router方法 */
const routerPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
    return routerPush.call(this, location).catch((error) => error);
};

export default router;
