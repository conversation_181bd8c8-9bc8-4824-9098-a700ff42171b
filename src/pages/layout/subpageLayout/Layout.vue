<template>
  <div style="height:100%">
    <Header></Header>
    <div class="zh-content">
      <div class="warp">
        <router-view></router-view>
      </div>
    </div>
    <Footer></Footer>
  </div>
</template>

<script>
import Header from '@/pages/layout/subpageLayout/Header';
import Footer from '@/pages/layout/Footer';

export default {
  name: 'HomeLayout',
  components: {
    Header,
    Footer,
  },
  data: () => {
    return {
      refresh: 0
    };
  },
  methods: {
    handleRefresh() {
      this.refresh++
    }
  },
};
</script>

<style scoped lang="scss">
.zh-content {
  background-size: 100% 100%;
  background: linear-gradient(180deg, #F7FAFF 0%, #DEECFE 100%);
  padding: 20px 0;
  min-height: calc(100% - 135px);

  .warp {
    width: 1200px;
    margin: 60px auto 20px auto;
    height: 100%
  }
}
</style>
