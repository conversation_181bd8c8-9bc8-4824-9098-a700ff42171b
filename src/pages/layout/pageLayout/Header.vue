<template>
  <div class="zh-header" style="background: #1B77F5;">
    <div v-if="showBack"
         class="crumbs"
         @click="goHome(2)">
      <i class="el-icon-back"></i>
      返回
    </div>
    <div class="logo">
      <!--        <img src="../../../assets/logo.png"/>-->
      {{title}}
    </div>
    <div class="login">
      <div class="account">
        <div
            @click="login"
            v-if="_.isEmpty(userInfo)">
          <!--          请登录您的账号-->
          登录
        </div>
        <div
            v-else
            class="navigation">
          <div
              class="switch"
              v-if="roles.length > 1">
            <el-dropdown trigger="click">
              <div class="el-dropdown-link">
                {{ role.child_name }}{{ role.role_name }}（{{
                  role.full_name
                }}）<img style="width: 14px;height:14px" src="@/pages/images/user_icons.svg">
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                    v-for="(item, index) in roles"
                    :key="index"
                    @click.native="switchHandle(item)">
                  {{ item.child_name }}{{ item.role_name }}（{{
                    item.full_name
                  }}）
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div
              v-else
              class="p_20"
              style="cursor: default">
            {{ role.full_name }}
          </div>
          <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                <img style="border-radius: 100px"
                     :src="avatar"
                     class="avatar"/>
                {{ name }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="$router.push('/personal')">
                个人中心
              </el-dropdown-item>
              <el-dropdown-item @click.native="logout">
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import Tabs from '@/components/Tabs';
import {mapGetters} from 'vuex';
import {setThisRoleNow} from "@/api/home";

export default {
  name: 'HomeHeader',
  components: {
    // Tabs,
  },
  computed: {
    ...mapGetters([
      'role',
      'name',
      'userInfo',
      'avatar',
      'role',
      'roles',
      'defRole',
      'backInfo'
    ]),
    backClass() {
      if (this.role.role_type === 'wor') {
        return 'schBackClass'
      } else if (this.role.role_type === 'tea') {
        return 'teaBackClass'
      } else if (this.role.role_type === 'stu') {
        return 'stuBackClass'
      } else if (this.role.role_type === 'par') {
        return 'famBackClass'
      } else {
        return 'manBackClass'
      }

    },
    showBack() {
      return !(this.$route.path === '/teacher'
          || this.$route.path === '/manage'
          || this.$route.path === '/school'
          || this.$route.path === '/parents'
          || this.$route.path === '/student'
          || this.$route.path === '/home/<USER>'
          || !this.propsShowBack
      );
    }
  },
  props: {
    propsShowBack: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      title: process.env.VUE_APP_TITLE,
      showRole: true,
      tabs: [
        {
          title: '个人信息',
          path: '/platform/admin',
          icon: 'el-icon-tickets',
        },
        {
          title: '年级班级设置',
          path: '/platform/class',
          icon: 'el-icon-tickets',
        },
        {title: '学科设置', path: '/index', icon: 'el-icon-tickets'},
        {title: '老师设置', path: '/index', icon: 'el-icon-tickets'},
        {title: '毕业设置', path: '/index', icon: 'el-icon-tickets'},
        {title: '学生设置', path: '/index', icon: 'el-icon-tickets'},
        {title: '组织架构', path: '/index', icon: 'el-icon-tickets'},
      ],
    };
  },
  created() {
  },
  methods: {
    //用户普通模式登录
    login() {
      // this.showLogin = true;
      // if (this.loginMode) {
      //   this.qrdata = '';
      //   this.getCode();
      // }
      this.$router.push({
        path: '/login'
      })
    },
    //根据角色进入空间
    goHome(type) {
      if (this.backInfo === true) {
        let item = this.role
        if (item.role_type === 'wor') {
          this.$router.push({path: '/school'});
        } else if (item.role_type === 'tea') {
          this.$router.push({path: '/teacher'});
        } else if (item.role_type === 'stu') {
          this.$router.push({path: '/student'});
        } else if (item.role_type === 'par') {
          this.$router.push({path: '/parents'});
        } else {
          this.$router.push({path: '/manage'});
        }
        this.$store.dispatch('clearBackInx', '')
      } else {
        this.$router.go(-1)
      }
      this.$forceUpdate()
    },
    //切换角色
    switchHandle(item) {
      let params = {
        roleKey: item.role_key,
        agencyId: item.agency_id,
        stageId: item.stage_id
      }
      setThisRoleNow(params).then(() => {
        this.$store.dispatch('SetRole', item).then((res) => {
          this.$emit('refresh')
          this.$message.success('切换成功')
        })
      })
    },
    logout() {
      this.$modal
          .confirm('是否退出登录')
          .then(() => {
            this.$store.dispatch('dict/cleanDict')
            this.$store.dispatch('LogOut').then(() => {
              // this.$router.push('/login');
              location.href = '/login'
            });
          })
          .catch(() => {
          });
    },
  },
};
</script>

<style scoped lang="scss">
.zh-header {
  height: 60px;
  display: flex;
  z-index: 1000;
  width: 100%;
  align-items: center;
  //min-width: 1300px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  position: fixed;
  color: #fff;

  .logo {
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 3.8rem;
    letter-spacing: 0.2rem;
    display: flex;
    align-items: center;

    img {
      height: 4rem;
      padding: 0 1rem;
    }
  }

  .crumbs {
    position: absolute;
    left: 38px;
    height: 80px;
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 15px;
    cursor: pointer;
    font-family: "微软雅黑", Arial;
  }

  .crumbs:active {
    opacity: .7;
  }

  .crumbs2 {
    position: absolute;
    left: 130px;
    height: 80px;
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 14px;
    cursor: pointer;
    font-family: "微软雅黑", Arial;
  }

  .login {
    position: absolute;
    right: 0;
    height: 80px;
    margin-right: 40px;
    display: flex;

    align-items: center;

    .account {
      font-weight: 700;
      font-size: 1.5rem;
      font-family: "微软雅黑", Arial, serif;
    }
  }

  justify-content: center;

  .el-dropdown-link {
    color: #fff;
    font-size: 14px;
    line-height: 14px;
    display: flex;
    justify-content: space-between;
    justify-items: center;
    align-items: center;

    img {
      margin: 0 5px;
      //padding: 0 0.8rem;
      width: 3rem;
      height: 3rem;
    }
  }

  .navigation {
    display: flex;
    align-items: center;
    cursor: pointer;

    .switch {
      margin-right: 10px;
      border-radius: 5px;
      border: 1px #fff solid;
      text-align: center;
      padding: 3px 10px;
    }
  }
}
</style>
