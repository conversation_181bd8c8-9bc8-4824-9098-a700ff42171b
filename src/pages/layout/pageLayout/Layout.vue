<template>
  <div class="zh-container">
    <div class="zh-content">
      <router-view></router-view>
    </div>
    <Footer></Footer>
  </div>
</template>

<script>
import Footer from '@/pages/layout/Footer';

export default {
  name: 'HomeLayout',
  components: {
    Footer,
  },
  data: () => {
    return {
      refresh: 0
    };
  },
  methods: {
    handleRefresh() {
      this.refresh++
    }
  },
};
</script>

<style scoped lang="scss">
.zh-container {
  height: calc(100% - 98px)
}

.zh-content {
  background-size: 100% 100%;
  background: linear-gradient(180deg, #F7FAFF 0%, #DEECFE 100%);
  min-height: 100%;

  .warp {
    width: 1200px;
    margin: 60px auto 20px auto;
    height: 100%
  }
}
</style>
