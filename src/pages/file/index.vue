<template>
  <div class="file-container" v-loading="vLoading" v-title data-title="文件预览">
    <vue-office-docx
        class="vue-office--box-container"
        :src="docx"
        style="height: 100vh;"
        @rendered="renderedHandler"
        @error="errorHandler"
    />
  </div>
</template>

<script>
import VueOfficeDocx from '@vue-office/docx'
//引入相关样式
import '@vue-office/docx/lib/index.css'
import * as base64 from "js-base64";
import Vue from "vue";
import {Loading} from "element-ui";

export default {
  components: {
    VueOfficeDocx
  },
  name: "index",
  data() {
    return {
      showPdf: false,
      showExcel: false,
      showDoc: false,
      fileType: '',
      docx: '',
      vLoading: false,
    }
  },
  created() {
    // // const loading = this.$loading({
    // //   lock: true,
    // //   text: '正在打开，若文件过大可能或有延迟，请耐心等待',
    // //   background: 'rgba(0,0,0,0.7)'
    // // })
    // setInterval(() => {
    //   // loading.close()
    // }, 3000)
  },
  mounted() {
    this.fileType = 1;
    this.vLoading = true
    let urlObj = this.parseURL(decodeURIComponent(window.location.href));
    this.docx = decodeURIComponent(base64.decode(urlObj.params.url))
  },
  methods: {
    parseURL(url) {
      var a = document.createElement('a');
      a.href = url;
      return {
        params: (function () {
          var ret = {},
              seg = a.search.replace(/^\?/, '').split('&'),
              len = seg.length,
              i = 0,
              s;
          for (; i < len; i++) {
            if (!seg[i]) {
              continue;
            }
            s = seg[i].split('=');
            ret[s[0]] = s[1];
          }
          return ret;
        })(),
      };
    },
// 调用
    renderedHandler() {
      this.vLoading = false
    },
    errorHandler() {
      this.vLoading = false
    },
  }
}
</script>

<style scoped>

</style>
