<template>
  <el-dialog
      class="reset-dialog"
      :visible.sync="showDialog"
      append-to-body
      :width="'400px'"
      @open="handleOpen"
      @close="dialogClosed"
  >
    <template slot="title">
      <div class="flex_alAndJsCenter">
        <img
            src="@/pages/images/img2/tips.png">&nbsp;
        <div class="fs_bold fs_18">文件</div>
      </div>
    </template>
    <div>
    </div>
    <div style="text-align: center">
      <el-button class="cancel" @click="dialogClosed">取消</el-button>&nbsp;&nbsp;
      <el-button class="pass" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>

export default {
  props: {
    url: {
      type: String, default: ''
    }
  },
  data() {
    return {
      showDialog: false,
      advice: ""
    }
  },
  methods: {
    handleOpen() {
    },
    dialogClosed() {
      this.showDialog = false
    },
    handleSubmit() {
      this.$emit('confirm')
    }
  }
}
</script>
<style lang="scss" scoped>
.closeIcon {
  font-size: 20px;
  cursor: pointer;
  opacity: .8;
  color: rgb(112, 112, 112);
  top: 5px;
}

::v-deep {
  .el-dialog {
    border-radius: 10px !important;
  }
}

::v-deep {
  .el-dialog__body {
    padding: 0 20px 20px 20px !important;
  }
}

.cancel {
  background: #F0F0F0 !important;
  color: #333333 !important;
  border-color: #F0F0F0 !important;
}

.pass {
  background: linear-gradient(90deg, #64A3F8 0%, #1B77F5 100%);
  color: #fff !important;
  border-color: #fff !important;
}
</style>
