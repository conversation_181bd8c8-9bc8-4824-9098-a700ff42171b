import {Loading} from "element-ui";
import CryptoJs from "crypto-js";
import Cookies from "js-cookie";

/**
 * @word 要加密的内容
 * @keyWord String  服务器随机返回的关键字
 */
export function aesEncryptECB(word, keyWord = 'XwKsGlMcdPMEhR1B') {
    const key = CryptoJs.enc.Utf8.parse(keyWord)
    const secs = CryptoJs.enc.Utf8.parse(word)
    const encrypted = CryptoJs.AES.encrypt(secs, key, {mode: CryptoJs.mode.ECB, padding: CryptoJs.pad.Pkcs7})
    return encrypted.toString()
}

/**
 * @word 要解密的内容
 * @keyWord String  服务器随机返回的关键字
 */
export function aesDecryptECB(word, keyWord = 'XwKsGlMcdPMEhR1B') {
    const key = CryptoJs.enc.Utf8.parse(keyWord)
    const decrypt = CryptoJs.AES.decrypt(word, key, {mode: CryptoJs.mode.ECB, padding: CryptoJs.pad.Pkcs7})
    return CryptoJs.enc.Utf8.stringify(decrypt).toString()
}

// AES加密
export function getAesString(data, key, iv) {
    let keys = CryptoJs.enc.Utf8.parse(key)
    let vis = CryptoJs.enc.Utf8.parse(iv)
    let encrypt = CryptoJs.AES.encrypt(data, keys, {
        iv: vis,
        mode: CryptoJs.mode.CBC,
        padding: CryptoJs.pad.ZeroPadding
    });
    return key + encrypt.toString() + iv;
}


//
export function getAesString2(data, key, iv) {
    let keys = CryptoJs.enc.Utf8.parse(key)
    let vis = CryptoJs.enc.Utf8.parse(iv)
    let encrypt = CryptoJs.AES.encrypt(data, keys, {
        iv: vis,
        mode: CryptoJs.mode.CBC,
        padding: CryptoJs.pad.Pkcs7
    });
    return key + encrypt.toString() + iv;
}

// AES解密
export function aesDecrypt(text, keyStr, ivStr) {
    const key = CryptoJs.enc.Utf8.parse(keyStr);
    const iv = CryptoJs.enc.Utf8.parse(ivStr);
    const decrypt = CryptoJs.AES.decrypt(text, key, {
        iv,
        mode: CryptoJs.mode.CBC,
        padding: CryptoJs.pad.ZeroPadding
    });
    return decrypt.toString(CryptoJs.enc.Utf8);
}

// AES解密方法2
export function aesDecrypt2(text) {
    let bodyStr = text.substr(16, text.length - 32)
    const key = CryptoJs.enc.Utf8.parse(text.substr(0, 16));
    const iv = CryptoJs.enc.Utf8.parse(text.substr(text.length - 16));
    const decrypt = CryptoJs.AES.decrypt(bodyStr, key, {
        iv,
        mode: CryptoJs.mode.CBC,
        padding: CryptoJs.pad.ZeroPadding
    });
    return decrypt.toString(CryptoJs.enc.Utf8);
}

// 生成随机字符
export function generateRandomStr(length) {
    const characters =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        result += characters[randomIndex];
    }
    return result.toString();
}

//记住密码AES加密
export function encryptKey(pwd) {
    let Pwd = CryptoJs.enc.Utf8.parse(pwd)
    Cookies.set('wjpdKey', CryptoJs.AES.encrypt(Pwd, process.env.VUE_APP_PD_SECRET), {expires: 30})
}

// 记住密码AES解密
export function decryptKey() {
    let Pwd = Cookies.get('wjpdKey')
    return CryptoJs.AES.decrypt(Pwd, process.env.VUE_APP_PD_SECRET).toString(CryptoJs.enc.Utf8)
}


// Loading动画
export const servicesLoading = (node, str, lock) => {
    return Loading.service({
        target: document.querySelector(node),//loading需要覆盖的DOM节点，默认为body
        text: str,//加载文案
        lock,//同v-loading的修饰符
        backgroundColor: 'rgba(55,55,55,0.4)',//背景色
        spinner: 'el-icon-loading',//加载图标
    })
}
// 判断应用data中是否全部不显示
export const determineShow = (arr) => {
    return !arr.every(item => (item.pcTag === '1' || item.pcTag === 1));
}

export function getArr(data) {
    let obj = []
    for (let i = 0; i < data.length; i++) {
        if (data[i].pcTag === 0 || data[i].pcTag === '0') {
            obj.push(data[i])
        }
    }
    return obj
}
