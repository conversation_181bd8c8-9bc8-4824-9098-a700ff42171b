import CryptoJS from 'crypto-js';

const SESSION_STORAGE_KEY = '_zh_wj_str';
const ENCRYPTION_SECRET = process.env.VUE_APP_ENCRYPTION_SECRET;
const setItem = (key, value) => {
    const encryptedValue = CryptoJS.AES.encrypt(JSON.stringify(value), ENCRYPTION_SECRET).toString();
    sessionStorage.setItem(key, encryptedValue);
};
const getItem = (key) => {
    const encryptedValue = sessionStorage.getItem(key);
    if (!encryptedValue) {
        return null;
    }
    const bytes = CryptoJS.AES.decrypt(encryptedValue, ENCRYPTION_SECRET);
    return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
};
const sessionStoragePlugin = store => {
    // 初始化时从sessionStorage恢复状态
    const savedState = getItem(SESSION_STORAGE_KEY);
    if (savedState) {
        store.replaceState(Object.assign(store.state, savedState));
    }
    // 每次mutation提交后，将状态存储到sessionStorage
    store.subscribe((mutation, state) => {
        setItem(SESSION_STORAGE_KEY, state);
    });
};
export default sessionStoragePlugin;

