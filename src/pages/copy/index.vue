<template>
  <div class="container fs_16 container-box">
    <div v-if="!showMoreApp">
      <div class="top-container">
        <div class="item1 m_r_20">
          <div class="navigation" style="text-align: right;margin-right: 20px">
            <div class="switch">
              <el-dropdown trigger="click" v-if="roles.length>1">
                <span class="change el-dropdown-link">切换身份
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                      v-for="(item, index) in roles"
                      :key="index"
                      @click.native="switchHandle(item)">
                    <!--                  {{ item.role_name }}-->
                    {{ item.child_name }}{{ item.role_name }}（{{
                      item.full_name
                    }}）
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div class="">
            <div class="flex">
              <div>
                <img
                    :src="avatar"
                    class="avatar"/></div>
              <div>
                <div v-if="role.role_name">{{ name }} （ {{ role.role_name }} ）</div>
                <div v-else>{{ name }} （未分配角色）</div>
                <div class="fs_14">{{ role.full_name }}</div>
              </div>
            </div>
            <div class="role_card">
              <!--              {{userInfo.sex==='1'?'女':'男'}}-->
              <div class="flex_center">性别：
                <dict-tag
                    :options="dict.type['sys_user_sex']"
                    :value="userInfo.sex"
                    myTag="phrases"/>
              </div>
              <div class="flex_center">民族：
                <dict-tag
                    :options="dict.type['user_nation']"
                    :value="(userInfo.nation|| '').split(';')"
                    myTag="phrases"/>
              </div>
              <div class="flex_center">政治面貌：
                <dict-tag
                    :options="dict.type['politics_status']"
                    :value="(userInfo.politicalLandscape|| '').split(';')"
                    myTag="phrases"/>
              </div>
              <div>生日：{{ userInfo.birthday }}</div>
            </div>
          </div>
        </div>
        <div class="item2 m_r_20 p_20_10">
          <Title :title="'常用应用'" @clickMore="handleClickApp"></Title>
          <div class="m_t_10 m_b_20 app_grid" v-if="determineShow(oftenUserApp.slice(0,4))">
            <div v-for="(item,index) in oftenUserApp.slice(0,4)" :key="index">
              <!--              <applicate-item-->
              <!--                  :img-src="item.iconUrl"-->
              <!--                  :item="item"-->
              <!--                  :app-title="item.appName"></applicate-item>-->
            </div>

          </div>
          <div style="text-align: center">
            <!--            <div>-->
            <!--              <img  v-if="oftenUserApp.length===0" src="@/assets/images/noData.png">-->
            <!--            </div>-->
            <el-empty :image-size="80" v-if="oftenUserApp.length===0"></el-empty>
          </div>
        </div>
        <div class="item3 p_20_10">
          <Title :title="'通知通告'" @clickMore="$router.push('/oa')"></Title>
          <div class="m_t_10" v-if="oaList.length!==0" v-loading="vLoading1" style="height:200px;overflow-y: auto">
            <div v-for="item in oaList" :key="item.contentId">
              <message :info="item" @handleClick="clickMessage(item)"></message>
            </div>
          </div>
          <div style="text-align: center;margin-top: 20px">
            <div v-if="oaList.length===0" class="fs_14">
              <img src="@/assets/images/noData.png">
              <div style="color:rgba(51,51,51,1)">暂无数据</div>
            </div>
          </div>
        </div>
      </div>
      <div class="m_t_20">
        <!--   整数列应用-->
        <div v-if="isInter">
          <div :class="roleAppArr.length>1?'grid':''">
            <!--            <applicate @handleClick="handleClickApp(item)"-->
            <!--                       v-for="(item,index) in roleAppArr"-->
            <!--                       :key="index"-->
            <!--                       :info="item"-->
            <!--                       :title="item.name"></applicate>-->
          </div>
        </div>
        <!--        非整数列应用-->
        <div v-if="!isInter">
          <div v-if="roleAppArr.length===1">
            <!--            <applicate :is-more="true" @handleClick="handleClickApp(item)"-->
            <!--                       v-for="(item,index) in roleAppArr"-->
            <!--                       :key="index"-->
            <!--                       :info="item"-->
            <!--                       :title="item.name"></applicate>-->
          </div>
          <div v-if="roleAppArr.length>1">
            <div :class="roleAppArr.length>1?'grid':''">
              <!--              <applicate @handleClick="handleClickApp(item)"-->
              <!--                         v-for="(item,index) in roleAppArr.slice(0,roleAppArr.length-1)"-->
              <!--                         :key="index"-->
              <!--                         :info="item"-->
              <!--                         :title="item.name"></applicate>-->
            </div>
            <div>
              <applicate :is-more="true" @handleClick="handleClickApp(item)"
                         v-for="(item,index) in roleAppArr.slice(roleAppArr.length-1,roleAppArr.length)"
                         :key="index"
                         :info="item"
                         :title="item.name"></applicate>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Title from '../components/title.vue'
import {mapGetters} from 'vuex'
import Message from "@/pages/components/Index/message.vue";
import {getOaList} from "@/api/OA";
import {getAppSpace, getOftenApp, getUserAllApp, setThisRole, setUseApp} from "@/api/home";
import {determineShow} from "@/pages/utils/tools";

export default {
  components: {
    Message,
    Title
  },
  data() {
    return {
      vLoading: false,
      params: {
        pageNum: 1,
        pageSize: 12,
      },
      oaList: [],
      total: '',
      vLoading1: false,
      showMoreApp: false,
      roleAppArr: [],
      moreApp: {},
      moreAppTitle: '',
      showAppLen: '',
      isInter: '',
      oftenUserApp: [],
      allApp: []
    }
  },
  dicts: ['user_nation', 'politics_status', 'sys_user_sex'],
  computed: {
    ...mapGetters(['name', 'userInfo', 'avatar', 'role', 'roles', 'defRole']),
    // getArr(arr) {
    //   let obj = []
    //   for (let i = 0; i < arr.length; i++) {
    //     if () {
    //     }
    //   }
    // },
  },
  mounted() {
    this.setThisRole()
    this.loadData()
    this.loadSpaceApp()
    this.loadOftenAppData()
  },
  methods: {
    determineShow,
    getArr(data) {
      let obj = []
      for (let i = 0; i < data.length; i++) {
        if (data[i].pcTag === 0 || data[i].pcTag === '0') {
          obj.push(data[i])
        }
      }
      return obj
    },
    setThisRole() {
      setThisRole({
        roleKey: this.role.role_key,
        agencyId: this.role.agency_id,
        stageId: this.role.stage_id
      }).then(res => {
      });
    },
    // 常用应用
    loadOftenAppData() {
      this.oftenUserApp = []
      getOftenApp({role_key: this.role.role_key}).then(res => {
        if (res.code == 200) {
          let data = res.data
          for (let i = 0; i < data.length; i++) {
            if (data[i].pcTag === 0 || data[i].pcTag === '0') {
              this.oftenUserApp.push(data[i])
            }
          }
        }
      })
    },
    // 全部应用
    loadAllAppData() {
      getUserAllApp().then(res => {
        this.allApp = res.data
      })
    },
    setUseApp(item) {
      let params = {
        appId: '',
        appInfoId: ''
      }
      setUseApp(params).then(res => {

      })
    },
    handleClick(type) {
      if (type === 2) {
        this.$router.push({
          path: '/oa'
        })
      }
    },
    switchHandle(item) {
      this.$store.dispatch('SetRole', item).then((res) => {
        if (item.role_type === 'wor') {
          this.$router.push({path: '/school'});
        } else if (item.role_type === 'tea') {
          this.$router.push({path: '/teacher'});
        } else if (item.role_type === 'stu') {
          this.$router.push({path: '/student'});
        } else if (item.role_type === 'par') {
          this.$router.push({path: '/parents'});
        } else {
          this.loadData()
          this.loadSpaceApp()
          this.loadOftenAppData()
        }
      })
    },
    // 获取空间应用
    loadSpaceApp() {
      this.roleAppArr = []
      getAppSpace({roleKey: this.role.role_key}).then(res => {
        let data = res.data
        for (let i = 0; i < data.length; i++) {
          let apps = data[i].apps
          let obj = []
          for (let k = 0; k < apps.length; k++) {
            if (apps[k].pcTag === 0 || apps[k].pcTag === '0') {
              obj.push(apps[k])
            }
          }
          if (obj.length !== 0) {
            this.roleAppArr.push({
              appType: data[i].appType,
              name: data[i].name,
              apps: obj
            })
          }
        }
        let length = Number(this.roleAppArr.length)
        if (length % 2 === 0) {
          this.isInter = true
        } else {
          this.isInter = false
          this.showAppLen = parseInt(length / 2) + 1
        }
      })
    },
    // 通知
    loadData() {
      this.vLoading1 = true
      getOaList({
        type: '', ...this.params,
        agencyId: this.role.agency_id
      }).then(res => {
        if (res.code === 200) {
          this.oaList = res.rows
          this.total = res.total
        }
      }).finally(() => {
        this.vLoading1 = false
      })
    },
    clickMessage(item) {
      this.$router.push({
        path: '/oa/detail',
        query: {
          type: 0,
          id: item.contentId
        }
      })
    },
    handleClickApp() {
      this.$router.push({
        path: '/more/app'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container-box {
  .top-container {
    display: flex;

    .item1 {
      width: calc(25% - 40px);
      background: url('@/assets/images/bgPic/manage.png') no-repeat;
      background-size: 100% 100%;
      border-radius: 10px;
      padding: 30px 20px 20px 20px;
      //box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.10);

      .avatar {
        padding: 0 0.8rem;
        width: 4rem;
        height: 4rem;
      }

      .role_card {
        margin-top: 10px;
        padding: 10px;
        width: 225px;
        height: 110px;
        opacity: 0.4;
        background: #ffffff;
        font-size: 14px;
        border-radius: 6px;
      }

      .change {
        width: 70px;
        height: 26px;
        right: 0;
        float: right;
        font-size: 15px;
        text-align: center;
        line-height: 26px;
        background: #3888f7;
        border-radius: 15px;
        color: #fff;
      }
    }

    .item2 {
      width: calc(50% - 40px);
      border-radius: 10px;
      background: #fff;

      .soft-box {
        width: 265px;
        margin-right: 10px;
        cursor: pointer;
        height: 82px;
        background: #ffffff;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
      }
    }

    .item3 {
      width: calc(25% - 40px);
      border-radius: 10px;
      background: #fff;
    }
  }
}

.navigation {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;

  .switch {
    text-align: center;
  }
}

.grid {
  display: grid;
  grid-template-columns: repeat(2, 49%);
  justify-content: space-between;
  //grid-auto-rows: 48px;
}

.app_grid {
  display: grid;
  grid-template-columns: repeat(2, 49%);
  gap: 20px 20px;
  justify-content: space-between;
}

.back {
  width: 70px;
  height: 26px;
  margin-bottom: 10px;
  cursor: pointer;
  font-size: 15px;
  text-align: center;
  line-height: 26px;
  background: #3888f7;
  border-radius: 15px;
  color: #fff;
}

::v-deep.el-empty {
  padding: 0 !important;
}
</style>
