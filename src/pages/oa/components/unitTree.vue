<template>
  <el-dialog class="reset_dialog"
             :visible.sync="showDialog"
             append-to-body
             :close-on-click-modal="false"
             :width="'63%'"
             :show-close="false"
             @open="handleOpen2"
  >
    <template slot="title">
      <div class="flex_space">
        <div style="width: 5px;"></div>
        <div class="fs_bold dialogTitle">{{ title }}</div>
        <div class="closeIcon pointer" @click="dialogClosed">
          <i class="el-icon-error icon fs_18"></i>
        </div>
      </div>
    </template>
    <div style="display: flex">
      <div style="flex:1;border:1px solid #EAEAEA">
        <div style="padding:10px;border-bottom: 1px solid #EAEAEA">
          <el-input
              v-model='filterKey'
              clearable
              placeholder="请输入关键字搜索">
          </el-input>
        </div>
        <div v-show="!showUsedGroups" v-loading="vLoading" style="height:340px;overflow-y: auto;">
          <el-tree
              ref="tree"
              :data="treeData"
              show-checkbox
              node-key="id"
              :filter-node-method="filterNode"
              @check="unHaveUsedGroupHandleSelectTreeNodes">
          </el-tree>
        </div>
        <div v-if="showUsedGroups">
          <div style="border-bottom: 1px solid #EAEAEA;height: 40px">
            <div style="display: flex;align-items: center;">
              <div @click="changeUnitType(0)" class="pointer"
                   :class="currentUnitType===0?'current-selected':'selected-item'">
                机构用户
              </div>
              <div @click="changeUnitType(1)" class="pointer"
                   :class="currentUnitType===1?'current-selected':'selected-item'">
                常用分组
              </div>
            </div>
          </div>
          <div v-show="currentUnitType===0" v-loading="vLoading" style="height:340px;overflow-y: auto;">
            <el-tree
                ref="tree"
                :data="treeData"
                show-checkbox
                node-key="id"
                :filter-node-method="filterNode2"
                @check="haveUsedGroupHandleSelectTreeNodes">
            </el-tree>
          </div>
          <div v-show="currentUnitType===1" v-loading="vLoading" style="height:340px;overflow-y: auto;">
            <el-tree
                ref="usedTree"
                :data="usedGroupsList"
                show-checkbox
                node-key="id"
                :filter-node-method="filterNode2"
                @check="handleSelectUsedNodes">
              <div slot-scope="{node,data}">
                <div v-if="data.groupName">{{ data.groupName }}</div>
                <div v-if="data.fullName">{{ data.fullName }}</div>
                <div v-if="data.userName">{{ data.userName }}</div>
                <div></div>
              </div>

            </el-tree>
          </div>
        </div>
      </div>
      <!--      已选择列表1-->
      <div v-if="showUsedGroups" style="flex:1;margin-left:10px;border:1px solid #EAEAEA">
        <div style="border-bottom:1px solid #EBEEF5;padding:16px 10px;"
             class="flex_space aligin_center">
          <!--      ({{ selectedTreeList.length > 0 ? selectedTreeList.length : 0 }})-->
          <div>已选择列表</div>
          <div class="pointer clear-box" @click="handleClear">清空列表</div>
        </div>
        <!--        showCurrentArr.length!==0-->
        <div style="height:340px;overflow-y: auto;">
          <el-tree v-if="selectedUserAll.length!==0"
                   ref="selectTree"
                   v-loading="vLoading"
                   :default-expand-all="true"
                   :data="selectedUserAll"
                   node-key="id"
          >
            <div class="flex fs_14" style="justify-content: space-between;width:100%" slot-scope="{ node, data }">
              <div>
                <div>{{ data.label || data.fullName || data.groupName || data.userName }}</div>
              </div>
              <div @click="deleteHaveUsedGroupNode(node,data)">删除</div>
            </div>
          </el-tree>
          <div v-if="selectedUserAll.length===0">
            <el-empty description="暂无选择"></el-empty>
          </div>
        </div>
      </div>
      <!--      无常用分组选择列表 -->
      <div v-if="!showUsedGroups" style="flex:1;margin-left:10px;border:1px solid #EAEAEA">
        <div style="border-bottom:1px solid #EBEEF5;padding:16px 10px;"
             class="flex_space aligin_center">
          <!--      ({{ selectedTreeList.length > 0 ? selectedTreeList.length : 0 }})-->
          <div>已选择列表</div>
          <div class="pointer clear-box" @click="handleClear">清空列表</div>
        </div>
        <!--        showCurrentArr.length!==0-->
        <div style="height:340px;overflow-y: auto;">
          <el-tree v-if="showCurrentArr.length!==0"
                   ref="selectTree"
                   v-loading="vLoading"
                   :default-expand-all="true"
                   :data="showCurrentArr"
                   node-key="id"
          >
            <div class="flex fs_14" style="justify-content: space-between;width:100%" slot-scope="{ node, data }">
              <div>
                <div>{{ node.label }}</div>
              </div>
              <div @click="deleteOrgan(node,data)">删除</div>
            </div>
          </el-tree>
          <div v-if="showCurrentArr.length===0">
            <el-empty description="暂无选择"></el-empty>
          </div>
        </div>
      </div>
      <div v-if="false" style="flex:1;margin-left:10px;border:1px solid #EAEAEA">
        <div style="border-bottom:1px solid #EBEEF5;padding:16px 10px;"
             class="flex_space aligin_center">
          <div>已选择({{ selectedTreeList.length > 0 ? selectedTreeList.length : 0 }})</div>
          <div class="pointer clear-box" @click="handleClear">清空列表</div>
        </div>
        <div class="p_10" style="height:340px;overflow-y: auto;">
          <div v-for="(item,index) in selectedTreeList" :key="item.id"
               class="p_10 br_5 m_b_10 flex_c"
               style="background: #F7FAFF;">
            <div style="width: 90%;">
              <div class="c_333 fs_16  flex_center">
                <div>{{ item.label }}</div>
                <div class="fs_14"></div>
              </div>
              <div class="c_666 m_t_5">{{ item.tempPath }}</div>
            </div>
            <div class="flex_center pointer" style="width:10%;height:30px;padding:0 10px;
            background: #DEECFE;border-radius: 5px;color:#1B77F5;
            border: 1px solid #1B77F5">
              <img style="width: 18px;height:18px" src="../../images/delete_icon.png">
              <div class=" fs_12" @click="newDeleteSelectOrg(item)">删除</div>
            </div>
          </div>
          <div v-if="selectedTreeList.length===0">
            <el-empty description="暂无选择"></el-empty>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" style="text-align: center">
      <el-button class="cancel" @click="dialogClosed">取消</el-button>&nbsp;
      <el-button class="submit" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {getGroupsSchoolTree, getUserTree} from "@/api/OA";
import {mapGetters} from 'vuex'

export default {
  components: {},
  props: {
    title: {
      type: String,
      default: '',
    },
    dialogWidth: {
      type: Number,
      default: 500
    },
    isClear: {
      type: Boolean,
      default: false
    },
    isRefresh: {
      type: Boolean,
      default: false
    },
    ifForward: {//是否转发
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showDialog: false,
      vLoading: false,
      filterKey: '',
      props: {
        label: 'name',
        children: 'zones'
      },
      defaultProps: {
        children: 'children',
        label: 'groupName'
      },
      postParams: {},
      selectArr: [],
      treeData: [],
      showCurrentArr: [],
      leftCheckTree: [],
      params: {
        agencyIds: [],
        stageIds: [],
        deptIds: [],
        receivePeoples: []
      },
      rightCheckTree: [],
      currentData: '',
      isDelete: false,
      treeList: [],
      selectedTreeList: [],
      currentUnitType: 0,
      usedGroupsList: [],
      usedCheckedTree: [],
      usedSelected: [],
      showUsedGroups: false,
      selectedUserAll: [],//有常用分组已选择展示
      concatNodesList: [],//结合两组树结构
      unUsedSelectNodes: [],//用户结构已选择节点 最小级
      UsedSelectNodes: [],//常用分组已选择节点 最小级
      childProps: {
        children: 'school',
        label: 'fullName'
      },
    }
  },
  watch: {
    filterKey(val) {
      if (this.currentUnitType == 0) {
        this.$refs.tree.filter(val);
        if (!val) {
          this.close();
        }
      } else {
        this.$refs.usedTree.filter(val);
        if (!val) {
          this.close2();
        }
      }
    },
  },
  computed: {
    ...mapGetters(['role']),
  },
  methods: {
    changeUnitType(type) {
      this.currentUnitType = type;
    },
    // 判断是否有常用分组
    determineIsHaveUsedUnitGroups() {
      // this.vLoading = true;
      getGroupsSchoolTree().then(res => {
        if (res.code == 200 && res.data && res.data.length !== 0) {
          this.showUsedGroups = true;
          this.usedGroupsList = res.data;
          this.addGroupsId(this.usedGroupsList, 1);
        }
        this.loadUsedTreeData()
      }).finally(() => {
        // this.vLoading = false;
      })
    },
    handleOpen() {
      this.currentUnitType = 0;
      this.leftCheckTree = [];
      this.selectArr = [];
      this.filterKey = '';
      this.clearSelect();
      this.showCurrentArr = []
      this.params = {
        agencyIds: [],
        stageIds: [],
        deptIds: [],
        receivePeoples: []
      }
      this.vLoading = true
      getUserTree({
        type: 'oa',
        thisRoleKey: this.role.role_key,
        thisAgencyId: this.role.agency_id,
        thisStageId: this.role.stage_id
      }).then((res) => {
        this.treeData = res.data
      }).finally(() => {
        this.vLoading = false
      })
    },
    handleOpen2() {
      this.currentUnitType = 0;
      this.filterKey = '';
      if (this.isClear) {//条件清空
        this.leftCheckTree = [];
        this.selectArr = [];
        this.selectedTreeList = [];
        this.showCurrentArr = [];
        this.selectedTreeList = [];
        this.currentUnitType = 0;
        this.usedGroupsList = [];
        this.usedCheckedTree = [];
        this.usedSelected = [];
        this.showUsedGroups = false;
        this.treeData = [];
        this.selectedUserAll = [];//有常用分组已选择展示
        this.concatNodesList = [];//结合两组树结构
        this.unUsedSelectNodes = [];//用户结构已选择节点 最小级
        this.UsedSelectNodes = [];//常用分组已选择节点 最小级
        this.params = {
          agencyIds: [],
          stageIds: [],
          deptIds: [],
          receivePeoples: []
        }
        this.determineIsHaveUsedUnitGroups();
        this.clearSelect();
      }
    },
    loadUsedTreeData() {
      this.vLoading = true;
      if (this.ifForward) {
        var params = {
          type: 'oa',
          thisRoleKey: this.role.role_key,
          thisAgencyId: this.role.agency_id,
          thisStageId: this.role.stage_id,
          isCopyTo: true
        }
      } else {
        var params = {
          type: 'oa',
          thisRoleKey: this.role.role_key,
          thisAgencyId: this.role.agency_id,
          thisStageId: this.role.stage_id,
        }
      }
      getUserTree(params).then((res) => {
        this.treeData = res.data;
        this.arrAddLevel(this.treeData, 1);
      }).finally(() => {
        this.vLoading = false;
      })
    },
    // 给树形结构数据添加层级 array是需要添加层级的树形结构数据，level是需要添加树形结构的层级
    arrAddLevel(array, level) {
      if (!array || !array.length) return
      array.forEach(item => {
        item.level = level
        item.type = 'unused'
        if (item.children && item.children.length) {
          this.arrAddLevel(item.children, level + 1)
        }
      })
    },
    addGroupsId(array, level) {
      if (!array || !array.length) return
      array.forEach(item => {
        item.level = level;
        item.type = "used"
        if (item.groupId && !item.id) {
          item.id = item.groupId;
        } else if (item.userId) {
          item.id = item.userId;
        }
        if (item.users) {
          item.children = item.users
        }
        item.label = item.groupName || item.userName || item.fullName
        if (item.children && item.children.length) {
          this.addGroupsId(item.children, level + 1)
        }
      })
    },
    dialogClosed() {
      this.showDialog = false;
      if (this.isRefresh) {
        // this.$emit('closeDialog')
      }
      // this.$emit('closeDialog')
    },
    handleClose() {
    },
    // 关闭所有节点
    close() {
      this.closeAllNodes(this.$refs.tree.store.root)
    },
    // 关闭常用分组节点
    close2() {
      this.closeAllNodes(this.$refs.usedTree.store.root)
    },
    closeAllNodes(node) {
      node.expanded = this.defaultExpand
      for (let i = 0; i < node.childNodes.length; i++) {
        // 改变节点的自身expanded状态
        node.childNodes[i].expanded = this.defaultExpand
        // 遍历子节点
        if (node.childNodes[i].childNodes.length > 0) {
          this.closeAllNodes(node.childNodes[i])
        }
      }
    },
    // 选择确定
    handleSubmit() {
      this.params = {
        agencyIds: [],
        stageIds: [],
        deptIds: [],
        receivePeoples: []
      };
      // 含有常用分组
      if (this.showUsedGroups) {
        let selected = [...this.UsedSelectNodes, ...this.unUsedSelectNodes];
        if (selected.length !== 0) {
          for (let i = 0; i < selected.length; i++) {
            if (selected[i].type == 'unused') {
              if (selected[i].isUser) {
                this.params.receivePeoples.push(selected[i]);
              }
            } else if (selected[i].userName) {
              this.params.receivePeoples.push(selected[i]);
            }
          }
          // console.log(this.params)
          this.$emit('confirm', this.params);
          this.showDialog = false;
        } else {
          this.$message({
            message: '当前还未选择',
            type: 'warning'
          })
        }
      } else {
        if (this.selectArr.length !== 0) {
          for (let i = 0; i < this.selectArr.length; i++) {
            if (this.selectArr[i].isUser) {
              this.params.receivePeoples.push(this.selectArr[i]);
            }
          }
          this.$emit('confirm', this.params);
          this.showDialog = false;
        } else {
          this.$message({
            message: '当前暂还选择',
            type: 'warning'
          })
        }
      }
    },
    treeToArray(arr) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].children) {
          this.treeToArray(arr[i].children)
        } else {
          let obj = arr[i];
          this.selectedTreeList.push(this.getItemByIdInTree(this.treeData, obj.id))
        }
      }
    },
    getItemByIdInTree(tree, value, path = "") {
      for (var i = 0; i < tree.length; i++) {
        let tempPath = path
        if (!tree[i].isUser) {
          tempPath = `${tempPath ? tempPath + '-' : tempPath}${tree[i].label}` // 避免出现在最前面的/
        }
        if (tree[i].id == value) {
          let obj = tree[i];
          obj.tempPath = tempPath
          return obj
        } else if (tree[i].children) {
          let result = this.getItemByIdInTree(tree[i].children, value, tempPath)
          if (result) {
            return result
          }
        }
      }
    },
    getItemByIdInTreeInfo(tree, value) {
      for (var i = 0; i < tree.length; i++) {
        let item = tree[i]
        if (tree[i].id == value) {
          return item
        } else if (tree[i].children) {
          let result = this.getItemByIdInTree(tree[i].children, value)
          if (result) {
            return result
          }
        }
      }
    },
    // 树节点重构
    restructureTreeNodes(arr, level) {
      level++;
      let nodes = this.concatNodesList.filter(o => o.level == level);
      let ids = nodes.map(item => item.id);//id数组
      for (let i = 0; i < arr.length; i++) {
        if (!ids.includes(arr[i].id)) {
          arr.splice(i--, 1);
        } else {
          if (Array.isArray(arr[i].children) && arr[i].children.length > 0) {
            this.restructureTreeNodes(arr[i].children, level);
          }
        }
      }
      return arr;
    },
    // 无常用分组的用户机构数节点选择
    haveUsedGroupHandleSelectTreeNodes(nodes) {
      this.$nextTick(() => {
        this.leftCheckTree = this.$refs.tree.getCheckedNodes(false, true);
        this.selectedUserAll = [];
        this.concatNodesList = [...this.leftCheckTree, ...this.usedCheckedTree];
        let arr = JSON.parse(JSON.stringify(this.concatNodesList));
        this.selectedUserAll = this.restructureTreeNodes(arr, 0);
        this.unUsedSelectNodes = this.$refs.tree.getCheckedNodes(true);//已选择节点
      })
    },
    // 常用分组树结构选择
    handleSelectUsedNodes() {
      this.$nextTick(() => {
        let list = [];
        list = this.$refs.usedTree.getCheckedNodes(false, true);
        this.selectedUserAll = [];
        this.usedCheckedTree = _.clone(list);
        this.concatNodesList = list.concat(this.leftCheckTree);
        let arr = JSON.parse(JSON.stringify(this.concatNodesList));
        this.selectedUserAll = this.restructureTreeNodes(arr, 0);
        this.UsedSelectNodes = this.$refs.usedTree.getCheckedNodes(true);//已选择节点
      })
    },
    // 有常用分组情况删除节点方法
    deleteHaveUsedGroupNode(node, data) {
      if (data.type && data.type == 'unused') {
        this.$nextTick(() => {
          this.$refs.tree.setChecked(data, false, true);
          this.selectedTreeList = [];
          this.leftCheckTree = this.$refs.tree.getCheckedNodes(false, true)
          // let arr = JSON.parse(JSON.stringify(this.leftCheckTree));
          // this.showCurrentArr = this.handleChooseNode(arr, 0);
          this.selectArr = this.$refs.tree.getCheckedNodes(true);//已选择节点
          this.selectedUserAll = [];
          this.concatNodesList = [...this.leftCheckTree, ...this.usedCheckedTree];
          let arr = JSON.parse(JSON.stringify(this.concatNodesList));
          this.selectedUserAll = this.restructureTreeNodes(arr, 0);
        })
      } else {
        this.$nextTick(() => {
          this.$refs.usedTree.setChecked(data, false, true);
          let list = [];
          list = this.$refs.usedTree.getCheckedNodes(false, true);
          this.selectedUserAll = [];
          this.usedCheckedTree = _.clone(list);
          this.concatNodesList = list.concat(this.leftCheckTree)
          let arr = JSON.parse(JSON.stringify(this.concatNodesList));
          this.selectedUserAll = this.restructureTreeNodes(arr, 0);
        })
      }
    },
    // 无常用分组的用户机构数节点选择
    unHaveUsedGroupHandleSelectTreeNodes(nodes) {
      this.$nextTick(() => {
        this.selectedTreeList = [];
        this.leftCheckTree = this.$refs.tree.getCheckedNodes(false, true);
        let arr = JSON.parse(JSON.stringify(this.leftCheckTree));
        this.showCurrentArr = this.handleChooseNode(arr, 0);
        this.selectArr = this.$refs.tree.getCheckedNodes(true);//已选择节点
      })
    },
    // 无常用分组情况删除节点方法
    deleteOrgan(node, data) {
      this.$nextTick(() => {
        this.selectedTreeList = [];
        this.showCurrentArr = []
        this.leftCheckTree = []
        this.$refs.tree.setChecked(data, false, true)
        this.leftCheckTree = this.$refs.tree.getCheckedNodes(false, true)
        let arr = JSON.parse(JSON.stringify(this.leftCheckTree))
        this.showCurrentArr = this.handleChooseNode(arr, 0)
        this.selectArr = this.$refs.tree.getCheckedNodes(true)
      })
    },
    // 树结构节点选择方法2
    handleSelectTreeNodes2(nodes) {
      this.$nextTick(() => {
        this.selectedTreeList = [];
        this.leftCheckTree = this.$refs.tree.getCheckedNodes(false, true)
        let arr = JSON.parse(JSON.stringify(this.leftCheckTree));
        let result = this.handleChooseNode(arr, 0);
        this.treeToArray(result)
        this.selectArr = this.$refs.tree.getCheckedNodes(true);//已选择节点
      })
    },
    // 已选择的树结构节点的删除方法2
    newDeleteSelectOrg(item) {
      this.$nextTick(() => {
        this.$refs.tree.setChecked(item.id, false, true);
        this.selectedTreeList = this.selectedTreeList.filter(function (it) {
          return it.id !== item.id
        })
        this.selectArr = this.$refs.tree.getCheckedNodes(true);//已选择节点
      })
    },
    clearSelect() {
      if (this.showUsedGroups) {
        if (this.concatNodesList.length !== 0) {
          this.usedGroupsList.forEach(item => {
            this.$nextTick(() => {
              this.$refs.usedTree.setChecked(item.id, false, true);
            })
          })
          this.concatNodesList = []
        }
      }
      if (this.selectArr.length !== 0) {
        this.treeData.forEach(item => {
          this.$nextTick(() => {
            this.$refs.tree.setChecked(item.id, false, true)
          })
          this.selectArr = []
        })
      }
    },
    // 根据结构逐层筛选重构
    handleChooseNode(arr, level) {
      level++;
      let nodes = this.leftCheckTree.filter(o => o.level == level);
      let ids = nodes.map(item => item.id);
      for (let i = 0; i < arr.length; i++) {
        if (!ids.includes(arr[i].id)) {
          arr.splice(i--, 1);
        } else {
          if (Array.isArray(arr[i].children) && arr[i].children.length > 0) {
            this.handleChooseNode(arr[i].children, level)
          }
        }
      }
      return arr;
    },
    // 清空列表
    handleClear() {
      this.showCurrentArr = [];
      this.leftCheckTree = [];
      this.selectArr = [];
      this.selectedTreeList = [];
      this.clearSelect();
      this.UsedSelectNodes = [];
      this.unUsedSelectNodes = [];
      this.concatNodesList = [];
      this.selectedUserAll = [];
      this.usedCheckedTree = []
    },
    // 搜索功能
    filterNode(value, data, node) {
      if (!value) return true;
      return this.findSearchKey2(node, value);
    },
    // 搜索功能
    filterNode2(value, data, node) {
      if (!value) return true;
      return this.findSearchKey2(node, value);
    },
    // 递归搜索父级是否包含关键字
    findSearchKey(node, key) {
      if (node.label.indexOf(key) !== -1) {
        return true;
      } else {
        if (node.parent.parent == null) {
          return false;
        } else {
          return this.findSearchKey(node.parent, key);
        }
      }
    },
    findSearchKey2(node, key) {
      if (node.label.indexOf(key) !== -1) {
        return true;
      } else {
        if (node.parent.parent == null) {
          return false;
        } else {
          return this.findSearchKey(node.parent, key);
        }
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.reset_dialog {
  font-size: 16px;

  .dialogTitle {
    position: relative;
  }

  .dialogTitle:before {
    content: "";
    position: absolute;
    left: 25%;
    bottom: -5px;
    width: 50%;
    border-radius: 5px;
    border-bottom: 3px solid #3888F7;
    box-sizing: border-box;
    opacity: .9;
  }

  .clear-box {
    width: 80px;
    padding: 4px;
    //font-size: 15px;
    text-align: center;
    background: #3888f7;
    border-radius: 15px;
    color: #fff;
  }
}

.current-selected {
  position: relative;
  height: 40px;
  color: #1B77F5;
  line-height: 40px;
  padding: 0 10px;
}

.current-selected:before {
  position: absolute;
  content: '';
  left: 0;
  bottom: 0;
  width: 100%;
  border-bottom: 2px solid #1B77F5;
  height: 1px;
}

.selected-item {
  padding: 0 10px;
  height: 40px;
  line-height: 40px;
}

::v-deep .el-tree-node__content {
  font-size: 15px;
  font-weight: bold;
  height: 50px;
  padding: 0 36px;
}

::v-deep .el-dialog {
  border-radius: 10px !important;
}

::v-deep {
  .el-dialog__body {
    padding: 10px 20px 20px 20px !important;
  }
}

::v-deep.el-table.cell {
  display: flex !important;
  align-items: center;
  justify-content: center;
}
</style>
