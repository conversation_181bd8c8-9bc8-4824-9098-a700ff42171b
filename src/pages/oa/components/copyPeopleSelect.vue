<template>
  <el-dialog class="reset_dialog"
             :visible.sync="showDialog"
             append-to-body
             :close-on-click-modal="false"
             :width="'63%'"
             :show-close="false"
             @open="handleOpen2"
             @close="dialogClosed"
  >
    <template slot="title">
      <div class="flex_space">
        <div style="width: 5px;"></div>
        <div class="fs_bold dialogTitle">{{ title }}</div>
        <div class="closeIcon pointer" @click="dialogClosed">
          <i class="el-icon-error icon fs_18"></i>
        </div>
      </div>
    </template>
    <div style="display: flex">
      <div style="flex:1;border:1px solid #EAEAEA">
        <div style="padding:10px;border-bottom: 1px solid #EAEAEA">
          <el-input
              v-model='filterKey'
              clearable
              placeholder="请输入关键字搜索">
          </el-input>
        </div>

        <div v-loading="vLoading" style="height:340px;overflow-y: auto;">
          <el-tree
              ref="tree"
              :data="treeData"
              show-checkbox
              node-key="id"
              :filter-node-method="filterNode"
              @check="handleSelectTreeNodes1">
          </el-tree>
        </div>
      </div>
      <!--      已选择列表1-->
      <div style="flex:1;margin-left:10px;border:1px solid #EAEAEA">
        <div style="border-bottom:1px solid #EBEEF5;padding:16px 10px;"
             class="flex_space aligin_center">
          <!--      ({{ selectedTreeList.length > 0 ? selectedTreeList.length : 0 }})-->
          <div>已选择列表</div>
          <div class="pointer clear-box" @click="handleClear">清空列表</div>
        </div>
        <!--        showCurrentArr.length!==0-->
        <div style="height:340px;overflow-y: auto;">
          <el-tree v-if="showCurrentArr.length!==0"
                   ref="selectTree"
                   v-loading="vLoading"
                   :default-expand-all="true"
                   :data="showCurrentArr"
                   node-key="id"
          >
            <div class="flex fs_14" style="justify-content: space-between;width:100%" slot-scope="{ node, data }">
              <div>
                <div>{{ node.label }}</div>
              </div>
              <div @click="deleteOrgan(node,data)">删除</div>
            </div>
          </el-tree>
          <div v-if="showCurrentArr.length===0">
            <el-empty description="暂无选择"></el-empty>
          </div>
        </div>
      </div>
      <!--      已选择列表2-->
      <div v-if="false" style="flex:1;margin-left:10px;border:1px solid #EAEAEA">
        <div style="border-bottom:1px solid #EBEEF5;padding:16px 10px;"
             class="flex_space aligin_center">
          <div>已选择({{ selectedTreeList.length > 0 ? selectedTreeList.length : 0 }})</div>
          <div class="pointer clear-box" @click="handleClear">清空列表</div>
        </div>
        <div class="p_10" style="height:340px;overflow-y: auto;">
          <div v-for="(item,index) in selectedTreeList" :key="item.id"
               class="p_10 br_5 m_b_10 flex_c"
               style="background: #F7FAFF;">
            <div style="width: 90%;">
              <div class="c_333 fs_16  flex_center">
                <div>{{ item.label }}</div>
                <div class="fs_14"></div>
              </div>
              <div class="c_666 m_t_5">{{ item.tempPath }}</div>
            </div>
            <div class="flex_center pointer" style="width:10%;height:30px;padding:0 10px;
            background: #DEECFE;border-radius: 5px;color:#1B77F5;
            border: 1px solid #1B77F5">
              <img style="width: 18px;height:18px" src="../../images/delete_icon.png">
              <div class=" fs_12" @click="newDeleteSelectOrg(item)">删除</div>
            </div>
          </div>
          <div v-if="selectedTreeList.length===0">
            <el-empty description="暂无选择"></el-empty>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" style="text-align: center">
      <el-button class="cancel" @click="dialogClosed">取消</el-button>&nbsp;
      <el-button class="submit" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {getUserTree} from "@/api/OA";
import {mapGetters} from 'vuex'

export default {
  components: {},
  props: {
    title: {
      type: String,
      default: '',
    },
    dialogWidth: {
      type: Number,
      default: 500
    },
    isClear: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showDialog: false,
      vLoading: false,
      filterKey: '',
      props: {
        label: 'name',
        children: 'zones'
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      postParams: {},
      selectArr: [],
      treeData: [],
      showCurrentArr: [],
      leftCheckTree: [],
      params: {
        agencyIds: [],
        stageIds: [],
        deptIds: [],
        receivePeoples: []
      },
      rightCheckTree: [],
      currentData: '',
      isDelete: false,
      treeList: [],
      selectedTreeList: []
    }
  },
  watch: {
    filterKey(val) {
      this.$refs.tree.filter(val)
      if (val == '') {
        this.close()
      }
    },
  },
  computed: {
    ...mapGetters(['role']),
  },
  methods: {
    handleOpen() {
      this.leftCheckTree = [];
      this.selectArr = [];
      this.filterKey = '';
      this.clearSelect();
      this.showCurrentArr = []
      this.params = {
        agencyIds: [],
        stageIds: [],
        deptIds: [],
        receivePeoples: []
      }
      this.vLoading = true
      getUserTree({
        type: 'oa',
        thisRoleKey: this.role.role_key,
        thisAgencyId: this.role.agency_id,
        thisStageId: this.role.stage_id,
        isCopyTo: true
      }).then((res) => {
        this.treeData = res.data
        this.arrayFlaglevel(this.treeData, 1)
      }).finally(() => {
        this.vLoading = false
      })
    },
    handleOpen2() {
      this.filterKey = '';
      if (this.isClear) {//条件清空
        this.leftCheckTree = [];
        this.selectArr = [];
        this.selectedTreeList = [];
        this.showCurrentArr = [];
        this.params = {
          agencyIds: [],
          stageIds: [],
          deptIds: [],
          receivePeoples: []
        }
        this.vLoading = true;
        this.clearSelect();
        getUserTree({
          type: 'oa',
          thisRoleKey: this.role.role_key,
          thisAgencyId: this.role.agency_id,
          thisStageId: this.role.stage_id,
          isCopyTo: true
        }).then((res) => {
          this.treeData = res.data
          this.arrayFlaglevel(this.treeData, 1)
        }).finally(() => {
          this.vLoading = false
        })
      }
    },
    // 给树形结构数据添加层级 array是需要添加层级的树形结构数据，level是需要添加树形结构的层级
    arrayFlaglevel(array, level) {
      if (!array || !array.length) return
      array.forEach(item => {
        item.level = level
        if (item.children && item.children.length) {
          this.arrayFlaglevel(item.children, level + 1)
        }
      })
    },
    dialogClosed() {
      this.showDialog = false;
    },
    // 关闭所有节点
    close() {
      this.defaultExpand = false
      this.closeAllNodes(this.$refs.tree.store.root)
    },
    closeAllNodes(node) {
      node.expanded = this.defaultExpand
      for (let i = 0; i < node.childNodes.length; i++) {
        // 改变节点的自身expanded状态
        node.childNodes[i].expanded = this.defaultExpand
        // 遍历子节点
        if (node.childNodes[i].childNodes.length > 0) {
          this.closeAllNodes(node.childNodes[i])
        }
      }
    },
    // 选择确定
    handleSubmit() {
      this.params = {
        agencyIds: [],
        stageIds: [],
        deptIds: [],
        receivePeoples: []
      };
      if (this.selectArr.length !== 0) {
        for (let i = 0; i < this.selectArr.length; i++) {
          if (this.selectArr[i].isUser) {
            this.params.receivePeoples.push(this.selectArr[i])
          }
        }
        this.$emit('confirm', this.params)
        this.dialogClosed()
      } else {
        this.$message({
          message: '当前暂未选择',
          type: 'warning'
        })
      }
    },
    treeToArray(arr) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].children) {
          this.treeToArray(arr[i].children)
        } else {
          let obj = arr[i];
          this.selectedTreeList.push(this.getItemByIdInTree(this.treeData, obj.id))
        }
      }
    },
    getItemByIdInTree(tree, value, path = "") {
      for (var i = 0; i < tree.length; i++) {
        let tempPath = path
        if (!tree[i].isUser) {
          tempPath = `${tempPath ? tempPath + '-' : tempPath}${tree[i].label}` // 避免出现在最前面的/
        }
        if (tree[i].id == value) {
          let obj = tree[i];
          obj.tempPath = tempPath
          return obj
        } else if (tree[i].children) {
          let reuslt = this.getItemByIdInTree(tree[i].children, value, tempPath)
          if (reuslt) {
            return reuslt
          }
        }
      }
    },
    getItemByIdInTreeInfo(tree, value) {
      for (var i = 0; i < tree.length; i++) {
        let item = tree[i]
        if (tree[i].id == value) {
          return item
        } else if (tree[i].children) {
          let reuslt = this.getItemByIdInTree(tree[i].children, value)
          if (reuslt) {
            return reuslt
          }
        }
      }
    },
    // 树结构节点选择方法1
    handleSelectTreeNodes1(nodes) {
      this.$nextTick(() => {
        this.selectedTreeList = [];
        this.leftCheckTree = this.$refs.tree.getCheckedNodes(false, true)
        let arr = JSON.parse(JSON.stringify(this.leftCheckTree));
        this.showCurrentArr = this.handleChooseNode(arr, 0);
        this.selectArr = this.$refs.tree.getCheckedNodes(true);//已选择节点
      })
    },
    // 已选择的树结构节点的删除方法1
    deleteOrgan(node, data) {
      this.$nextTick(() => {
        this.selectedTreeList = [];
        this.showCurrentArr = []
        this.leftCheckTree = []
        this.$refs.tree.setChecked(data, false, true)
        this.leftCheckTree = this.$refs.tree.getCheckedNodes(false, true)
        let arr = JSON.parse(JSON.stringify(this.leftCheckTree))
        this.showCurrentArr = this.handleChooseNode(arr, 0)
        this.selectArr = this.$refs.tree.getCheckedNodes(true)
      })
    },
    // 树结构节点选择方法2
    handleSelectTreeNodes2(nodes) {
      this.$nextTick(() => {
        this.selectedTreeList = [];
        this.leftCheckTree = this.$refs.tree.getCheckedNodes(false, true)
        let arr = JSON.parse(JSON.stringify(this.leftCheckTree));
        let result = this.handleChooseNode(arr, 0);
        this.treeToArray(result)
        this.selectArr = this.$refs.tree.getCheckedNodes(true);//已选择节点
      })
    },
    // 已选择的树结构节点的删除方法2
    newDeleteSelectOrg(item) {
      this.$nextTick(() => {
        this.$refs.tree.setChecked(item.id, false, true);
        this.selectedTreeList = this.selectedTreeList.filter(function (it) {
          return it.id !== item.id
        })
        this.selectArr = this.$refs.tree.getCheckedNodes(true);//已选择节点
      })
    },
    clearSelect() {
      this.treeData.forEach(item => {
        this.$nextTick(() => {
          this.$refs.tree.setChecked(item.id, false, true)
        })
      })
    },
    // 根据结构逐层筛选重构
    handleChooseNode(arr, level) {
      level++;
      let nodes = this.leftCheckTree.filter(o => o.level == level);
      let ids = nodes.map(item => item.id);
      for (let i = 0; i < arr.length; i++) {
        if (!ids.includes(arr[i].id)) {
          arr.splice(i--, 1);
        } else {
          if (Array.isArray(arr[i].children) && arr[i].children.length > 0) {
            this.handleChooseNode(arr[i].children, level)
          }
        }
      }
      return arr;
    },
    // 清空列表
    handleClear() {
      this.showCurrentArr = [];
      this.leftCheckTree = [];
      this.selectArr = [];
      this.selectedTreeList = [];
      this.clearSelect();
    },
    // 搜索功能
    filterNode(value, data, node) {
      if (!value) return true;
      return this.findSearchKey(node, value);
    },
    // 递归搜索父级是否包含关键字
    findSearchKey(node, key) {
      if (node.label.indexOf(key) !== -1) {
        return true;
      } else {
        if (node.parent.parent == null) {
          return false;
        } else {
          return this.findSearchKey(node.parent, key);
        }
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.reset_dialog {
  font-size: 16px;

  .dialogTitle {
    position: relative;
  }

  .dialogTitle:before {
    content: "";
    position: absolute;
    left: 25%;
    bottom: -5px;
    width: 50%;
    border-radius: 5px;
    border-bottom: 3px solid #3888F7;
    box-sizing: border-box;
    opacity: .9;
  }

  .clear-box {
    width: 80px;
    padding: 4px;
    //font-size: 15px;
    text-align: center;
    background: #3888f7;
    border-radius: 15px;
    color: #fff;
  }
}

::v-deep .el-tree-node__content {
  font-size: 15px;
  font-weight: bold;
  height: 50px;
  padding: 0 36px;
}

::v-deep .el-dialog {
  border-radius: 10px !important;
}

::v-deep {
  .el-dialog__body {
    padding: 10px 20px 20px 20px !important;
  }
}

::v-deep.el-table.cell {
  display: flex !important;
  align-items: center;
  justify-content: center;
}
</style>
