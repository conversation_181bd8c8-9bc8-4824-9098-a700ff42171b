<template>
  <el-dialog
      class="reset-dialog"
      :visible.sync="showDialog"
      append-to-body
      :close-on-click-modal="false"
      :width="'600px'"
      :show-close="false"
      @open="handleOpen"
  >
    <template slot="title">
      <div class="flex_alAndJsCenter">
        <img src="../../images/img2/tips.png">&nbsp;
        <div class="fs_bold fs_18">签收</div>
      </div>
    </template>
    <div>
      <div style="display: flex;margin-bottom: 12px" class="user-un-select">
        <div class="label-class ">
          <div>公文标题：</div>
        </div>
        <div style="width:400px;line-height: 20px">{{ info.noticeTitle }}
        </div>
      </div>
      <el-form inline :model="params" :rules="rule" ref="formRef" label-width="95px">
        <!--        <el-form-item label="公文标题：">-->
        <!--          &lt;!&ndash;          <div style="width:250px;">{{ info.noticeTitle }}</div>&ndash;&gt;-->
        <!--          <el-input style="width:250px;border:none !important" type="textarea"-->
        <!--                    autosize v-model="info.noticeTitle"></el-input>-->
        <!--        </el-form-item>-->
        <el-form-item label="办理意见：" prop="advice">
          <el-input style="width:400px;font-family: system-ui"
                    :rows="4"
                    v-model="params.advice"
                    type="textarea"
                    placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item class="user-un-select" v-if="info.type==='3'" label="上传附件：" style="width: 100%;" required>
          <div class="c_999 fs_14" style="color:#F35C5B ">
            <span></span>最多上传10个文件，压缩包文件上传.zip格式。
          </div>
          <div>
            <el-upload class="upload-demo"
                       action=""
                       accept=""
                       ref="upload"
                       :auto-upload="false"
                       :on-change="selectChange"
                       :show-file-list="false"
                       :http-request="httpRequest"
                       :on-success="success"
                       :on-exceed="handleExceed"
                       :limit="10"
                       multiple
                       :file-list="fileList">
              <!--              <div style="padding:8px 15px;background: #F0F0F0;border-radius: 5px;height:20px;line-height: 20px"-->
              <!--                   class="pointer">-->
              <!--                <span>上传文件</span>-->
              <!--              </div>-->
              <el-button class="upload_btn">
                <span>点击上传</span>
              </el-button>
            </el-upload>

          </div>

        </el-form-item>
        <div v-if="info.type==='3'">
          <draggable :list="imageFile"
                     animation="500"
                     force-fallback="true"
                     class="m_t_10">
            <div v-for="(item,index) in imageFile" :key="index" class="m_t_5 m_b_5">
              <div class="flex_c" style="width:100%;user-select: none">
                <div style="width:70%;display: flex;align-items: center;cursor: move">
                  <img style="width:28px;height:28px;margin-right: 2px" :src="item.url">
                  <div class="fs_14" style="width: calc(100% - 30px);">
                    {{ item.originalFileName }}
                    <!--                    <my-tooltip style="width: 100%" :title="item.fileName"></my-tooltip>-->
                  </div>
                </div>
                <div class="fs_14 flex_c" style="width:30%;margin-left:10px;color:#999;white-space: nowrap">
                  <div style="padding:5px 10px;background: #F0F0F0;border-radius: 5px" class="pointer"
                       @click="viewFile(item)">
                    <i class="el-icon-document-remove"></i>&nbsp;
                    <span>预览</span>
                  </div>
                  <div style="padding:5px 10px;background: #F0F0F0;border-radius: 5px" class="pointer"
                       @click="deleteImg(item,index)">
                    <i class="el-icon-delete"></i>&nbsp;
                    <span>删除</span>
                  </div>
                </div>
              </div>
            </div>
          </draggable>
        </div>
      </el-form>
    </div>
    <div class="user-un-select" style="text-align: center;margin-top:20px">
      <el-button class="cancel" @click="dialogClosed">取消</el-button>&nbsp;&nbsp;
      <el-button class="pass" @click="handleSubmit">签收</el-button>&nbsp;
      <el-button class="pass" @click="signForward">签收并转发</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {handleDeleteFile, signContent} from "@/api/OA";
import axios from "axios";
import {Base64 as base64} from "js-base64";
import MyTooltip from "@/components/Tooltip.vue";
import draggable from 'vuedraggable'
import {aesDecrypt2, generateRandomStr, getAesString} from "@/pages/utils/tools";
import {getSessionToken} from "@/utils/local";
import {mapGetters} from "vuex";

export default {
  components: {MyTooltip, draggable},
  props: {
    info: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  data() {
    return {
      icons: {
        pic: require('@/pages/images/img2/pic.png'),
        word: require('@/pages/images/img2/docx.png'),
        xlsx: require('@/pages/images/img2/xls.png'),
        ppt: require('@/pages/images/img2/ppt.png'),
        pdf: require('@/pages/images/img2/pdf.png'),
        video: require('@/pages/images/img2/vedio.png'),
        file: require('@/pages/images/img2/file.png'),
      },
      showDialog: false,
      advice: "",
      imageFile: [],
      fileList: [],
      params: {
        advice: '已阅',
        contentId: '',
        files: [],
      },
      isDelete: false,
      rule: {
        advice: [
          {required: true, message: '请填写办理意见', trigger: 'blur'}
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'name', 'role'])
  },
  methods: {
    handleOpen() {
      this.advice = ''
      this.params = {
        advice: '已阅',
        contentId: '',
        files: []
      }
      this.imageFile = [];
      this.fileList = [];
      this.isDelete = false;
    },
    dialogClosed() {
      // 未签收取消则删除所有文件
      if (this.info.type === '3' && this.imageFile.length !== 0) {
        let ossFilePaths = []
        this.imageFile.forEach(item => {
          ossFilePaths.push(item.ossFilePath)
        })
        handleDeleteFile(ossFilePaths).then((res) => {
        })
        this.showDialog = false;
      } else {
        this.showDialog = false
      }
    },
    success() {
      this.$forceUpdate()
    },
    // 签收并转发
    signForward() {
      let that = this;
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          that.params.contentId = this.info.contentId;
          if (that.info.type === '3') {
            if (that.params.files.length === 0) {
              that.$message.warning('请上传文件')
            } else {

              signContent(this.params).then(res => {
                if (res.code === 200) {
                  this.$message({
                    type: "success",
                    message: "签收成功"
                  })
                  that.showDialog = false;
                  that.$emit('forward');
                }
              }).finally(() => {
                that.showDialog = false;
              })
            }
          } else {
            signContent(this.params).then(res => {
              if (res.code === 200) {
                this.$message({
                  type: "success",
                  message: "签收成功"
                })
                this.showDialog = false;
                that.$emit('forward');
              }
            }).finally(() => {
              this.showDialog = false;
            })
          }
        }
      })
    },
    selectChange(file, fileList) {
      var upload_file = document.getElementsByClassName('upload-demo')
      let uploadNum = 0;
      if (upload_file && upload_file.length > 0) {
        var upload = upload_file[0].getElementsByTagName('input')
        if (upload && upload.length > 0 && upload[0].files && upload[0].files.length > 0) {
          uploadNum = upload[0].files.length
        }
      }
      if (this.isDelete && (this.params.files.length + uploadNum >= 10)) {
        this.$selfMessage.warning('最多上传10个文件');
      } else {
        this.httpRequest(file)
      }
    },
    httpRequest(formItem) {
      let that = this
      let formData = new FormData()
      formData.append('file', formItem.raw)
      const loading = this.$loading({
        lock: true,
        text: '文件上传中',
        background: 'rgba(0,0,0,0.7)'
      })
      axios({
        headers: {
          Authorization: 'Bearer ' + getSessionToken(),
          'Content-Type': 'multipart/form-data',
          version: process.env.VUE_APP_VERSION
        },
        url: process.env.VUE_APP_BASE_API + '/minioFile/put-file',
        method: 'post',
        name: 'file',
        data: formData,
        timeout: 200000000
      }).then(res => {
        if (res.data && !res.data.code && (typeof res.data == 'string')) {
          let result = JSON.parse(aesDecrypt2(res.data))
          if (result.code == 200) {
            that.$message({
              message: '上传成功',
              type: 'success'
            })
            this.params.files.push(result.data);
            that.imageFile.push(result.data)
            for (let i = 0; i < that.imageFile.length; i++) {
              that.imageFile[i].url = that.isAssetTypeAnImage(that.imageFile[i].originalFileName)
            }
            loading.close()
            that.$emit('uploadSuccess', that.submitData)
            this.success()
          } else {
            this.$message({
              message: result.msg,
              type: 'warning'
            })
          }
        } else {
          let result = res.data
          if (result.code == 200) {
            that.$message({
              message: '上传成功',
              type: 'success'
            })
            this.params.files.push(result.data);
            that.imageFile.push(result.data)
            for (let i = 0; i < that.imageFile.length; i++) {
              that.imageFile[i].url = that.isAssetTypeAnImage(that.imageFile[i].originalFileName)
            }
            loading.close()
            that.$emit('uploadSuccess', that.submitData)
            this.success()
          } else {
            this.$message({
              message: result.msg,
              type: 'warning'
            })
          }
        }
        if (res.data.code === 200) {
          // that.$message({
          //   message: '上传成功',
          //   type: 'success'
          // })
          // this.params.files.push(res.data.data);
          // that.imageFile.push(res.data.data)
          // for (let i = 0; i < that.imageFile.length; i++) {
          //   that.imageFile[i].url = that.isAssetTypeAnImage(that.imageFile[i].originalFileName)
          // }
          // loading.close()
          // that.$emit('uploadSuccess', that.submitData)
          // this.success()
        }
      })
    },
    uploadSuccess(info) {
      this.listQuery.files = info
      this.imageFile = info
    },
    handleExceed() {
      this.$message.warning('最多上传10个文件')
    },
    //判断文件类型
    isAssetTypeAnImage(url) {
      let i = url.lastIndexOf('.'),
          ext = '';
      if (i > -1) {
        ext = url.substring(i + 1);
      }
      //图片
      if (
          [
            'png',
            'jpg',
            'jpeg',
            'bmp',
            'gif',
            'webp',
            'psd',
            'svg',
            'tiff',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.pic;
      } else if (['doc', 'docx'].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.word;
      } else if (['xls', 'xlsx',].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.xlsx;
      } else if (['pdf',].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.pdf;
      } else if (['ppt', 'pptx'].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.ppt;
      } else if (
          [
            'mp4',
            'flv',
            'm3u8',
            'rtmp',
            'hls',
            'rtsp',
            'mp3',
            'flac',
            'm4a',
            'ogg',
            'ape',
            'amr',
            'wma',
            'wav',
            'aac',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.video;
      } else {
        return this.icons.file;
      }
    },
    viewFile(item) {
      let watermarkTxt = ''
      if (this.role.role_type == 'stu') {
        watermarkTxt = this.name;
      } else {
        watermarkTxt = this.name + ' ' + this.userInfo.phone;
      }
      window.open(process.env.VUE_APP_KFILE_URL +
          encodeURIComponent(base64.encode(item.fileUrl)) + '&watermarkTxt=' + encodeURIComponent(getAesString(watermarkTxt,
              generateRandomStr(16), generateRandomStr(16))))
    },
    deleteImg(item, index) {
      let ossFilePaths = []
      ossFilePaths.push(item.ossFilePath)
      handleDeleteFile(ossFilePaths).then((res) => {
        if (res.code == 200) {
          this.imageFile.splice(index, 1);
          this.params.files.splice(index, 1);
          this.fileList.splice(index, 1);
          this.$message.success('删除成功');
          this.isDelete = true;
        }
      })
    },
    handleSubmit() {
      let that = this
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          that.params.contentId = this.info.contentId;
          if (that.info.type === '3') {
            if (that.imageFile.length === 0) {
              that.$message.warning('请上传文件')
            } else {
              let fileArr = JSON.parse(JSON.stringify(this.imageFile));
              this.params.files = fileArr.map((item, index) => {
                let obj = item;
                obj.sort = index;
                return obj
              })
              signContent(this.params).then(res => {
                if (res.code === 200) {
                  this.$message({
                    type: "success",
                    message: "签收成功"
                  })
                  this.showDialog = false;
                  this.$emit('success')
                }
              }).finally(() => {
                this.showDialog = false;
              })
            }
          } else {
            signContent(this.params).then(res => {
              if (res.code === 200) {
                this.$message({
                  type: "success",
                  message: "签收成功"
                })
                this.showDialog = false;
                this.$emit('success')
              }
            }).finally(() => {
              this.showDialog = false;
            })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.closeIcon {
  font-size: 20px;
  cursor: pointer;
  opacity: .8;
  color: rgb(112, 112, 112);
  top: 5px;
}

::v-deep {
  .el-dialog {
    border-radius: 10px !important;
  }
}

::v-deep {
  .el-dialog__body {
    padding: 0 20px 20px 20px !important;
  }
}

.cancel {
  background: #F0F0F0 !important;
  color: #333333 !important;
  border-color: #F0F0F0 !important;
}

.cancel:active {
  opacity: .7;
}

.pass {
  background: #1B77F5;
  color: #fff !important;
  border-color: #fff !important;
}

.pass:active {
  opacity: .7;
}

.label-class {
  text-align: right;
  float: left;
  font-size: 14px;
  color: #606266;
  padding: 0 12px 0 12px;
}

.upload_btn {
  background: #F0F0F0;
  color: #666 !important;
  border-color: #F0F0F0 !important;
}

.upload_btn:active {
  opacity: .7;
}


::v-deep .el-textarea__inner {
  font-family: "Microsoft YaHei" !important;
}
</style>
