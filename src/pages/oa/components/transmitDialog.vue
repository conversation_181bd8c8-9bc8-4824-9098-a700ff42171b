<template>
  <el-dialog class="reset-dialog"
             :visible.sync="showDialog"
             append-to-body
             title="提示"
             :width="'470px'"
             @open="handleOpen"
             @close="dialogClosed">
    <!--    <template slot="title">-->
    <!--      <div class="flex_alAndJsCenter">-->
    <!--        <img src="@/pages/images/img2/tips.png">&nbsp;-->
    <!--        <div class="flex_alAndJsCenter">提示</div>-->
    <!--      </div>-->
    <!--    </template>-->
    <div style="text-align: center;margin:10px 0;">
      <div style="font-size: 14px;color:#FC5353;margin-bottom: 10px;">{{
          "注：若所选择的人员在您转发之前全部都接收过此公文，则在转发之后，此公文不会出现在您的" + '"' + "我发起的" + '"' + "菜单列表中"
        }}
      </div>
      <div style="font-size: 17px;">{{ tipTitle }}</div>
    </div>
    <div style="text-align: center">
      <el-button class="cancel" @click="dialogClosed">取消</el-button>&nbsp;&nbsp;
      <el-button class="pass" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {putContent, revokeOa} from "@/api/OA";

export default {
  props: {
    tipTitle: {
      type: String,
      default: ''
    },
    auditType: {
      type: Number,
      default: 0
    },
    info: {
      type: Object,
      default: function () {
        return {}
      }
    },
    warningTip: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showDialog: false,
      type: 0,
      data: {}
    }
  },
  methods: {
    handleOpen() {
      this.data = this.info
      this.type = this.auditType
    },
    dialogClosed() {
      this.data = {}
      this.type = 0
      this.showDialog = false;
    },
    handleSubmit() {
      this.showDialog = false;
      this.$emit('transmitSuccess')
    }
  }
}
</script>
<style lang="scss" scoped>
.reset-dialog {
  font-size: 16px;

  .contents {
    padding: 10px 30px 30px 30px;
    font-size: 18px;
    font-weight: bold;
  }

  ::v-deep.el-dialog {
    border-radius: 5px !important;
  }

  ::v-deep.el-dialog__body {
    padding: 10px 20px 20px 20px !important;
  }

  .cancel {
    background: #F0F0F0 !important;
    color: #333333 !important;
    border-color: #F0F0F0 !important;
  }

  .pass {
    background: linear-gradient(90deg, #64A3F8 0%, #1B77F5 100%);
    color: #fff !important;
    border-color: #fff !important;
  }
}
</style>
