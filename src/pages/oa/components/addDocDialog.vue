<template>
  <el-dialog class="reset_dialog"
             :visible.sync="showDialog"
             append-to-body
             :width="'1000px'"
             :show-close="false"
             :close-on-click-modal="false"
             @open="handleOpen">
    <template slot="title">
      <div class="flex_c">
        <div></div>
        <div class="flex_center">
          <img style="width: 30px;height:30px;"
               src="@/pages/images/img2/add.png">&nbsp;
          <div class="fs_18 fs_bold c_title">新建公文</div>
        </div>
        <div class="pointer" @click="goBack">
          <i class="el-icon-close fs_22 c_999"></i>
        </div>
      </div>
    </template>
    <div class="doc-container" v-loading="vLoadingSubmit">
      <div class="m_t_20">
        <el-form :model="listQuery" label-width="auto"
                 inline :rules="rules" label-position="right" ref="formData">
          <el-form-item label="标题：" prop="noticeTitle" style="width: 100%;">
            <el-input placeholder="请输入标题" style="width: 800px"
                      v-model="listQuery.noticeTitle"></el-input>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item label="上传附件：" style="width: 100%;">
            <div>
              <el-upload class="upload-demo"
                         action="https://jsonplaceholder.typicode.com/posts/"
                         accept=""
                         ref="upload"
                         :auto-upload="false"
                         :on-change="selectChange"
                         :show-file-list="false"
                         :before-upload="beforeUpload"
                         :http-request="httpRequest"
                         :on-success="success"
                         :on-exceed="handleExceed"
                         multiple
                         :limit="10"
                         :file-list="fileList">
                <el-button class="upload_btn">
                  <span>点击上传</span>
                </el-button>
              </el-upload>
              <!--<span style="color:#F35C5B ">压缩包请上传.zip格式</span>-->
            </div>
            <span class="c_999 fs_14" style="color:#F35C5B ">
              上传要求：最多上传10个文件，压缩包文件上传.zip格式。
            </span>
            <div v-if="imageFile.length!==0">
              <span class="c_title">提示：上传的文件可上下拖动排序</span>
              <draggable
                  :list="imageFile"
                  animation="500"
                  force-fallback="true"
                  class="m_t_10">
                <div v-for="(item,index) in imageFile" :key="index" class="m_t_5 m_b_5">
                  <div class="flex_c" style="width:800px;user-select: none">
                    <div class="flex_center" style="cursor: move;width: 90%">
                      <img style="width:35px;height:35px" :src="item.url">&nbsp;
                      <div class="fs_14" style="width:calc(100%- 50px);line-height: 16px">{{ item.fileName }}</div>
                    </div>
                    <div class="fs_14 flex_c" style="color:#999;white-space: nowrap;width: 10%">
                      <div class="pointer" @click="viewFile(item)">预览</div>
                      <div class="pointer" @click="deleteImg(item,index)">删除</div>
                    </div>
                  </div>
                </div>
              </draggable>
            </div>

          </el-form-item>
          <el-divider></el-divider>
          <el-form-item style="width: 100%;" prop="urgentLevel" label="紧急程度：">
            <div class="flex_center">
              <!--              <div class="urgent-radio m_r_10" v-for="(dict,index) in dict.type['notice_urgent_level']"-->
              <!--                   :key="dict.value"-->
              <!--                   :class="dictTypeStatus(dict.value)" @click="changeRadio(index)">-->
              <!--                <div :class="radio_rounds(index)" class="round"></div>&nbsp;&nbsp;-->
              <!--                <div :class="radio_color(index)">{{ dict.label }}</div>-->
              <!--                <div :class="radio_triangle()" class="triangle  flex_alAndJsCenter">-->
              <!--                  <i class="el-icon-check c_fff right_icon"></i>-->
              <!--                </div>-->
              <!--              </div>-->
              <div class="urgent-radio flex_alAndJsCenter" :class="listQuery.urgentLevel=='0'?'radio_active':''"
                   @click="changeRadio(0)">
                <div style="background:#2A80F6;" class="round"></div>&nbsp;&nbsp;
                <div style="color:#2A80F6">一般</div>
                <div v-if="listQuery.urgentLevel=='0'" class="triangle active_triangle
                 flex_alAndJsCenter">
                  <i class="el-icon-check c_fff right_icon"></i>
                </div>
              </div>&nbsp;&nbsp;&nbsp;&nbsp;
              <div class="urgent-radio  flex_alAndJsCenter"
                   :class="listQuery.urgentLevel=='1'?'urgent_active':''"
                   @click="changeRadio(1)">
                <div class="round" style="background:#FF8C1C;"></div>&nbsp;&nbsp;
                <div style="color:#FF8C1C">紧急</div>
                <div v-if="listQuery.urgentLevel=='1'" class="triangle urgent_triangle
                flex_alAndJsCenter">
                  <i class="el-icon-check c_fff right_icon"></i>
                </div>
              </div>&nbsp;&nbsp;&nbsp;&nbsp;
              <div class="urgent-radio flex_alAndJsCenter"
                   :class="listQuery.urgentLevel=='2'?'extra_active':''"
                   @click="changeRadio(2)">
                <div class="round" style="background:#FC5353;"></div>&nbsp;&nbsp;
                <div style="color:#FC5353">特急</div>
                <div v-if="listQuery.urgentLevel=='2'" class="triangle extra_triangle
                 flex_alAndJsCenter">
                  <i class="el-icon-check c_fff right_icon"></i>
                </div>
              </div>
            </div>
          </el-form-item>
          <!--          <el-divider></el-divider>-->
          <el-form-item v-if="false" style="width: 100%;"
                        prop="autoRemind"
                        label="自动提醒：">
            <el-radio-group v-model="listQuery.autoRemind">
              <el-radio label="0">不提醒</el-radio>
              <el-radio label="1">自动提醒</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item style="width: 100%;" prop="noticeType" label="公文类型：">
            <el-select v-model="listQuery.noticeType"
                       style="width:100%"
                       clearable>
              <el-option v-for="item in dict.type['notice_type']"
                         :label="item.label"
                         :value="item.value"
                         :key="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item style="width:100%" prop="type" label="签收后的动作：">
            <el-radio-group v-model="listQuery.type">
              <el-radio label="0" value="0">默认</el-radio>
              <el-radio label="3" value="3">材料收集</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-divider></el-divider>
          <!--          <el-divider></el-divider>-->
          <el-form-item style="width: 100%;"
                        prop="selectPeoples"
                        label="接收单位：">
            <el-select v-model="listQuery.selectPeoples"
                       @click.native="showSelect"
                       clearable
                       @clear="selectClear"
                       style="width:800px">
              <el-option :label="userInfos" :value="listQuery.selectPeoples"></el-option>
            </el-select>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item style="width: 100%;"
                        label="抄送人：">
            <el-select v-model="listQuery.copyName"
                       @click.native="showSelect2"
                       clearable
                       @clear="selectClear2"
                       style="width:800px">
              <el-option :label="userInfo2" :value="listQuery.copyName"></el-option>
            </el-select>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item label="文号：" style="width: 100%;">
            <div class="flex aligin_center">
              <div>
                <el-switch class="define-switch"
                           v-model="showNum"
                           :active-value="true"
                           :inactive-value="false"
                           active-text="开"
                           inactive-text="关"
                           active-color="#3888F7"
                           inactive-color="#666666"
                           @change="changeSwitch()"
                ></el-switch>
              </div>
              <el-select v-if="showNum" class="m_l_10"
                         v-model="listQuery.numberAgency"
                         @change="changeBeforeNo"
              >
                <el-option v-for="item in beforeList"
                           :label="item.before"
                           :value="item.id"
                           :key="item.id"></el-option>
              </el-select>
              <div v-if="showNum&&listQuery.numberAgency"
                   class="m_l_20">
                <el-input-number v-model="listQuery.numberYear">
                </el-input-number>&nbsp;年
              </div>
              <div v-if="showNum&&listQuery.numberAgency">
                <el-input-number v-model="listQuery.numberRef">

                </el-input-number>&nbsp;号
              </div>
            </div>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item label="公文内容：" style="width:100%">
            <el-input placeholder="请输入公文内容" v-model="listQuery.noticeCon" style="width:800px" type="textarea"
                      :rows="4" clearable></el-input>
          </el-form-item>
          <!--          <el-divider></el-divider>-->
          <el-form-item v-if="false" style="width:100%" prop="type" label="签收后的动作：">
            <el-radio-group v-model="listQuery.type">
              <el-radio label="0">默认</el-radio>
              <el-radio label="1">活动报名</el-radio>
              <el-radio label="2">信息采集</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item style="width:100%" label="活动名称" v-if="listQuery.type==='1'" prop="activityName">
            <el-input placeholder="请输入活动名称" v-model="listQuery.activityName"></el-input>
          </el-form-item>
        </el-form>
        <div style="text-align: center;margin-top:10px">
          <el-button class="cancel" @click="goBack">关闭</el-button>&nbsp;&nbsp;
          <el-button class="submit" @click="submit">发送</el-button>
        </div>
      </div>
      <unit-tree ref="unitRef"
                 :is-clear="isClear"
                 :if-forward="isForward"
                 :title="title"
                 @confirm="unitSave"></unit-tree>
      <!--      抄送人选择-->
      <copy-people-select ref="unitRef2"
                          :is-clear="isClear2"
                          :title="title2"
                          @confirm="unitSave2"></copy-people-select>
    </div>
  </el-dialog>
</template>
<script>
import {addOa, beforeList, getDocNo, handleDeleteFile} from "@/api/OA";
import {before} from "lodash";
import {mapGetters} from 'vuex'
import axios from "axios";
import unitTree from "@/pages/oa/components/unitTree.vue";
import CopyPeopleSelect from "@/pages/oa/components/copyPeopleSelect.vue";
import draggable from 'vuedraggable'
import {aesDecrypt2, generateRandomStr, getAesString} from "@/pages/utils/tools";
import {getSessionToken} from "@/utils/local";

let base64 = require('js-base64').Base64
export default {
  components: {
    draggable,
    CopyPeopleSelect,
    unitTree
  },
  dicts: ['notice_type', 'notice_urgent_level'],
  data() {
    return {
      isForward: false,
      showDialog: false,
      icons: {
        pic: require('@/pages/images/img2/pic.png'),
        word: require('@/pages/images/img2/docx.png'),
        xlsx: require('@/pages/images/img2/xls.png'),
        ppt: require('@/pages/images/img2/ppt.png'),
        pdf: require('@/pages/images/img2/pdf.png'),
        video: require('@/pages/images/img2/vedio.png'),
        file: require('@/pages/images/img2/file.png'),
      },
      userInfos: '',
      fileList: [],
      submitData: [],
      listQuery: {
        advice: '',
        noticeTitle: '',
        noticeType: '',
        numberAgency: '',
        numberFlag: 0,
        numberRef: '',
        numberYear: '',
        status: '0',
        files: [],
        urgentLevel: '0',
        autoRemind: '0',
        receivePeoples: '',
        agencyId: '',
        deptId: '',
        agencyIds: [],
        stageIds: [],
        deptIds: [],
        selectPeoples: '',//避免触发validate
        noticeCon: '',//公文内容
        type: '0',//类型
        activityName: '',//活动名称
        copyName: '',
        copyPeoples: [],//抄送人
      },
      params: {
        noticeTitle: '',
        noticeCon: '',//公文内容
        urgentLevel: '',//紧急程度
        noticeType: '',
        agencyId: '',
        deptId: '',
        autoRemind: '',
        numberAgency: '',
        numberYear: '',
        advice: '',//办理意见
        type: '',//类型
        activityName: '',//活动名称
      },
      showNum: false,
      rules: {
        noticeTitle: [
          {
            required: true, message: '请输入标题', trigger: 'blur'
          }
        ],
        noticeType: [
          {
            required: true, message: '请选择公文类型', trigger: 'change'
          }
        ],
        selectPeoples: [
          {
            required: true, message: '请选择接收单位', trigger: 'change'
          }
        ],
        autoRemind: [
          {
            required: true, message: '自动提醒不能为空', trigger: 'change'
          }
        ],
        activityName: [
          {
            required: true, message: '活动名称不能为空', trigger: 'blur'
          }
        ],
        urgentLevel: [
          {
            required: true, message: '紧急程度不能为空', trigger: 'blur'
          }
        ]
        // type: [
        //   {
        //     required: true, message: '类型不能为空', trigger: 'change'
        //   }
        // ]
      },
      beforeList: [],
      title: '',
      imageFile: [],
      autoRemindList: [
        {
          label: '不提醒',
          value: '0'
        },
        {
          label: '自动提醒',
          value: '1'
        },
      ],
      userInfoArr: [],
      userInfoArr2: [],
      userInfo2: '',
      vLoadingSubmit: false,
      isClear: true,
      title2: '',
      isClear2: true,
      isDelete: false
    }
  },
  computed: {
    ...mapGetters(['role', 'userInfo', 'name']),
    banUpload() {
      return this.listQuery.files.length >= 10;
    },

  },
  methods: {
    before,
    handleOpen() {
      this.userInfos = '';
      this.imageFile = [];
      this.fileList = [];
      this.isDelete = false;
      this.isClear = true;
      this.showNum = false;
      this.listQuery = {
        advice: '',
        noticeTitle: '',
        noticeType: '',
        numberAgency: '',
        numberFlag: 0,
        numberRef: '',
        numberYear: '',
        status: '0',
        files: [],
        urgentLevel: '0',
        autoRemind: '0',
        receivePeoples: '',
        agencyId: '',
        deptId: '',
        agencyIds: [],
        stageIds: [],
        deptIds: [],
        selectPeoples: '',//避免触发validate
        noticeCon: '',//公文内容
        type: '0',//类型
        activityName: '',//活动名称
        copyName: '',
        copyPeoples: [],//抄送人
      }
    },
    dialogClosed() {
      this.listQuery = {
        advice: '',
        noticeTitle: '',
        noticeType: '',
        numberAgency: '',
        numberFlag: 0,
        numberRef: '',
        numberYear: '',
        status: '0',
        files: [],
        urgentLevel: '0',
        autoRemind: '0',
        receivePeoples: '',
        agencyId: '',
        deptId: '',
        agencyIds: [],
        stageIds: [],
        deptIds: [],
        selectPeoples: '',//避免触发validate
        noticeCon: '',//公文内容
        type: '0',//类型
        activityName: '',//活动名称
      }
      this.$refs.formData.resetFields()
      this.showDialog = false
    },
    changeRadio(val) {
      this.listQuery.urgentLevel = val
    },
    goBack() {
      if (this.imageFile.length !== 0) {
        // 取消并且上传过文件则删除所有
        let ossFilePaths = []
        this.imageFile.forEach(item => {
          ossFilePaths.push(item.ossFilePath)
        })
        handleDeleteFile(ossFilePaths).then(() => {

        })
      }
      this.dialogClosed()
    },
    uploadSuccess(info) {
      this.listQuery.files = info
      this.imageFile = info
    },
    success() {
      this.$forceUpdate()
    },
    deleteImg(item, index) {
      let ossFilePaths = []
      ossFilePaths.push(item.ossFilePath)
      // let params = {ossFilePath: item.fileUrl}
      handleDeleteFile(ossFilePaths).then((res) => {
        if (res.code == 200) {
          this.imageFile.splice(index, 1);
          this.fileList.splice(index, 1)
          this.listQuery.files.splice(index, 1);
          this.$message.success('删除成功')
          this.isDelete = true;
        }
      })

    },
    // 预览
    viewFile(item) {
      let watermarkTxt = ''
      if (this.role.role_type == 'stu') {
        watermarkTxt = this.name;
      } else {
        watermarkTxt = this.name + ' ' + this.userInfo.phone;
      }
      window.open(process.env.VUE_APP_KFILE_URL +
          encodeURIComponent(base64.encode(item.fileUrl)) + '&watermarkTxt=' + encodeURIComponent(getAesString(watermarkTxt,
              generateRandomStr(16), generateRandomStr(16))))
    },
    httpRequest(formItem) {
      let that = this
      let formData = new FormData()
      formData.append('file', formItem.raw)
      const loading = this.$loading({
        lock: true,
        text: '文件上传中',
        background: 'rgba(0,0,0,0.7)'
      })
      axios({
        headers: {
          Authorization: 'Bearer ' + getSessionToken(),
          'Content-Type': 'multipart/form-data',
          version: process.env.VUE_APP_VERSION
        },
        url: process.env.VUE_APP_BASE_API + '/minioFile/put-file',
        method: 'post',
        name: 'file',
        data: formData,
        timeout: 200000000
      }).then(res => {
        if (res.data && !res.data.code && (typeof res.data == 'string')) {
          let result = JSON.parse(aesDecrypt2(res.data))
          if (result.code == 200) {
            that.$message({
              message: '上传成功',
              type: 'success'
            })
            that.listQuery.files.push({
              fileName: result.data.originalFileName,
              fileUrl: result.data.ossFileUrl
            })
            that.imageFile.push({
              fileName: result.data.originalFileName,
              fileUrl: result.data.ossFileUrl,
              ossFilePath: result.data.ossFilePath
            })
            for (let i = 0; i < that.imageFile.length; i++) {
              that.imageFile[i].url = that.isAssetTypeAnImage(that.imageFile[i].fileName)
            }
            loading.close()
            that.$emit('uploadSuccess', that.submitData)
            this.success()
          } else {
            this.$message({
              message: result.msg,
              type: 'warning'
            })
          }
        } else {
          let result = res.data
          if (result.code == 200) {
            that.$message({
              message: '上传成功',
              type: 'success'
            })
            that.listQuery.files.push({
              fileName: result.data.originalFileName,
              fileUrl: result.data.ossFileUrl
            })
            that.imageFile.push({
              fileName: result.data.originalFileName,
              fileUrl: result.data.ossFileUrl,
              ossFilePath: result.data.ossFilePath
            })
            for (let i = 0; i < that.imageFile.length; i++) {
              that.imageFile[i].url = that.isAssetTypeAnImage(that.imageFile[i].fileName)
            }
            loading.close()
            that.$emit('uploadSuccess', that.submitData)
            this.success()
          } else {
            this.$message({
              message: result.msg,
              type: 'warning'
            })
          }
        }
        if (res.data.code === 200) {
          // that.$message({
          //   message: '上传成功',
          //   type: 'success'
          // })
          // that.listQuery.files.push({
          //   fileName: res.data.data.originalFileName,
          //   fileUrl: res.data.data.ossFileUrl
          // })
          // that.imageFile.push({
          //   fileName: res.data.data.originalFileName,
          //   fileUrl: res.data.data.ossFileUrl,
          //   ossFilePath: res.data.data.ossFilePath
          // })
          // for (let i = 0; i < that.imageFile.length; i++) {
          //   that.imageFile[i].url = that.isAssetTypeAnImage(that.imageFile[i].fileName)
          // }
          // loading.close()
          // that.$emit('uploadSuccess', that.submitData)
          // this.success()
        }
      })
    },
    selectChange(file, fileList) {
      // const fileType = file.name.substring(file.name.lastIndexOf('.') + 1);
      // const banTypeList = ['doc', 'docx', 'pdf', 'xls', 'xlsx', 'zip', 'jpg', 'png', 'jpeg', 'ppt', 'pptx'];
      // console.log(banTypeList.indexOf(fileType.toLowerCase()))
      // if (banTypeList.indexOf(fileType.toLowerCase()) === -1) {
      //   this.$message.warning('上传的文件中存在格式有误的文件，请注意要求！');
      //   return
      // } else {
      var upload_file = document.getElementsByClassName('upload-demo');
      // 获取选择的文件数量
      let uploadNum = 0;
      if (upload_file && upload_file.length > 0) {
        var upload = upload_file[0].getElementsByTagName('input');
        if (upload && upload.length > 0 && upload[0].files && upload[0].files.length > 0) {
          uploadNum = upload[0].files.length;
        }
      }
      if (this.isDelete && (this.listQuery.files.length + uploadNum >= 10)) {
        this.$selfMessage.warning('最多上传10个文件');
      } else {
        this.httpRequest(file);
      }
      // }
    },
    handleExceed() {
      this.$message.warning('最多上传10个文件')
    },
    beforeUpload(file) {
    },
    //判断文件类型
    isAssetTypeAnImage(url) {
      let i = url.lastIndexOf('.'),
          ext = '';
      if (i > -1) {
        ext = url.substring(i + 1);
      }
      //图片
      if (
          [
            'png',
            'jpg',
            'jpeg',
            'bmp',
            'gif',
            'webp',
            'psd',
            'svg',
            'tiff',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.pic;
      } else if (['doc', 'docx'].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.word;
      } else if (['xls', 'xlsx',].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.xlsx;
      } else if (['pdf',].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.pdf;
      } else if (['ppt', 'pptx'].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.ppt;
      } else if (
          [
            'mp4',
            'flv',
            'm3u8',
            'rtmp',
            'hls',
            'rtsp',
            'mp3',
            'flac',
            'm4a',
            'ogg',
            'ape',
            'amr',
            'wma',
            'wav',
            'aac',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.video;
      } else {
        return this.icons.file;
      }
    },
    changeSwitch() {
      this.listQuery.numberFlag = this.showNum ? 1 : 0;
      beforeList().then(res => {
        this.beforeList = res.rows
      })
    },
    // 文号选择触发
    async changeBeforeNo() {
      this.listQuery.numberYear = new Date().getFullYear();
      const result = await getDocNo({
        beforeId: this.listQuery.numberAgency,
        year: this.listQuery.numberYear,
      });
      this.listQuery.numberRef = result.data;
    },
    // 接收单位
    showSelect() {
      this.title = '接收单位选择';
      if (this.listQuery.receivePeoples.length !== 0) {
        this.isClear = false;
      } else {
        this.isClear = true;
      }
      this.$refs.unitRef.showDialog = true;
    },
    showSelect2() {
      this.title2 = '抄送人选择';
      if (this.listQuery.copyPeoples.length !== 0) {
        this.isClear2 = false;
      } else {
        this.isClear2 = true;
      }
      this.$refs.unitRef2.showDialog = true;
    },
    selectClear2() {
      this.userInfo2 = '';
      this.listQuery.copyPeoples = [];
    },
    selectClear() {
      this.userInfos = '';
      this.listQuery.receivePeoples = '';
      this.listQuery.agencyIds = [];
      this.listQuery.deptIds = [];
    },
    // 抄送人选择回执
    unitSave2(data) {
      this.listQuery.copyPeoples = []
      this.userInfoArr2 = []
      this.userInfo2 = ''
      let userList = _.clone(data)
      this.listQuery.copyName = ' '
      if (userList.receivePeoples.length !== 0) {
        this.userInfoArr2 = this.userInfoArr2.concat(userList.receivePeoples.map(item => {
          return {
            label: item.label || item.userName,
            deptId: item.deptId,
            signBy: item.userId,
            agencyId: item.agencyId,
          };
        }))
        this.userInfoArr2 = Array.from(new Set(this.userInfoArr2))
        this.userInfo2 += this.userInfoArr2.map(item => {
          return item.label
        }).toString() + ','

        this.listQuery.copyPeoples =
            this.listQuery.copyPeoples.concat(userList.receivePeoples.map(item => {
              return {
                signBy: item.userId,
                deptId: item.deptId,
                agencyId: item.agencyId,
                stageId: item.stageId
              };
            }))
        this.listQuery.copyPeoples = Array.from(new Set(this.listQuery.copyPeoples))
      }
    },
    // 接收人选择回执
    unitSave(data) {
      this.listQuery.receivePeoples = []
      this.userInfoArr = []
      this.userInfos = ''
      let userList = _.clone(data)
      this.listQuery.selectPeoples = ' '
      if (userList.agencyIds.length !== 0) {
        this.userInfos = userList.agencyIds.map(item => {
          return item.label || item.userName
        }).toString() + ','
        this.listQuery.agencyIds = userList.agencyIds.map(item => {
          return item.id;
        })
      }
      if (userList.deptIds.length !== 0) {
        this.userInfos += userList.deptIds.map(item => {
          return item.label || item.userName
        }).toString() + ','
        this.listQuery.deptIds = userList.deptIds.map(item => {
          return item.id;
        })
      }
      if (userList.receivePeoples.length !== 0) {
        this.userInfoArr = this.userInfoArr.concat(userList.receivePeoples.map(item => {
          return {
            label: item.label || item.userName,
            deptId: item.deptId || '',
            signBy: item.userId,
            agencyId: item.agencyId,
          };
        }))
        this.userInfoArr = Array.from(new Set(this.userInfoArr))
        this.userInfos += this.userInfoArr.map(item => {
          return item.label || item.userName
        }).toString() + ','

        this.listQuery.receivePeoples =
            this.listQuery.receivePeoples.concat(userList.receivePeoples.map(item => {
              return {
                signBy: item.userId,
                deptId: item.deptId || '',
                agencyId: item.agencyId,
                stageId: item.stageId
              };
            }))
        this.listQuery.receivePeoples = Array.from(new Set(this.listQuery.receivePeoples))
      }
    },
    submit() {
      let that = this;
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.listQuery.deptId = this.role.dept_id ? this.role.dept_id : '';
          this.listQuery.agencyId = this.role.agency_id;
          if (this.listQuery.numberFlag === 1 && (this.listQuery.numberRef === '' || this.listQuery.numberYear === '')) {
            this.$message({
              message: '请完善文号信息选择',
              type: 'error'
            })
            return
          }
          let fileArr = JSON.parse(JSON.stringify(this.imageFile));
          this.listQuery.files = fileArr.map((item, index) => {
            let obj = {}
            obj.fileName = item.fileName;
            obj.fileUrl = item.fileUrl;
            obj.sort = index;
            return obj
          })
          if (that.listQuery.files.length !== 0 || that.listQuery.noticeCon !== '') {
            if (this.showNum == false) {
              this.listQuery.numberRef = '';
              this.listQuery.numberYear = '';
            }
            let params = this.listQuery
            delete params.selectPeoples;
            delete params.copyName;
            // if (this.showNum && this.listQuery.numberAgency) {
            this.vLoadingSubmit = true
            addOa(params).then((res) => {
              if (res.code === 200) {
                this.$message({
                  message: '发送成功',
                  type: 'success'
                })
                setTimeout(() => {
                  this.showDialog = false;
                  this.$emit('success');
                }, 1000);
              }
            }).finally(() => {
              this.vLoadingSubmit = false
            });
          } else {
            this.$message.error('请填写公文内容或者上传附件')
          }

          // } else {
          //   this.$message({
          //     message: '请选择文号',
          //     type: 'warning'
          //   })
          // }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.reset_dialog {
  ::v-deep.el-dialog {
    border-radius: 10px;
  }

  ::v-deep.el-dialog__body {
    padding: 10px 20px 20px 20px;
  }
}

.doc-container {
  font-size: 16px;
  padding: 10px 20px 20px 20px;
  border-radius: 5px;
  background: #fff;

  .btn-back {
    background: #fff !important;
    border-color: #3888F7 !important;
    color: #3888F7 !important;
    font-family: "微软雅黑", Arial;
  }

  .btn-submit {
    background: #3888F7 !important;
    border-color: #3888F7 !important;
    color: #fff !important;
    font-family: "微软雅黑", Arial;
  }
}

//开关样式
::v-deep.define-switch {
  .el-switch__core {
    width: 48px !important;
  }

  .el-switch__label--left {
    position: absolute;
    left: 24px;
    color: #fff;
    z-index: -1111;
    cursor: default;
    user-select: none;
  }

  .el-switch__label--right {
    position: absolute;
    right: 24px;
    color: #fff;
    z-index: -1111;
    cursor: default;
    user-select: none;
  }

  .el-switch__label--right.is-active {
    z-index: 1111;
    color: #fff !important;
  }

  .el-switch__label--left.is-active {
    z-index: 1111;
    color: #9c9c9c !important;
  }
}

.upload_btn {
  background: #F0F0F0;
  color: #666 !important;
  border-color: #F0F0F0 !important;

  .el-button {
  }
}

.upload_btn:active {
  opacity: .7;
}

.upload_btn:hover {
  color: #4C94F7 !important;
}

.cancel {
  background: #F0F0F0 !important;
  color: #333333 !important;
  border-color: #F0F0F0 !important;
}

::v-deep.el-divider--horizontal {
  margin: 15px 0 !important;
}

.doc-container {
  ::v-deep.el-form-item {
    margin-bottom: 5px !important;
  }
}

.urgent-radio {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90px;
  height: 38px;
  border: 1px solid #CCCCCC;
  border-radius: 5px;
  cursor: pointer;

  .round {
    width: 10px;
    height: 10px;
    border-radius: 100px
  }
}

.radio_active {
  border-color: #4C94F7;
  position: relative;
}


.triangle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 18px;

  .right_icon {
    font-weight: bold;
    margin-left: 7px;
    font-size: 10px;
    margin-top: 5px;
  }
}

.active_triangle {
  background: linear-gradient(315deg, #4C94F7, #4C94F7 50%, transparent 50%, transparent 100%) no-repeat;
  background-size: 100% 100%;
}

.urgent_active {
  border-color: rgb(255, 140, 28);
  position: relative;
}

.urgent_triangle {
  background: linear-gradient(315deg, rgb(255, 140, 28), rgb(255, 140, 28) 50%, transparent 50%, transparent 100%) no-repeat;
  background-size: 100% 100%;
}

.extra_active {
  position: relative;
  border-color: rgb(252, 83, 83);
}

.extra_triangle {
  background: linear-gradient(315deg, rgb(252, 83, 83), rgb(252, 83, 83) 50%, transparent 50%, transparent 100%) no-repeat;
  background-size: 100% 100%;
}

.normal_round {
  background: #2A80F6;
}

.normal_color {
  color: #2A80F6;
}

.urgent_round {
  background: #FF8C1C;
}

.urgent_color {
  color: #FF8C1C;
}

.extra_round {
  background: #FC5353;
}

.extra_color {
  color: #FC5353;
}

::v-deep .el-textarea__inner {
  font-family: "Microsoft YaHei" !important;
}
</style>
