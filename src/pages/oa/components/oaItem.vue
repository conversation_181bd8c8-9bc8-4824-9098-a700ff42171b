<template>
  <div class="item-container" :class="(btnType===0&&info.consultFlag=='1')?'c_999':''">
    <div class="flex_c" style="height:30px;position: relative">
      <!--左上侧标签-->
      <!--我收到的-->
      <div v-if="btnType===0">
        <div v-if="info.status==='1'" class="flex_center fs_12 p_l_20">
          <div :style="{'background':colorStyle}"
               style="width: 10px;height:10px;border-radius: 100px"></div>&nbsp;
          <div :style="{'color':colorStyle}">{{ tipName }}</div>&nbsp;&nbsp;&nbsp;
          <div v-if="info.signFlag==='1'&&info.nodeType!=='2'" class="flex_center">
            <div class="is-tag-icon bg_b" style="position: relative">
              <i class="el-icon-check c_fff fs_12" style="position: absolute"></i>
            </div>&nbsp;
            <div class="c_title">已签收</div>
          </div>
          <div v-if="info.signFlag==='0'&&info.nodeType!=='2'" class="flex_center">
            <div class="bg_fff tag-icon-un-agree"></div>&nbsp;
            <div class="c_999">未签收</div>
          </div>
        </div>
      </div>
      <!--我发起的和我审核的-->
      <div v-if="btnType===1||btnType===2" class="flex_center">
        <div v-if="info.status==='1'&&btnType===1" class="flex_center fs_12 p_l_20">
          <div :style="{'background':colorStyle}"
               style="width: 10px;height:10px;border-radius: 100px"></div>&nbsp;
          <div :style="{'color':colorStyle}">
            <dict-tag
                :options="dict.type.notice_urgent_level"
                :value="info.urgentLevel"/>
          </div>
        </div>
        <!--        <div v-if="info.status==='2'"-->
        <!--             class="flex_center fs_12 p_l_10">-->
        <!--          <div class="bg_revoke tag-icon"></div>&nbsp;-->
        <!--          <div class="c_revoke">已驳回</div>&nbsp;&nbsp;&nbsp;-->
        <!--        </div>-->
        <!--        <div v-if="info.status==='1'" class="flex_center fs_12 p_l_10">-->
        <!--          <div class="tag-icon bg_b"></div>&nbsp;-->
        <!--          <div class="c_title">已通过</div>&nbsp;&nbsp;&nbsp;-->
        <!--        </div>-->
        <!--        <div v-if="info.status==='0'" class="flex_center fs_12 p_l_10">-->
        <!--          <div class="tag-icon bg_999"></div>&nbsp;-->
        <!--          <div class="c_999">待审核</div>&nbsp;&nbsp;&nbsp;-->
        <!--        </div>-->
        <div v-if="info.status==='3'" class="flex_center fs_12 p_l_10">
          <div class="tag-icon bg_999"></div>&nbsp;
          <div class="c_999">已撤销</div>&nbsp;&nbsp;&nbsp;
        </div>
      </div>
      <!--  -->
      <div class="tag_box" v-if="info.noticeType">
        <dict-tag
            :options="dict.type.notice_type"
            :value="info.noticeType"/>
      </div>
    </div>
    <div class="card-box pointer"
         @click="btnClick">
      <div class="clearfix">
        <div style="width:90%" class="fs_18 fw_700">
          <tooltip style="width:100%" :title="info.noticeTitle"></tooltip>
        </div>
        <div class="title-class flex_center">
          <div>发文单位:&nbsp;</div>
          <div style="width:65%" class="pointer">
            <tooltip style="width:100%"
                     :title="info.agencyName+'/'+info.createBy"></tooltip>
          </div>
        </div>
        <div class="title-class">发文时间: {{ dayjs()(info.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
      </div>
    </div>
    <!--      我收到的-->
    <div v-if="btnType===0" class="btn-click">
      <!--      抄送仅查看-->
      <div v-if="info.nodeType=='2'">
        <div @click="btnClick" class="br_b_l_6 w_100 pointer sign_box"
             style="background: #DEECFE;color:#1B77F5">查看
        </div>
      </div>
      <div v-if="info.nodeType!=='2'">
        <div v-if="info.signFlag === '0' " class="flex_center w_100">
          <div class="w_50 bg_linear br_l_6  pointer sign_box"
               @click="handleSign"
          >签收
          </div>
          <div class="br_r_6 w_50 pointer sign_box"
               style="background: #DEECFE;color:#1B77F5" @click="transmit">转发
          </div>
        </div>
        <div v-if="info.signFlag === '1' " @click="transmit"
             class="flex_alAndJsCenter br_b_l_6 bg_linear w_100 pointer sign_box">
          <div>转发</div>
        </div>
        <!--        <div v-if="info.signFlag === '1' " @click="transmit"-->
        <!--             class="flex_alAndJsCenter br_b_l_6  w_100 pointer" style="background: #DEECFE;color:#1B77F5">-->
        <!--          <div>转发</div>-->
        <!--        </div>-->
        <!--        <div v-if="info.signFlag === '0' " class="br_l_6 w_100 bg_linear pointer"-->
        <!--             @click="handleSign"-->
        <!--        >签收-->
        <!--        </div>-->

      </div>

    </div>
    <!--    我发起的-->
    <div v-if="btnType===1" class="btn-click">
      <div v-if="info.status === '1' " class="flex_center w_100">
        <div class="br_l_6 w_50 pointer bg_revoke  c_revoke revoke_box"
             @click="handleRevoke"
        >撤销
        </div>
        <div class="br_r_6 w_50 pointer sign_box"
             style="background: #DEECFE;color:#1B77F5" @click="transmit">转发
        </div>
      </div>
      <div v-if="info.status === '0'||info.status==='2'" @click="handleRevoke" style="color:#FC5353"
           class="flex_alAndJsCenter br_b_l_6 bg_revoke w_100 pointer revoke_box">
        <div>撤销</div>
      </div>
      <div v-if="info.status === '3'" @click="btnClick" class="br_b_l_6 w_100 pointer look_box"
           style="background: #DEECFE;color:#1B77F5">查看
      </div>
    </div>

    <!--    我审核的-->
    <div v-if="btnType===2" class="btn-click">
      <div v-if="info.status === '0' " class="flex_center w_100">
        <div class="br_b_l_6 w_100 pointer bg_linear c_fff" @click="handleSf">审核
        </div>
      </div>
      <div v-if="info.status !== '0'" style="background: #DEECFE;color:#1B77F5" @click="btnClick"
           class="flex_alAndJsCenter br_b_l_6  w_100 pointer">
        <div>查看</div>
      </div>
    </div>

    <public-dialog ref="publicDialogRef"
                   :info="publicInfo"
                   :audit-type="publicType"
                   :tipTitle="pubTitle"
                   @submit="pubSubmit"></public-dialog>
    <!--转发-->
    <unit-tree ref="unitRef"
               :is-clear="true"
               :if-forward="isForward"
               :title="title"
               :is-refresh="isRefresh"
               @closeDialog="closeDialog"
               @confirm="unitSave"></unit-tree>
    <!--    签收-->
    <sign-dialog ref="signDialogRef"
                 :info="signInfo"
                 @forward="signForward"
                 @success="success"></sign-dialog>
    <audit-type ref="auditDialogRef"
                @success="success"
                :title="title" :info="dialogInfo"></audit-type>

    <transmit-dialog ref="transmitRef" :tip-title="tipTitle" :warningTip="warningTip"
                     @transmitSuccess="handleZf2"></transmit-dialog>
  </div>
</template>
<script>
import dayjs from "dayjs";
import Tooltip from "@/components/Tooltip.vue";
import MyTooltip from "@/components/Tooltip.vue";
import PublicDialog from "@/pages/oa/components/publicDialog.vue";
import signDialog from "@/pages/oa/components/signDialog.vue";
import UnitTree from "@/pages/oa/components/unitTree.vue";
import AuditType from "@/pages/oa/components/auditType.vue";
import AuditDialog from "@/pages/oa/component/auditDialog.vue";
import {forward} from "@/api/OA";
import TransmitDialog from "@/pages/oa/components/transmitDialog.vue";

export default {
  components: {
    MyTooltip,
    TransmitDialog,
    AuditDialog,
    AuditType,
    UnitTree,
    signDialog,
    PublicDialog,
    Tooltip
  },
  dicts: ['notice_type', 'notice_urgent_level'],
  props: {
    btnType: {
      type: Number,
      default: 0
    },
    info: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },

  data() {
    return {
      publicInfo: {},
      publicType: 0,
      pubTitle: '',
      signInfo: {},
      title: '',
      dialogInfo: {},
      userInfo: '',
      listQuery: {
        receivePeoples: [],
        agencyIds: [],
        deptIds: []
      },
      warningTip: '',
      tipTitle: '',
      isRefresh: true,
      isForward: false
    }
  },
  computed: {
    colorStyle() {
      if (this.info.urgentLevel === '0') {
        return '#2A80F6'
      } else if (this.info.urgentLevel === '1') {
        return '#FF8C1C'
      } else if (this.info.urgentLevel === '2') {
        return '#FC5353'
      } else {
        return ''
      }
    },
    tipName() {
      if (this.info.urgentLevel === '0') {
        return '正常'
      } else if (this.info.urgentLevel === '1') {
        return '紧急'
      } else if (this.info.urgentLevel === '2') {
        return '特急'
      } else {
        return ''
      }
    }
  },
  methods: {
    dayjs() {
      return dayjs
    },
    // 转发
    transmit() {
      this.title = '接收单位选择';
      this.isForward = true;
      this.isRefresh = true;
      this.$refs.unitRef.showDialog = true
    },
    closeDialog() {
      this.$emit('loadSuccess')
    },
    // 转发确认
    unitSave(data) {
      this.listQuery.receivePeoples = []
      let userList = _.clone(data)
      let showLabel = ''
      if (userList.deptIds.length !== 0) {
        userList.deptIds.forEach((item, index) => {
          if (index === userList.deptIds.length - 1) {
            showLabel += item.label
          } else {
            showLabel += item.label + ','
          }
        })
      }
      if (userList.receivePeoples.length !== 0) {
        userList.receivePeoples.forEach((item, index) => {
          if (index === userList.receivePeoples.length - 1) {
            showLabel += item.label
          } else {
            showLabel += item.label + ','
          }
        })
        this.listQuery.receivePeoples = userList.receivePeoples.map(item => {
          return {
            signBy: item.userId,
            deptId: item.deptId || '',
            agencyId: item.agencyId,
            stageId: item.stageId || ''
          };
        })
      }
      this.userInfo = showLabel
      // this.$refs.transmitRef.showDialog = true;
      // this.tipTitle = '是否转发给' + '"' + this.userInfo + '"' + '?';
      this.handleZf()
    },
    handleZf2() {
      let that = this
      let params = {
        contentId: that.info.contentId,
        receivePeoples: that.listQuery.receivePeoples,
        deptIds: that.listQuery.deptIds
      }
      forward(params).then(res => {
        if (res.code === 200) {
          this.$message({
            message: '转发成功',
            type: 'success',
          });
          that.$confirm(res.msg, '提示', {
            cancelButtonText: '关闭',
            showConfirmButton: false,
            type: 'success',
          }).then(() => {
          }).finally(() => {
            this.pubSubmit()
          })
        }
      })

    },
    handleZf() {
      let that = this
      this.$confirm('是否转发给' + this.userInfo + '?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let params = {
          contentId: that.info.contentId,
          receivePeoples: that.listQuery.receivePeoples,
          deptIds: that.listQuery.deptIds
        }
        forward(params).then(res => {
          if (res.code === 200) {
            this.$message({
              message: '转发成功',
              type: 'success',
            });
          }
          this.$emit('loadSuccess');
          this.isRefresh = false;
          this.isForward = false;
          // this.$refs.transmitRef.showDialog = true;
          // this.warningTip = res.msg;
        })
      }).catch((action) => {
        if (action == 'cancel') {
          this.isForward = false;
          if (this.isRefresh) {
            this.$emit('loadSuccess')
            this.isRefresh = false;
          }
        }
      })
    },
    // 撤销确定
    pubSubmit() {
      this.$emit('loadSuccess')
    },
    // 签收成功
    success() {
      this.$emit('loadSuccess')
    },
    // 签收并转发
    signForward() {
      this.isRefresh = true;
      this.isForward = true;
      this.title = '接收单位选择';
      this.$refs.unitRef.showDialog = true
    },
    // 签收
    handleSign() {
      this.signInfo = this.info
      this.$refs.signDialogRef.showDialog = true
    },
    // 审核
    handleSf() {
      this.dialogInfo = this.info
      this.$refs.auditDialogRef.showDialog = true
    },
    // 撤销
    handleRevoke() {
      this.publicType = 1
      let infos = JSON.parse(JSON.stringify(this.info));
      infos.status = 3;
      this.pubTitle = '是否撤销公文' + "'" + this.info.noticeTitle + "'" + '？';
      this.publicInfo = infos;
      this.$refs.publicDialogRef.showDialog = true;
    },
    btnClick() {
      if (this.btnType === 0) {
        //   转发
        this.$router.push({
          path: '/oa/audit',
          query: {
            type: 0,
            id: this.info.contentId
          }
        })
      } else if (this.btnType === 1) {
        //   撤销
        this.$router.push({
          path: '/oa/audit',
          query: {
            type: 1,
            id: this.info.contentId
          }
        })

      } else if (this.btnType === 2) {
        //   审核
        this.$router.push({
          path: '/oa/audit',
          query: {
            type: 2,
            id: this.info.contentId
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.item-container {
  position: relative;
  width: 270px;
  //padding: 20px 10px 0 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  border-radius: 6px;
  background: #F7FAFF;

  .title-class {
    color: #666666;
    font-size: 12px;
    margin-top: 10px;
  }

  .btn-click {
    text-align: center;
    font-size: 15px;
    width: 270px;
    height: 40px;
    line-height: 40px;
    color: #fff;
    opacity: 1;
  }

  .card-box {
    padding: 5px 20px 10px 20px;
  }

  .tag_box {
    //position: absolute;
    //font-size: 14px;
    //color: #fff;
    //width: 67px;
    //height: 30px;
    //line-height: 28px;
    //top: 0;
    //right: -3px;
    //text-align: center;
    //background: url("@/pages/images/img2/status.png") no-repeat;
    //background-size: 100% 100%;

    position: absolute;
    min-width: 67px;
    padding: 0 5px;
    height: 30px;
    background-size: 100% 100%;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAH4AAABICAYAAAAwAEE4AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAR6SURBVHic7Z1NbhNBEIVfj8OvhJQDgOQjcAJETsAJAJMLEFaEFWQFEgvMBZKQk1jiIBjBAVgAAaKkWNiddM+8rpkhInHiV1I0XVWvp2p6XP68S0DNRjvf7yJceQrYfQBDA2AGAAZDgM0czEOz61zTiB3vTa6Jpq5P43RfqU62NgABNhewOuk9WZ0uvbbqa7Gm1mAW5qsOdXrUTvRTM0wMRx++vrk1QWIhddb3fr0zw0a/g7DTHVzbi+cPVK7Z2Df7wMLmV9ixBgCO2up0qO09Y+PZWvdZvzppLNE3zjKE8ZfXN5/Fdqq4WN/7vQPMXrpndnK7pFzSXENPktYM9TZyv3aB02/iWM/GOpenkvw8vdLu+Tp1ZjnbuLP5Yye6FQCs7/3cAGyUCQNfB4R5ICSJ0NjS2Erux/RM3ioITH/SW6j1SWukz1gqXojTcOH8MkmIyZO/8kkWzrdDnWgGjG5v/ngJAGG0Y6uDwe9PBqzWv0riRYzveI9UX4s1tWfCeKafHoY/a1U12B8BWAXQMonxs8gmPlEEtgt0Alqn2hMVP+npdNcn/d8nvu+3U/skptNdn3hehJ1l8RlKfQHDweGV+ytVqB70YZpHocVjfKgJFpXxrdJirjPjU6vwuDLDkCbF+M5NXRTGJ8LhCpC8eMKlPBAKE59zv5lh9+v46S6JavezLBFQj7Cb9Zl49mxei0aet74rMr55L94AO5NSHYN7vsOq+ySK8X1aXFTGR1vpC1ox3m+FBheN8QCqvhMlxncMLzTj6xMvxs+Wl5/xEOOJI8YTE+P9VmhQjM/XYnxMivH+PiqAGM/i5XYBiPHUEeOJifF+KzQoxudrMT4mxXh/HxVAjGfxcrsAxHjqiPHExHi/FRoU4/O1GB+TYry/jwogxrN4uV0AYjx1xHhiYrzfCg2K8flajI9JMd7fRwUQ41m83C4AMZ46YjwxMd5vhQbF+HwtxsekGO/vowKI8SxebheAGE8dMZ6YGO+3QoNifL4W42NSjPf3UQHEeBYvtwtAjKeOGE9MjPdboUExPl+L8TEpxvv7qABiPIuX2wUgxlNHjCcmxvut0KAYn6/F+JgU4/19VAAxnsXL7QIQ46kjxhMT4/1WaFCMz9difEyK8f4+KoAYz+LldgGI8dQR44mJ8X4rNCjG52sxPibFeH8fFUCMZ/FyuwDEeOqI8cTEeL8VGhTj87UYH5NivL+PCiDGs3i5XQBiPHWWgfEVDFMn3zD93zm/klueSvLzPEvGT2lGjO/c1EVkfGVmH469VsaXJn7RGB+vcYpOP/H/h/F5nycTfwaMPzrExDCfejF+tlwGxu8+uTFFCLuOJjMx3q/klqeS82I8gO2H17aA2ssX4zs3dSEZHxfbj649ATAGxPilYHzqbD+6/syObA0WdoH6r30xvk+Li874Hl8OsotqV++9fQnYq+gbbFw5etnltOlBtb+lF79EZsC3qjpcw+TVN734ZTKzrV+TF1Og9uNOdnnNYOODj8/H0V85z2ZkZ2Nm9vlgsP8+jf0F9QhP6EW0wvgAAAAASUVORK5CYII=');
    border-radius: 4px 4px 0px 4px;
    right: -3px;
    top: 0;
    display: flex;
    justify-content: center;
    //align-items: center;
    line-height: 28px;
    color: #ffffff;
    font-size: 12px;
    //background: linear-gradient(90deg, #64A3F8 0%, #1B77F5 100%);
    //border-radius: 0 4px 0 4px;
  }

  .tag {
    position: absolute;
    font-size: 14px;
    color: #fff;
    padding: 5px 15px;
    right: 0;
    top: 0;
    background: #6A94FF;
    border-radius: 0px 4px 4px 4px;
  }
}

.revoke_box:active {
  opacity: .7;
}

.look_box:active {
  opacity: .7;
}

.transmit_box:active {
  opacity: .7;
}

.sign_box:active {
  opacity: .7;
}
</style>
