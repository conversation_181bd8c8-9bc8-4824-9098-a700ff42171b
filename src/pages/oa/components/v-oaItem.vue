<template>
  <div :class="((info.status==='3'&&type=='1')||(type=='0'&&info.consultFlag=='1'))?'c_999':''">
    <div v-if="inx==0" class="top-bg"></div>
    <div class="item-container" :class="inx!==0?'top-border':''">
      <div class="item-top flex_c">
        <!-- 正常状态-->
        <div v-if="info.status!=='3'" style="width: 80%" class="flex pointer" @click="toDetail">
          <div :class="dictTagClass" style="margin-top: 5px;"
               class="dict--class">
            <dict-tag
                :options="dict.type.notice_urgent_level"
                :value="info.urgentLevel"/>
          </div>&nbsp;
          <div style="width: 90%" class="fs_18 fw_700">{{ info.noticeTitle }}
          </div>
        </div>
        <!--已撤销-->
        <div v-if="info.status==='3'" style="width: 80%" class="flex pointer" @click="toDetail">
          <div class="dict--class revoke--class" style="margin-top: 5px;">
            <dict-tag
                :options="dict.type.notice_urgent_level"
                :value="info.urgentLevel"/>
          </div>&nbsp;
          <div style="width: 90%" class="fs_18 fw_700">{{ info.noticeTitle }}</div>
        </div>
        <div v-if="info.status!=='3'&&info.nodeType!=='2'" style="width: 12%;"
             class="flex_center item-top-tip-class flex_end">
          <div v-if="info.type=='3'" class="flex_center">
            <div style="width: 13px;height:13px">
              <img style="width: 13px;height:13px" src="@/pages/assets/images/OA/file_icon.png">
            </div>&nbsp;
            <div class="c_title">材料收集</div>
          </div>
          <div v-if="info.signFlag==='1'&&info.nodeType!=='2'" class="flex_center m_l_5">
            <div class="is-tag-icon bg_b" style="position: relative">
              <i class="el-icon-check c_fff fs_12" style="position: absolute"></i>
            </div>&nbsp;
            <div class="c_title">已签收</div>
          </div>
          <div v-if="info.signFlag==='0'&&info.nodeType!=='2'" class="flex_center m_l_5">
            <div class="bg_fff tag-icon-un-agree"></div>&nbsp;
            <div class="c_999">未签收</div>
          </div>
        </div>
        <!--        抄送公文提示-->
        <div v-if="info.nodeType=='2'&&type=='0'" class="flex_center item-top-tip-class flex_end">
          <!--          <div class="c_999">抄送公文</div>-->
        </div>
        <!-- 已撤销-->
        <div v-if="info.status==='3'&&type=='1'" style="width: 12%;" class="flex_center item-top-tip-class flex_end">
          <div v-if="info.type=='3'" class="flex_center">
            <div style="width: 13px;height:13px">
              <img style="width: 13px;height:13px" src="@/pages/assets/images/OA/file_revoke.png">
            </div>&nbsp;
            <div class="c_999">材料收集</div>&nbsp;&nbsp;
          </div>
          <div class="c_999">已撤销</div>
        </div>

      </div>
      <div class="item-center-box" :class="info.status!=='3'?'c_666':'c_999'">
        <div class="item-center--left pointer" @click="toDetail">
          <div style="width: 50%;" class="item-center--left--tip flex_center">
            <div>发文单位：</div>
            <div style="width: 80%">
              <my-tooltip style="width: 100%"
                          :title="info.agencyName+'/'+info.createBy"></my-tooltip>
            </div>
          </div>
          <div style="width: 25%;" class="item-center--left--tip flex_center">
            <div>公文类型：</div>
            <div>
              <dict-tag
                  :options="dict.type.notice_type"
                  :value="info.noticeType"/>
            </div>
          </div>
          <div v-if="type=='1'" style="width: 20%;" class="item-center--left--submit">
            <div>已签收：{{ info.signedCount }}</div>
          </div>
          <div style="width: 50%;" class="item-center--left--tip">
            <div>发文时间：{{ dayjs()(info.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div style="width: 25%;" class="item-center--left--tip flex_center">
            <div>文号：</div>
            <div v-if="info.numberAgency&&info.numberYear" style="width: 80%">
              <my-tooltip style="width: 100%"
                          :title="info.numberAgency+'【'+info.numberYear+'】'+info.numberRef+'号'"></my-tooltip>
              <!--              {{ info.numberAgency }} 【{{-->
              <!--                info.numberYear-->
              <!--              }}】{{ info.numberRef }}号-->
            </div>
            <div v-else>-</div>
          </div>
          <div v-if="type=='1'" style="width: 20%;" class="item-center--left--submit">
            <div>应签收：{{ info.count }}</div>
          </div>
        </div>
        <div class="item-center--right" v-if="info.status!=='3'&&info.nodeType!=='2'">
          <!--1.我收到的-->
          <div v-if="type=='0'" class="flex_center flex_end">
            <v-button v-if="info.signFlag === '0'" type="primary" class="m_r_10" @click.native="handleSign">签收
            </v-button>
            <v-button type="primary-plain" @click.native="transmit">转发</v-button>
          </div>
          <!--2.我发起的-->
          <div v-if="type=='1'" class="flex_center flex_end">
            <v-button v-if="info.status !== '3'" type="dangerous" class="m_r_10" @click.native="handleRevoke">撤销
            </v-button>
            <v-button type="primary-plain" @click.native="transmit">转发</v-button>
          </div>
        </div>
      </div>

      <!--    定位-->
      <div class="inx-class">
        <div>{{ inx + 1 }}</div>
      </div>
    </div>

    <!--签收-->
    <sign-dialog ref="signDialogRef"
                 :info="signInfo"
                 @forward="signForward"
                 @success="success"></sign-dialog>

    <!--转发-->
    <unit-tree ref="unitRef"
               :if-forward="isForward"
               :is-clear="true"
               :is-refresh="isRefresh"
               :title="title"
               @closeDialog="closeDialog"
               @confirm="unitSave"></unit-tree>
    <!--撤销-->
    <public-dialog ref="publicDialogRef"
                   :info="publicInfo"
                   :audit-type="publicType"
                   :tipTitle="pubTitle"
                   @submit="pubSubmit"></public-dialog>
  </div>
</template>
<script>
import dayjs from "dayjs";
import vButton from '@/pages/components/v-button.vue'
import signDialog from "@/pages/oa/components/signDialog.vue";
import UnitTree from "@/pages/oa/components/unitTree.vue";
import PublicDialog from "@/pages/oa/components/publicDialog.vue";
import MyTooltip from "@/components/Tooltip.vue";
import {forward} from "@/api/OA";

export default {
  components: {
    MyTooltip,
    PublicDialog,
    UnitTree,
    signDialog,
    vButton
  },
  dicts: ['notice_type', 'notice_urgent_level'],
  props: {
    type: {
      type: Number,
      default: 0
    },
    inx: {
      type: Number,
      default: 0
    },
    info: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      isForward: false,
      isRefresh: true,
      signInfo: {},
      title: '',
      publicInfo: {},
      publicType: 0,
      pubTitle: '',
      listQuery: {
        receivePeoples: [],
        agencyIds: [],
        deptIds: []
      },
      dialogInfo: {},
      userInfo: '',
    }
  },
  computed: {
    dictTagClass() {
      if (this.info.urgentLevel == '0') {
        return 'normal_icon--class'
      } else if (this.info.urgentLevel == '1') {
        return 'emergency--class'
      } else if (this.info.urgentLevel == '2') {
        return 'extra-urgent--class'
      }
    }
  },
  methods: {
    dayjs() {
      return dayjs
    },
    // 签收
    handleSign() {
      this.signInfo = this.info
      this.$refs.signDialogRef.showDialog = true
    },
    // 签收并转发
    signForward() {
      this.transmit()
    },
    // 签收成功
    success() {
      this.$emit('loadSuccess')
    },
    // 转发
    transmit() {
      this.title = '接收单位选择';
      this.isForward = true;
      this.isRefresh = true;
      this.$refs.unitRef.showDialog = true
    },
    // 撤销
    handleRevoke() {
      this.publicType = 1
      let infos = JSON.parse(JSON.stringify(this.info));
      infos.status = 3;
      this.pubTitle = '是否撤销公文' + "'" + this.info.noticeTitle + "'" + '？';
      this.publicInfo = infos;
      this.$refs.publicDialogRef.showDialog = true;
    },
    // 撤销确定
    pubSubmit() {
      this.$emit('loadSuccess')
    },
    // 转发关闭弹窗
    closeDialog() {
      this.$emit('loadSuccess');
    },
    // 转发确认
    unitSave(data) {
      this.listQuery.receivePeoples = []
      let userList = _.clone(data)
      let showLabel = ''
      if (userList.deptIds.length !== 0) {
        userList.deptIds.forEach((item, index) => {
          if (index === userList.deptIds.length - 1) {
            showLabel += item.label
          } else {
            showLabel += item.label + ','
          }
        })
      }
      if (userList.receivePeoples.length !== 0) {
        userList.receivePeoples.forEach((item, index) => {
          if (index === userList.receivePeoples.length - 1) {
            showLabel += item.label
          } else {
            showLabel += item.label + ','
          }
        })
        this.listQuery.receivePeoples = userList.receivePeoples.map(item => {
          return {
            signBy: item.userId,
            deptId: item.deptId || '',
            agencyId: item.agencyId,
            stageId: item.stageId || ''
          };
        })
      }
      this.userInfo = showLabel
      this.handleZf()
    },
    handleZf() {
      let that = this
      this.$confirm('是否转发给' + this.userInfo + '?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let params = {
          contentId: that.info.contentId,
          receivePeoples: that.listQuery.receivePeoples,
          deptIds: that.listQuery.deptIds
        }
        forward(params).then(res => {
          if (res.code === 200) {
            this.$message({
              message: '转发成功',
              type: 'success',
            });
          }
          this.isForward = false;
          this.isRefresh = false;
          this.$emit('loadSuccess')
          // this.$refs.transmitRef.showDialog = true;
          // this.warningTip = res.msg;
        })
      }).catch((action) => {
        if (action == 'cancel') {
          this.isForward = false;
          if (this.isRefresh) {
            this.$emit('loadSuccess')
            this.isRefresh = false;
          }
        }
      })
    },
    toDetail() {
      this.$emit('handleClickOa')

      this.$router.push({
        path: '/oa/audit',
        query: {
          type: this.type,
          id: this.info.contentId
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.top-border {
  border-top: 1px solid #F0F0F0;
}

.top-bg {
  background: #F0F0F0;
  height: 8px;
}

.item-container {
  padding: 15px 15px 15px 70px;
  position: relative;

  .item-top {
    .item-top-tip-class {
      //color: #999;
      font-size: 12px;
    }
  }

  .item-center-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;

    .item-center--left {
      width: 80%;
      display: flex;
      gap: 12px;
      flex-direction: row;
      flex-wrap: wrap;

      .item-center--left--tip {
        font-size: 14px;
      }

      .item-center--left--submit {
        font-size: 14px;
      }
    }

    .item-center--right {
      width: 15%;
    }
  }
}

.inx-class {
  position: absolute;
  top: -1px;
  left: 20px;
  width: 30px;
  height: 35px;
  text-align: center;
  line-height: 32px;
  color: #666;
  font-size: 12px;
  background: url("@/pages/assets/images/OA/inx_icon.png") no-repeat;
  background-size: 100% 100%;
}

.normal_icon--class {
  background: url('@/pages/assets/images/OA/normal_icon.png') no-repeat;
  background-size: 100% 100%;
}

.extra-urgent--class {
  background: url('@/pages/assets/images/OA/extra_urgent.png') no-repeat;
  background-size: 100% 100%;
}

.emergency--class {
  background: url('@/pages/assets/images/OA/emergent.png') no-repeat;
  background-size: 100% 100%;
}

.revoke--class {
  background: url('@/pages/assets/images/OA/revoke_icon.png') no-repeat;
  background-size: 100% 100%;
}

.dict--class {
  font-size: 12px;
  font-family: YouSheBiaoTiHei;
  width: 32px;
  height: 16px;
  text-align: center;
  color: #fff
}

</style>
