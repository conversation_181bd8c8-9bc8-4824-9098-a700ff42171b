<template>
  <div class="m_b_20">
    <el-collapse accordion v-model="active" style="border:none;background: #F7FAFF" @change="changeCollapse">
      <el-collapse-item style="border:none">
        <template slot="title">
          <div class="default" style="width:94%;" @click.stop="showCollpase()">
            <div class="top flex_space aligin_center">
              <div class="flex_center">
                <div style="color:black" class="fs_18 fs_bold">{{ collpaseInfo.fullName }}</div>
                <div class="m_l_10 tag" :class="collpaseInfo.signTime?'is-tag':'is-no'">
                  {{ collpaseInfo.signTime ? '已签收' : '未签收' }}
                </div>
              </div>
              <div class="m_l_20 c_999 flex_center pointer remind"
                   @click="downLoadAllSingleStageFile()">
                <i class="el-icon-download c_title"></i>&nbsp;&nbsp;
                <span class="c_title">打包下载</span>
              </div>
            </div>
            <div class="top-content  m_t_10">
              <!--              <div>成都市武侯区第十四幼儿园（成都市地三十三幼儿园太平园校区）</div>-->
              <div>阅读时间：{{ collpaseInfo.readTime ? collpaseInfo.readTime : collpaseInfo.signTime }}</div>
              <div class="m_t_10">签收时间：{{ collpaseInfo.signTime }}</div>
            </div>
          </div>
        </template>
        <div class="contents">
          <!--          直属部门-->
          <div v-if="false">
            <div v-for="(item,index) in collpaseInfo.userList" :key="index"
                 class="collpaseItem  m_b_10">
              <div style="display: grid;grid-template-columns: 45% 55%;justify-content: space-between;">
                <div class="flex_center">
                  <div>
                    <my-tooltip style="width: 100px"
                                :title="item.username"></my-tooltip>
                  </div>
                  <div class="m_l_20 c_999 flex_center pointer s remind"
                       v-if="item.signFiles&&item.signFiles.length>1&&item.signFlag=='1'"
                       @click="downLoadAllSingleUserFile(item)">
                    <i class="el-icon-download c_title"></i>&nbsp;&nbsp;
                    <span class="c_title">打包下载</span>
                  </div>
                </div>
                <div v-if="item.signFiles&&item.signFiles.length>=1" class="flex_space m_b_10">
                  <div class="flex_center" style="width: 70%">
                    <img style="width: 28px;height:28px" :src="isAssetTypeAnImage(item.signFiles[0].originalFileName)"/>
                    <my-tooltip style="width: 70%" :title="item.signFiles[0].originalFileName"></my-tooltip>
                  </div>
                  <div class="flex_alAndJsCenter">
                    <div class="pointer  c_999"
                         @click="handleView(item.signFiles[0])">
                      <i class="el-icon-document-remove"></i>&nbsp;
                      <span>预览</span>
                    </div>&nbsp;&nbsp;
                    <div class="c_title pointer" @click="downloadSingleFile(item.signFiles[0])">
                      <i class="el-icon-download"></i>&nbsp;
                      <span>下载</span>
                    </div>
                  </div>
                </div>
                <div style="text-align: right" class="c_999" v-if="item.signFlag==='0'">暂未签收</div>
              </div>
              <!-- 从第二个文件开始 item.signFiles.slice(1,item.signFiles.length)-->
              <div v-if="item.signFiles&&item.signFiles.length>1">
                <sub-collapse-file
                    :file-arr="item.signFiles.slice(1,item.signFiles.length)"></sub-collapse-file>
              </div>
            </div>
          </div>
          <div v-if="false">
            <div v-for="(item,index) in userListArr" :key="index"
                 class="collpaseItem  m_b_10">
              <div style="display: grid;grid-template-columns: 35% 55%;justify-content: space-between;">
                <div class="flex_center">
                  <div style="width: 16px;height:16px;margin-right: 5px">
                    <img v-if="item.nodeType=='1'" style="width: 16px;height:16px"
                         src="https://whkj.wuhousmartedu.com/s3/zhihuiyunkongjian/miniprogram/forwad_icon.png">
                  </div>
                  <div>
                    <my-tooltip style="width: 100px"
                                :title="item.username"></my-tooltip>
                  </div>
                  <div class="m_l_20 c_999 flex_center pointer s remind"
                       v-if="item.signFiles&&item.signFiles.length>1&&item.signFlag=='1'"
                       @click="downLoadAllSingleUserFile(item)">
                    <i class="el-icon-download c_title"></i>&nbsp;&nbsp;
                    <span class="c_title">打包下载</span>
                  </div>
                </div>
                <div v-if="item.signFiles&&item.signFiles.length>=1" class="flex_space m_b_10">
                  <div class="flex_center" style="width: 70%">
                    <img style="width: 28px;height:28px" :src="isAssetTypeAnImage(item.signFiles[0].originalFileName)"/>
                    <my-tooltip style="width: 70%" :title="item.signFiles[0].originalFileName"></my-tooltip>
                  </div>
                  <div class="flex_alAndJsCenter">
                    <div class="pointer  c_999"
                         @click="handleView(item.signFiles[0])">
                      <i class="el-icon-document-remove"></i>&nbsp;
                      <span>预览</span>
                    </div>&nbsp;&nbsp;
                    <div class="c_title pointer" @click="downloadSingleFile(item.signFiles[0])">
                      <i class="el-icon-download"></i>&nbsp;
                      <span>下载</span>
                    </div>
                  </div>
                </div>
                <div style="text-align: right" class="c_999" v-if="item.signFlag==='0'">暂未签收</div>
              </div>
              <!-- 从第二个文件开始 item.signFiles.slice(1,item.signFiles.length)-->
              <div v-if="item.signFiles&&item.signFiles.length>1">
                <sub-collapse-file
                    :file-arr="item.signFiles.slice(1,item.signFiles.length)"></sub-collapse-file>
              </div>
            </div>
            <div v-if="showUserStep"
                 style="display: flex;align-items: center;justify-content: space-between;padding:0 10px;">
              <div></div>
              <div style="display: flex;align-items: center;">
                <div class="pointer change_page" @click="handleBeforeUnitArr(collpaseInfo.userList)">上一页</div>&nbsp;&nbsp;
                <div>当前页:{{ step }}</div>&nbsp;&nbsp;
                <div class="pointer change_page" @click="handleNextUnitArr(collpaseInfo.userList)">下一页</div>
              </div>
              <div>共{{ collpaseInfo.userList.length }}条</div>
            </div>
          </div>
          <!-- 子部门-->
          <div v-if="false">
            <div v-for="(item,index) in collpaseInfo.deptVOS" :key="item.deptId">
              <div v-for="it in item.userList" :key="it.userid" class="collpaseItem  m_b_10">
                <div
                    style="display: grid;grid-template-columns: 45% 55%;justify-content: space-between;align-items: center">
                  <div class="flex">
                    <div>
                      <my-tooltip style="width: 100px"
                                  :title="it.username"></my-tooltip>
                    </div>
                    <div class="m_l_20 c_999 flex">
                      <div>所属部门：</div>
                      <div>
                        <my-tooltip style="width: 120px"
                                    :title="item.deptName"></my-tooltip>
                      </div>
                    </div>
                    <div class="m_l_20 c_999 flex_center pointer remind"
                         @click="downLoadAllSingleUserFile(it)"
                         v-if="it.signFiles&&it.signFiles.length>1&&it.signFlag==='1'">
                      <i class="el-icon-download c_title"></i>&nbsp;&nbsp;
                      <span class="c_title">打包下载</span>
                    </div>
                  </div>
                  <div v-if="it.signFiles&&it.signFiles.length>=1" class="flex_space m_b_10">
                    <div class="flex_center" style="width: 70%">
                      <img style="width: 28px;height:28px" :src="isAssetTypeAnImage(it.signFiles[0].originalFileName)"/>
                      <my-tooltip style="width: 70%" :title="it.signFiles[0].originalFileName"></my-tooltip>
                    </div>
                    <div class="flex_alAndJsCenter">
                      <div class="pointer  c_999"
                           @click="handleView(it.signFiles[0])">
                        <i class="el-icon-document-remove"></i>&nbsp;
                        <span>预览</span>
                      </div>&nbsp;&nbsp;
                      <div class="c_title pointer" @click="downloadSingleFile(it.signFiles[0])">
                        <i class="el-icon-download"></i>&nbsp;
                        <span>下载</span>
                      </div>
                    </div>
                  </div>
                  <div style="text-align: right" class="c_999" v-if="it.signFlag==='0'">暂未签收</div>
                </div>
                <!-- 从第二个文件开始-->
                <sub-collapse-file v-if="it.signFiles&&it.signFiles.length>1"
                                   :file-arr="it.signFiles.slice(1,it.signFiles.length)"></sub-collapse-file>
              </div>

            </div>
          </div>
          <div v-if="false">
            <div v-for="(item,index) in deptVOSList" :key="item.deptId">
              <div v-for="it in item.userList" :key="it.userid" class="collpaseItem  m_b_10">
                <div
                    style="display: grid;grid-template-columns: 45% 55%;justify-content: space-between;align-items: center">
                  <div class="flex_center">
                    <div style="width: 16px;height:16px;margin-right: 5px">
                      <img v-if="it.nodeType=='1'" style="width: 16px;height:16px"
                           src="https://whkj.wuhousmartedu.com/s3/zhihuiyunkongjian/miniprogram/forwad_icon.png">
                    </div>
                    <div>
                      <my-tooltip style="width: 100px"
                                  :title="it.username"></my-tooltip>
                    </div>
                    <div class="m_l_20 c_999 flex">
                      <div>所属部门：</div>
                      <div>
                        <my-tooltip style="width: 120px"
                                    :title="item.deptName"></my-tooltip>
                      </div>
                    </div>
                    <div class="m_l_20 c_999 flex_center pointer remind"
                         @click="downLoadAllSingleUserFile(it)"
                         v-if="it.signFiles&&it.signFiles.length>1&&it.signFlag==='1'">
                      <i class="el-icon-download c_title"></i>&nbsp;&nbsp;
                      <span class="c_title">打包下载</span>
                    </div>
                  </div>
                  <div v-if="it.signFiles&&it.signFiles.length>=1" class="flex_space m_b_10">
                    <div class="flex_center" style="width: 70%">
                      <img style="width: 28px;height:28px" :src="isAssetTypeAnImage(it.signFiles[0].originalFileName)"/>
                      <my-tooltip style="width: 70%" :title="it.signFiles[0].originalFileName"></my-tooltip>
                    </div>
                    <div class="flex_alAndJsCenter">
                      <div class="pointer  c_999"
                           @click="handleView(it.signFiles[0])">
                        <i class="el-icon-document-remove"></i>&nbsp;
                        <span>预览</span>
                      </div>&nbsp;&nbsp;
                      <div class="c_title pointer" @click="downloadSingleFile(it.signFiles[0])">
                        <i class="el-icon-download"></i>&nbsp;
                        <span>下载</span>
                      </div>
                    </div>
                  </div>
                  <div style="text-align: right" class="c_999" v-if="it.signFlag==='0'">暂未签收</div>
                </div>
                <!-- 从第二个文件开始-->
                <sub-collapse-file v-if="it.signFiles&&it.signFiles.length>1"
                                   :file-arr="it.signFiles.slice(1,it.signFiles.length)"></sub-collapse-file>
              </div>

            </div>
            <div v-if="showDeptStep"
                 style="display: flex;align-items: center;justify-content: space-between;padding:0 10px;">
              <div></div>
              <div style="display: flex;align-items: center;">
                <div class="pointer change_page" @click="handleBeforeDept(collpaseInfo.deptVOS)">上一页</div>&nbsp;&nbsp;
                <div>当前页:{{ deptStep }}</div>&nbsp;&nbsp;
                <div class="pointer change_page" @click="handleNextDept(collpaseInfo.deptVOS)">下一页</div>
              </div>
              <div>共{{ collpaseInfo.deptVOS.length }}条</div>
            </div>
          </div>
          <div>
            <div v-for="item in showUserAndDeptArr" :key="item.userid" class="collpaseItem  m_b_10">
              <div style="display: grid;grid-template-columns: 45% 55%;justify-content: space-between;">
                <div class="flex_center">
                  <div>
                    <my-tooltip style="width: 100px"
                                :title="item.username"></my-tooltip>
                  </div>
                  <div class="m_l_20 c_999 flex" v-if="item.signDeptName">
                    <div>所属部门：</div>
                    <div>
                      <my-tooltip style="width: 120px"
                                  :title="item.signDeptName"></my-tooltip>
                    </div>
                  </div>
                  <div class="m_l_20 c_999 flex_center pointer s remind"
                       v-if="item.signFiles&&item.signFiles.length>1&&item.signFlag=='1'"
                       @click="downLoadAllSingleUserFile(item)">
                    <i class="el-icon-download c_title"></i>&nbsp;&nbsp;
                    <span class="c_title">打包下载</span>
                  </div>
                </div>
                <div v-if="item.signFiles&&item.signFiles.length>=1" class="flex_space m_b_10">
                  <div class="flex_center" style="width: 70%">
                    <img style="width: 28px;height:28px" :src="isAssetTypeAnImage(item.signFiles[0].originalFileName)"/>
                    <my-tooltip style="width: 70%" :title="item.signFiles[0].originalFileName"></my-tooltip>
                  </div>
                  <div class="flex_alAndJsCenter">
                    <div class="pointer  c_999"
                         @click="handleView(item.signFiles[0])">
                      <i class="el-icon-document-remove"></i>&nbsp;
                      <span>预览</span>
                    </div>&nbsp;&nbsp;
                    <div class="c_title pointer" @click="downloadSingleFile(item.signFiles[0])">
                      <i class="el-icon-download"></i>&nbsp;
                      <span>下载</span>
                    </div>
                  </div>
                </div>
                <div style="text-align: right" class="c_999" v-if="item.signFlag==='0'">暂未签收</div>
              </div>
              <!-- 从第二个文件开始 item.signFiles.slice(1,item.signFiles.length)-->
              <div v-if="item.signFiles&&item.signFiles.length>1">
                <sub-collapse-file
                    :file-arr="item.signFiles.slice(1,item.signFiles.length)"></sub-collapse-file>
              </div>
            </div>
            <div style="text-align: center">
              <el-pagination background
                             layout="total,pager ,jumper"
                             :current-page="showStep"
                             :page-size="5"
                             @current-change="handleChangePage"
                             :total="total"></el-pagination>
            </div>
            <div v-if="false"
                 style="display: flex;align-items: center;justify-content: space-between;padding:0 10px;">
              <div></div>
              <div style="display: flex;align-items: center;user-select: none">
                <div class="pointer change_page" @click="handlePrev">上一页</div>&nbsp;&nbsp;
                <div>当前页:{{ showStep }}</div>&nbsp;&nbsp;
                <div class="pointer change_page" @click="handleNext">下一页</div>
              </div>
              <div>共{{ userAndDeptArr.length }}条</div>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script>
import MyTooltip from "@/components/Tooltip.vue";
import {Base64 as base64} from "js-base64";
import SubCollapseFile from "@/pages/oa/components/collapaseFolder/subCollapseFile.vue";
import {downloadFile} from "@/utils/tools";
import {generateRandomStr, getAesString} from "@/pages/utils/tools";
import {mapGetters} from "vuex";

export default {
  components: {SubCollapseFile, MyTooltip},
  props: {
    collpaseInfo: {
      type: Object,
      default: function () {
        return {}
      }
    },
    type: {
      type: Number,
      default: 0
    },
    contentType: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      icons: {
        pic: require('@/pages/images/img2/pic.png'),
        word: require('@/pages/images/img2/docx.png'),
        xlsx: require('@/pages/images/img2/xls.png'),
        ppt: require('@/pages/images/img2/ppt.png'),
        pdf: require('@/pages/images/img2/pdf.png'),
        video: require('@/pages/images/img2/vedio.png'),
        file: require('@/pages/images/img2/file.png'),
      },
      userList: [],
      receiveDeptList: [],
      params: {
        pageNum: 1,
        pageSize: 1000,
      },
      active: '1',
      unitUserArr: [],
      step: 1,
      beforeStep: 1,
      afterStep: 5,
      deptStep: 1,
      deptBeforeStep: 1,
      deptAfterStep: 5,
      showUserStep: false,
      showDeptStep: false,
      showStep: 1,
      showPrevStep: 0,
      showNextStep: 5,
      showStepAll: false,
      total: 0
    }
  },
  mounted() {

  },
  computed: {
    ...mapGetters(['userInfo', 'name']),
    userAndDeptArr() {
      let arr = JSON.parse(JSON.stringify(this.collpaseInfo.userList));
      this.collpaseInfo.deptVOS.forEach((item, index) => {
        arr = [...arr, ...item.userList]
      })
      this.total = arr.length
      return arr
    },
    showUserAndDeptArr() {
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = (this.userAndDeptArr.length / 5) + 1;
      }
      if (this.userAndDeptArr.length > 5) {
        this.showStepAll = true;
      }
      if (this.showStep === 1) {
        if (this.userAndDeptArr.length > 1 && this.userAndDeptArr.length < 6) {
          return this.userAndDeptArr
        } else {
          return this.userAndDeptArr.slice(0, 5)
        }
      } else {
        return this.userAndDeptArr.slice(this.showPrevStep, this.showNextStep);
      }
    },
    userListArr() {
      let reminder = this.collpaseInfo.userList.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.collpaseInfo.userList.length / 5
      } else {
        totalStep = (this.collpaseInfo.userList.length / 5) + 1;
      }
      if (this.collpaseInfo.userList.length > 5) {
        this.showUserStep = true;
      }
      if (this.step === 1) {
        if (this.collpaseInfo.userList.length > 1 && this.collpaseInfo.userList.length < 6) {
          return this.collpaseInfo.userList
        } else {
          return this.collpaseInfo.userList.slice(0, 5)
        }
      } else {
        return this.collpaseInfo.userList.slice(this.beforeStep, this.afterStep);
      }
    },
    deptVOSList() {
      let reminder = this.collpaseInfo.deptVOS.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.collpaseInfo.deptVOS.length / 5
      } else {
        totalStep = (this.collpaseInfo.deptVOS.length / 5) + 1;
      }
      if (this.collpaseInfo.deptVOS.length > 5) {
        this.showDeptStep = true;
      }
      if (this.deptStep === 1) {
        if (this.collpaseInfo.deptVOS.length > 1 && this.collpaseInfo.deptVOS.length < 6) {
          return this.collpaseInfo.deptVOS
        } else {
          return this.collpaseInfo.deptVOS.slice(0, 5)
        }
      } else {
        return this.collpaseInfo.deptVOS.slice(this.deptBeforeStep, this.deptAfterStep);
      }
    }
  },
  methods: {
    handleChangePage(page) {
      this.showStep = page;
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder == 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = Math.floor(this.userAndDeptArr.length / 5) + 1;
      }
      if (this.showStep <= totalStep) {
        this.showPrevStep = 5 * (this.showStep - 1);
        this.showNextStep = this.showPrevStep + 5;
      }
    },
    handleNext() {
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder == 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = Math.floor(this.userAndDeptArr.length / 5) + 1;
      }
      if (this.showStep < totalStep) {
        this.showStep++;
        this.showPrevStep = 5 * (this.showStep - 1);
        this.showNextStep = this.showPrevStep + 5;
      }
    },
    handlePrev() {
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = (this.userAndDeptArr.length / 5) + 1;
      }
      if (this.showStep > 1) {
        this.showStep--;
        this.showPrevStep = 5 * (this.showStep - 1);
        this.showNextStep = this.showPrevStep + 5;
      }
    },
    // 直属部门上一页
    handleBeforeUnitArr(arr) {
      let reminder = arr.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = arr.length / 5
      } else {
        totalStep = (arr.length / 5) + 1;
      }
      if (this.step > 1) {
        this.step--;
        this.beforeStep = 5 * (this.step - 1);
        this.afterStep = this.beforeStep + 5;
        // this.computedUniArr(arr, 'sub')
      }
    },
    // 直属部门下一页
    handleNextUnitArr(arr) {
      let reminder = arr.length % 5;
      let totalStep;
      if (reminder == 0) {
        totalStep = arr.length / 5
      } else {
        totalStep = Math.floor(arr.length / 5) + 1;
      }
      if (this.step < totalStep) {
        this.step++;
        this.beforeStep = 5 * (this.step - 1);
        this.afterStep = this.beforeStep + 5;
        // this.computedUniArr(arr, 'add')
      }
    },
    // 子部门上一页
    handleBeforeDept(arr) {
      let reminder = arr.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = arr.length / 5
      } else {
        totalStep = (arr.length / 5) + 1;
      }
      if (this.deptStep > 1) {
        this.deptStep--;
        this.deptBeforeStep = 5 * (this.deptStep - 1);
        this.deptAfterStep = this.deptBeforeStep + 5;
        // this.computedUniArr(arr, 'sub')
      }
    },
    // 子部门下一页
    handleNextDept(arr) {
      let reminder = arr.length % 5;
      let totalStep;
      if (reminder == 0) {
        totalStep = arr.length / 5
      } else {
        totalStep = Math.floor(arr.length / 5) + 1;
      }
      if (this.deptStep < totalStep) {
        this.deptStep++;
        this.deptBeforeStep = 5 * (this.deptStep - 1);
        this.deptAfterStep = this.deptBeforeStep + 5;
        // this.computedUniArr(arr, 'add')
      }
    },
    showCollpase() {
    },
    changeCollapse() {
      if (this.active.length === 1) {
        this.step = 1
        this.beforeStep = 1
        this.afterStep = 5
        this.deptStep = 1
        this.deptBeforeStep = 1
        this.deptAfterStep = 5
      }
    },
    clickRemind() {
    },
    // 机构打包下载
    downLoadAllSingleStageFile() {
      this.$download.zip('/oa/content/' + this.contentType.contentId + '/download-zip', {
        agencyId: this.collpaseInfo.agencyId,
        stageId: this.collpaseInfo.stageId
      }, this.collpaseInfo.fullName + '-' + '文件汇总')

    },
    //个人打包下载
    downLoadAllSingleUserFile(item) {
      this.$download.zip('/oa/content/' + this.contentType.contentId + '/download-zip', {
        nodeId: item.nodeId
      }, this.collpaseInfo.fullName + '-' + (item.signDeptName ? item.signDeptName + '-' + item.username + '文件汇总' : item.username + '文件汇总'))
    },
    downloadSingleFile(item) {
      downloadFile(item.ossFileUrl, item.originalFileName)
      // this.$download.downloadExport(item.originalFileName, item.ossFileUrl)
      // window.open(item.ossFileUrl + `?attname=` + item.originalFileName, '_self')
    },
    handleView(item) {
      let watermarkTxt = ''
      if (this.role.role_type == 'stu') {
        watermarkTxt = this.name;
      } else {
        watermarkTxt = this.name + ' ' + this.userInfo.phone;
      }
      window.open(process.env.VUE_APP_KFILE_URL +
          encodeURIComponent(base64.encode(item.ossFileUrl)) + '&watermarkTxt=' + encodeURIComponent(getAesString(watermarkTxt,
              generateRandomStr(16), generateRandomStr(16))))
    },
    //判断文件类型
    isAssetTypeAnImage(url) {
      let i = url.lastIndexOf('.'),
          ext = '';
      if (i > -1) {
        ext = url.substring(i + 1);
      }
      //图片
      if (
          [
            'png',
            'jpg',
            'jpeg',
            'bmp',
            'gif',
            'webp',
            'psd',
            'svg',
            'tiff',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.pic;
      } else if (['doc', 'docx'].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.word;
      } else if (['xls', 'xlsx',].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.xlsx;
      } else if (['pdf',].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.pdf;
      } else if (['ppt', 'pptx'].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.ppt;
      } else if (
          [
            'mp4',
            'flv',
            'm3u8',
            'rtmp',
            'hls',
            'rtsp',
            'mp3',
            'flac',
            'm4a',
            'ogg',
            'ape',
            'amr',
            'wma',
            'wav',
            'aac',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.video;
      } else {
        return this.icons.file;
      }
    },
  }

}
</script>
<style lang="scss">
.top {
  .tag {
    border-radius: 4px;
    line-height: 10px;
    font-size: 12px;
    text-align: center;
    padding: 5px;
    width: 40px;
    height: 10px;
  }

  .is-tag {
    color: #FFFFFF;
    background: #1B77F5;
  }

  .is-read {
    color: #3B8989;
    background: rgb(209, 227, 228);
  }

  .is-no {
    color: #949494;
    background: #E2E2E2;
  }
}

.top-content {
  color: rgb(151, 154, 154);
}

.contents {
  .collpaseItem {
    background: #fff;
    padding: 10px 20px;
    border-radius: 6px;

    .is-color {
      color: #6A94FF;
    }

    .no-color {
      color: #999999
    }
  }
}

.el-collapse-item__header {
  border-top-left-radius: 10px;
  border-bottom: 0;
  height: auto;
  line-height: 20px;
  align-items: initial;
}

.el-collapse-item__wrap {
  border-bottom: 0;

}

.el-collapse-item__header, .el-collapse-item__wrap {
  padding: 10px;
  background-color: #F7FAFF
}

.file-collapse {
  .el-collapse-item__header {

  }

  .el-collapse-item__header, .el-collapse-item__wrap {
    padding: 10px;
    background-color: #fff
  }

  .el-collapse-item__arrow {
    text-align: center;
    height: 20px;
    line-height: 20px;
    font-size: 14px;

    &:before {
      content: '更多材料'+' \e790';
    }
  }

  .el-collapse-item__arrow.is-active {
    transform: rotate(0);
  }

  .el-collapse-item__arrow.is-active {
    &:before {
      content: '收起'+' \e78f';
      transform: rotate(0);
    }
  }
}

.el-collapse-item__arrow {
  height: 20px;
  line-height: 20px;
  font-size: 14px;

  &:before {
    content: '展开'+' \e790';
  }
}

.el-collapse-item__arrow.is-active {
  transform: rotate(0);
}

.el-collapse-item__arrow.is-active {
  &:before {
    content: '收起'+' \e78f';
    transform: rotate(0);
  }
}

.remind:active {
  opacity: .7;
}
</style>
