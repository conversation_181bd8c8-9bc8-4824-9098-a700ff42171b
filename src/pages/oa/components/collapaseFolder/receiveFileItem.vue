<template>
  <div>
    <div style="display: grid;grid-template-columns: 45% 55%;justify-content: space-between;">
      <div class="flex_center">
        <div>
          <my-tooltip style="width: 100px" :show-forward="info.nodeType=='1'"
                      :title="info.username"></my-tooltip>
        </div>
        <div class="m_l_20 c_999 flex" v-if="false">
          <div>所属部门：</div>
          <div>
            <my-tooltip style="width: 120px"
                        :title="info.signDeptName"></my-tooltip>
          </div>
        </div>
        <div class="m_l_20 c_999 flex_center pointer s remind" style="user-select:none;"
             v-if="info.signFiles&&info.signFiles.length>1&&info.signFlag=='1'"
             @click="downLoadAllSingleUserFile(info)">
          <i class="el-icon-download c_title"></i>&nbsp;&nbsp;
          <span class="c_title">打包下载</span>
        </div>
      </div>
      <div v-if="info.signFiles&&info.signFiles.length>=1" class="flex_space m_b_10">
        <div class="flex_center" style="width: 70%">
          <img style="width: 28px;height:28px" :src="isAssetTypeAnImage(info.signFiles[0].originalFileName)"/>
          <my-tooltip style="width: 70%" :title="info.signFiles[0].originalFileName"></my-tooltip>
        </div>
        <div class="flex_alAndJsCenter" style="user-select:none;">
          <div class="pointer  c_999"
               @click="handleView(info.signFiles[0])">
            <i class="el-icon-document-remove"></i>&nbsp;
            <span>预览</span>
          </div>&nbsp;&nbsp;
          <div class="c_title pointer" @click="downloadSingleFile(info.signFiles[0])">
            <i class="el-icon-download"></i>&nbsp;
            <span>下载</span>
          </div>
        </div>
      </div>
      <div style="text-align: right" class="c_999" v-if="info.signFlag==='0'">暂未签收</div>
    </div>
    <div v-if="info.signFiles&&info.signFiles.length>1">
      <sub-collapse-file
          :file-arr="info.signFiles.slice(1,info.signFiles.length)"></sub-collapse-file>
    </div>
  </div>
</template>
<script>
import SubCollapseFile from "@/pages/oa/components/collapaseFolder/subCollapseFile.vue";
import MyTooltip from "@/components/Tooltip.vue";
import {Base64 as base64} from "js-base64";
import {downloadFile} from "@/utils/tools";
import {generateRandomStr, getAesString} from "@/pages/utils/tools";
import {mapGetters} from "vuex";

export default {
  components: {MyTooltip, SubCollapseFile},
  props: {
    info: {
      type: Object,
      default: function () {
        return {}
      }
    },
    contentType: {
      type: Object,
      default: function () {
        return {}
      }
    },
    collpaseInfo: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  computed: {
    ...mapGetters(['userInfo', 'name', 'role'])
  },
  data() {
    return {
      icons: {
        pic: require('@/pages/images/img2/pic.png'),
        word: require('@/pages/images/img2/docx.png'),
        xlsx: require('@/pages/images/img2/xls.png'),
        ppt: require('@/pages/images/img2/ppt.png'),
        pdf: require('@/pages/images/img2/pdf.png'),
        video: require('@/pages/images/img2/vedio.png'),
        file: require('@/pages/images/img2/file.png'),
      },
    }
  },
  methods: {
    handleView(item) {
      let watermarkTxt = ''
      if (this.role.role_type == 'stu') {
        watermarkTxt = this.name;
      } else {
        watermarkTxt = this.name + ' ' + this.userInfo.phone;
      }
      window.open(process.env.VUE_APP_KFILE_URL +
          encodeURIComponent(base64.encode(item.ossFileUrl)) + '&watermarkTxt=' + encodeURIComponent(getAesString(watermarkTxt,
              generateRandomStr(16), generateRandomStr(16))))
    },
    downloadSingleFile(item) {
      downloadFile(item.ossFileUrl, item.originalFileName)
    },
    //个人打包下载
    downLoadAllSingleUserFile(item) {
      let fileName = this.collpaseInfo.fullName ? this.collpaseInfo.fullName + '-' : ''
      this.$download.zip('/oa/content/' + this.contentType.contentId + '/download-zip', {
        nodeId: item.nodeId
      }, fileName + (item.signDeptName ? item.signDeptName + '-' + item.username + '文件汇总' : item.username + '文件汇总'))
    },
    //判断文件类型
    isAssetTypeAnImage(url) {
      let i = url.lastIndexOf('.'),
          ext = '';
      if (i > -1) {
        ext = url.substring(i + 1);
      }
      //图片
      if (
          [
            'png',
            'jpg',
            'jpeg',
            'bmp',
            'gif',
            'webp',
            'psd',
            'svg',
            'tiff',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.pic;
      } else if (['doc', 'docx'].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.word;
      } else if (['xls', 'xlsx',].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.xlsx;
      } else if (['pdf',].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.pdf;
      } else if (['ppt', 'pptx'].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.ppt;
      } else if (
          [
            'mp4',
            'flv',
            'm3u8',
            'rtmp',
            'hls',
            'rtsp',
            'mp3',
            'flac',
            'm4a',
            'ogg',
            'ape',
            'amr',
            'wma',
            'wav',
            'aac',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.video;
      } else {
        return this.icons.file;
      }
    },
  }
}
</script>
<style lang="scss" scoped></style>
