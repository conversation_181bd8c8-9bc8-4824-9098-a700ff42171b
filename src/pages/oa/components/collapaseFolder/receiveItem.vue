<template>
  <div class="collpaseItem  m_b_10">
    <div class="flex">
      <my-tooltip :show-forward="info.nodeType=='1'" style="width: 80%"
                  :title="info.username"></my-tooltip>
    </div>
    <div class="m_l_20 c_999 flex">
      <div v-if="info.signFlag=='1'">
        <span style="white-space: nowrap">办理意见：</span>
      </div>
      <el-tooltip v-if="info.signFlag=='1'"
                  effect="dark"
                  :content="info.advice"
                  placement="top"
                  :open-delay="300"
      >
        <div class="description">
          {{ info.advice }}
        </div>
      </el-tooltip>
      <my-tooltip v-if="false" style="width: 80%"
                  :title="'办理意见：办理意见最长字数展示，办理意见最长字数展示办理意见最长字数展示，办理意见最长 字数展示，办理意见最长字数展示办理意见最长字数展示，办理意见最长字数展示办...'"></my-tooltip>
    </div>
    <div class="c_999">
      <div v-if="info.readTime">阅读时间：{{ parseTime(info.readTime) }}</div>
      <div v-if="info.signTime">签收时间：{{ parseTime(info.signTime) }}</div>
    </div>
    <div style="width: 100%;display: flex;align-items: center;justify-content: flex-end">
      <div v-if="info.signFlag=='0'" class="remind pointer flex_alAndJsCenter c_title m_r_10"
           @click="handleRemind(it)">
        <img src="../../../images/img2/remind_blue.png">&nbsp;
        <div>提醒</div>
      </div>
      <div style="text-align: right">
        <span :class="info.signFlag === '0'?'no-color':'is-color'">{{
            info.signFlag === '0' ? '未签收' : '已签收'
          }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import MyTooltip from "@/components/Tooltip.vue";
import {pushNoticeSignMsg} from "@/api/OA";
import {parseTime} from "@/utils/tools";

export default {
  props: {
    info: {
      type: Object,
      default: function () {
        return {}
      }
    },
    contentType: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  components: {MyTooltip},
  data() {
    return {}
  },
  methods: {
    parseTime,
    handleRemind(item) {
      this.$confirm('确定发起提醒吗？', '提示', {
        type: 'warning',
      }).then(() => {
        let params = {
          userId: this.info.userid,
          contentId: this.contentType.contentId
        };
        pushNoticeSignMsg(params).then(res => {
          if (res.code == 200) {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.$emit('success')
          }
        })
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.collpaseItem {
  background: #fff;
  padding: 10px 20px 10px 30px;
  border-radius: 6px;
  line-height: 1.5;
  display: grid;
  grid-template-columns:10% 55% 25% 10%;
  justify-content: space-between;
  min-height: 40px;

  .is-color {
    color: #6A94FF;
  }

  .no-color {
    color: #999999
  }
}

.description {
  width: 80%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
</style>
