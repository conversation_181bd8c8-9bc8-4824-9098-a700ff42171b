<template>
  <div class="m_b_20">
    <el-collapse v-model="isOpenCollapse" style="border:none;background: #F7FAFF"
                 @change="openCollpase">
      <el-collapse-item style="border:none" :name="currentInx">
        <template slot="title">
          <div class="default" style="width:94%;" @click.stop="showCollpase()">
            <div class="top flex_space aligin_center">
              <div class="flex_center">
                <div style="color:black" class="fs_18 fs_bold">{{ collpaseInfo.fullName }}</div>
                <div class="m_l_10 tag" :class="collpaseInfo.signTime?'is-tag':'is-no'">
                  {{ collpaseInfo.signTime ? '已签收' : '未签收' }}
                </div>
              </div>
              <div v-if="!collpaseInfo.signTime" class="fs_14 remind pointer flex_alAndJsCenter c_title"
                   @click="clickRemind">
                <img src="../../../images/img2/remind_blue.png">&nbsp;
                <span>提醒</span>
              </div>
            </div>
            <div class="top-content  m_t_10">
              <!--              <div>成都市武侯区第十四幼儿园（成都市地三十三幼儿园太平园校区）</div>-->
              <div>阅读时间：{{
                  collpaseInfo.readTime ? parseTime(collpaseInfo.readTime) : parseTime(collpaseInfo.signTime)
                }}
              </div>
              <div class="m_t_10">签收时间：{{ parseTime(collpaseInfo.signTime) }}</div>
            </div>
          </div>
        </template>
        <div class="contents">
          <div>
            <div v-for="it in showUserAndDeptArr" :key="it.userid">
              <receive-item :info="it" :content-type="contentType"></receive-item>
            </div>
            <div style="text-align: center">
              <el-pagination background
                             layout="total,pager ,jumper"
                             :current-page="showStep"
                             :page-size="5"
                             @current-change="handleChangePage"
                             :total="total"></el-pagination>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script>
import {pushNoticeSignMsg} from "@/api/OA";
import MyTooltip from "@/components/Tooltip.vue";
import ReceiveItem from "@/pages/oa/components/collapaseFolder/receiveItem.vue";
import {parseTime} from "@/utils/tools";

export default {
  components: {ReceiveItem, MyTooltip},
  props: {
    collpaseInfo: {
      type: Object,
      default: function () {
        return {}
      }
    },
    type: {
      type: Number,
      default: 0
    },
    contentType: {
      type: Object,
      default: function () {
        return {}
      }
    },
    isOpen: {
      type: Boolean,
      default: false
    },
    currentInx: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      userList: [],
      receiveDeptList: [],
      params: {
        pageNum: 1,
        pageSize: 1000,
      },
      activeNames: '1',
      isClick: true,
      step: 1,
      showStep: 1,
      showPrevStep: 0,
      showNextStep: 5,
      showStepAll: false,
      total: 0
    }
  },
  computed: {
    userAndDeptArr() {
      let arr = JSON.parse(JSON.stringify(this.collpaseInfo.userList));
      this.collpaseInfo.deptVOS.forEach((item, index) => {
        arr = [...arr, ...item.userList]
      })
      this.total = arr.length
      return arr
    },
    showUserAndDeptArr() {
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = (this.userAndDeptArr.length / 5) + 1;
      }
      if (this.userAndDeptArr.length > 5) {
        this.showStepAll = true;
      }
      if (this.showStep === 1) {
        if (this.userAndDeptArr.length > 1 && this.userAndDeptArr.length < 6) {
          return this.userAndDeptArr
        } else {
          return this.userAndDeptArr.slice(0, 5)
        }
      } else {
        return this.userAndDeptArr.slice(this.showPrevStep, this.showNextStep);
      }
    },
    isOpenCollapse: {
      get() {
        if (this.isOpen) {
          return [this.currentInx]
        } else {
          return []
        }
      },
      set() {

      }
    }
  },
  methods: {
    parseTime,
    //获取人员
    getReceiveUserLists() {
    },
    showCollpase() {
    },
    openCollpase() {
      if (this.activeNames.length === 1) {
        this.showStep = 1;
        this.showPrevStep = 0;
        this.showNextStep = 5;
        this.showStepAll = false
      }
      if (this.activeNames.length === 2) {
        this.$emit('open');
        this.getReceiveUserLists()
      }
    },
    // 单位提醒
    clickRemind() {
      this.$confirm('确定发起提醒吗？', '提示', {
        type: 'warning',
      }).then(() => {
        let params = {
          agencyId: this.collpaseInfo.agencyId,
          contentId: this.contentType.contentId
        };
        pushNoticeSignMsg(params).then(res => {
          if (res.code == 200) {
            this.$message({
              message: '操作成功'
              , type: 'success'
            })
            this.$emit('success')
          }
        })
      })
    },
    handleChangePage(page) {
      this.showStep = page;
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder == 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = Math.floor(this.userAndDeptArr.length / 5) + 1;
      }
      if (this.showStep <= totalStep) {
        this.showPrevStep = 5 * (this.showStep - 1);
        this.showNextStep = this.showPrevStep + 5;
      }
    },
    handleNext() {
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder == 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = Math.floor(this.userAndDeptArr.length / 5) + 1;
      }
      if (this.showStep < totalStep) {
        this.showStep++;
        this.showPrevStep = 5 * (this.showStep - 1);
        this.showNextStep = this.showPrevStep + 5;
      }
    },
    handlePrev() {
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = (this.userAndDeptArr.length / 5) + 1;
      }
      if (this.showStep > 1) {
        this.showStep--;
        this.showPrevStep = 5 * (this.showStep - 1);
        this.showNextStep = this.showPrevStep + 5;
      }
    },
  }

}
</script>
<style lang="scss">
.top {
  .tag {
    border-radius: 4px;
    line-height: 10px;
    font-size: 12px;
    text-align: center;
    padding: 5px;
    width: 40px;
    height: 10px;
  }

  .is-tag {
    color: #FFFFFF;
    background: #1B77F5;
  }

  .is-read {
    color: #3B8989;
    background: rgb(209, 227, 228);
  }

  .is-no {
    color: #949494;
    background: #E2E2E2;
  }
}

.top-content {
  color: rgb(151, 154, 154);
}

.contents {
  .collpaseItem {
    background: #fff;
    padding: 10px 20px 10px 30px;
    border-radius: 6px;

    .is-color {
      color: #6A94FF;
    }

    .no-color {
      color: #999999
    }
  }
}

.el-collapse-item__header {
  border-top-left-radius: 10px;
  border-bottom: 0;
  height: auto;
  line-height: 20px;
  align-items: initial;
}

.el-collapse-item__wrap {
  border-bottom: 0;

}

.el-collapse-item__header, .el-collapse-item__wrap {
  padding: 10px;
  background-color: #F7FAFF
}

.el-collapse-item__arrow {
  height: 20px;
  line-height: 20px;
  font-size: 14px;

  &:before {
    content: '展开'+' \e790';
  }
}

.el-collapse-item__arrow.is-active {
  transform: rotate(0);
}

.el-collapse-item__arrow.is-active {
  &:before {
    content: '收起'+' \e78f';
    transform: rotate(0);
  }
}

.remind:active {
  opacity: .7;
}
</style>

