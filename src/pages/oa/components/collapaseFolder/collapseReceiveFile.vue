<template>
  <div class="m_b_20">
    <el-collapse accordion v-model="isOpenCollapse" style="border:none;background: #F7FAFF" @change="changeCollapse">
      <el-collapse-item style="border:none" :name="currentInx">
        <template slot="title">
          <div class="default" style="width:94%;" @click.stop="showCollpase()">
            <div class="top flex_space aligin_center">
              <div class="flex_center">
                <div style="color:black" class="fs_18 fs_bold">{{ collpaseInfo.fullName }}</div>
                <div class="m_l_10 tag" :class="collpaseInfo.signTime?'is-tag':'is-no'">
                  {{ collpaseInfo.signTime ? '已签收' : '未签收' }}
                </div>
              </div>
              <div class="m_l_20 c_999 flex_center pointer remind"
                   @click="downLoadAllSingleStageFile()">
                <i class="el-icon-download c_title"></i>&nbsp;&nbsp;
                <span class="c_title">打包下载</span>
              </div>
            </div>
            <div class="top-content  m_t_10">
              <!--              <div>成都市武侯区第十四幼儿园（成都市地三十三幼儿园太平园校区）</div>-->
              <div>阅读时间：{{ collpaseInfo.readTime ? collpaseInfo.readTime : collpaseInfo.signTime }}</div>
              <div class="m_t_10">签收时间：{{ collpaseInfo.signTime }}</div>
            </div>
          </div>
        </template>
        <div class="contents">
          <div>
            <div v-for="item in showUserAndDeptArr" :key="item.userid" class="collpaseItem  m_b_10">
              <receive-file-item :info="item" :collpase-info="collpaseInfo"
                                 :content-type="contentType"></receive-file-item>
            </div>
            <div style="text-align: center">
              <el-pagination background
                             layout="total,pager ,jumper"
                             :current-page="showStep"
                             :page-size="5"
                             @current-change="handleChangePage"
                             :total="total"></el-pagination>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script>
import MyTooltip from "@/components/Tooltip.vue";
import {Base64 as base64} from "js-base64";
import SubCollapseFile from "@/pages/oa/components/collapaseFolder/subCollapseFile.vue";
import {downloadFile} from "@/utils/tools";
import ReceiveFileItem from "@/pages/oa/components/collapaseFolder/receiveFileItem.vue";

export default {
  components: {ReceiveFileItem, SubCollapseFile, MyTooltip},
  props: {
    collpaseInfo: {
      type: Object,
      default: function () {
        return {}
      }
    },
    type: {
      type: Number,
      default: 0
    },
    contentType: {
      type: Object,
      default: function () {
        return {}
      }
    },
    currentInx: {
      type: Number,
      default: 0
    },
    isOpen: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      icons: {
        pic: require('@/pages/images/img2/pic.png'),
        word: require('@/pages/images/img2/docx.png'),
        xlsx: require('@/pages/images/img2/xls.png'),
        ppt: require('@/pages/images/img2/ppt.png'),
        pdf: require('@/pages/images/img2/pdf.png'),
        video: require('@/pages/images/img2/vedio.png'),
        file: require('@/pages/images/img2/file.png'),
      },
      userList: [],
      receiveDeptList: [],
      params: {
        pageNum: 1,
        pageSize: 1000,
      },
      active: '1',
      unitUserArr: [],
      step: 1,
      beforeStep: 1,
      afterStep: 5,
      deptStep: 1,
      deptBeforeStep: 1,
      deptAfterStep: 5,
      showUserStep: false,
      showDeptStep: false,
      showStep: 1,
      showPrevStep: 0,
      showNextStep: 5,
      showStepAll: false,
      total: 0
    }
  },
  mounted() {

  },
  computed: {
    userAndDeptArr() {
      let arr = JSON.parse(JSON.stringify(this.collpaseInfo.userList));
      this.collpaseInfo.deptVOS.forEach((item, index) => {
        arr = [...arr, ...item.userList]
      })
      this.total = arr.length
      return arr
    },
    showUserAndDeptArr() {
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = (this.userAndDeptArr.length / 5) + 1;
      }
      if (this.userAndDeptArr.length > 5) {
        this.showStepAll = true;
      }
      if (this.showStep === 1) {
        if (this.userAndDeptArr.length > 1 && this.userAndDeptArr.length < 6) {
          return this.userAndDeptArr
        } else {
          return this.userAndDeptArr.slice(0, 5)
        }
      } else {
        return this.userAndDeptArr.slice(this.showPrevStep, this.showNextStep);
      }
    },
    isOpenCollapse: {
      get() {
        if (this.isOpen) {
          return [this.currentInx]
        } else {
          return []
        }
      },
      set() {

      }
    },
    userListArr() {
      let reminder = this.collpaseInfo.userList.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.collpaseInfo.userList.length / 5
      } else {
        totalStep = (this.collpaseInfo.userList.length / 5) + 1;
      }
      if (this.collpaseInfo.userList.length > 5) {
        this.showUserStep = true;
      }
      if (this.step === 1) {
        if (this.collpaseInfo.userList.length > 1 && this.collpaseInfo.userList.length < 6) {
          return this.collpaseInfo.userList
        } else {
          return this.collpaseInfo.userList.slice(0, 5)
        }
      } else {
        return this.collpaseInfo.userList.slice(this.beforeStep, this.afterStep);
      }
    },
    deptVOSList() {
      let reminder = this.collpaseInfo.deptVOS.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.collpaseInfo.deptVOS.length / 5
      } else {
        totalStep = (this.collpaseInfo.deptVOS.length / 5) + 1;
      }
      if (this.collpaseInfo.deptVOS.length > 5) {
        this.showDeptStep = true;
      }
      if (this.deptStep === 1) {
        if (this.collpaseInfo.deptVOS.length > 1 && this.collpaseInfo.deptVOS.length < 6) {
          return this.collpaseInfo.deptVOS
        } else {
          return this.collpaseInfo.deptVOS.slice(0, 5)
        }
      } else {
        return this.collpaseInfo.deptVOS.slice(this.deptBeforeStep, this.deptAfterStep);
      }
    }
  },
  methods: {
    handleChangePage(page) {
      this.showStep = page;
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder == 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = Math.floor(this.userAndDeptArr.length / 5) + 1;
      }
      if (this.showStep <= totalStep) {
        this.showPrevStep = 5 * (this.showStep - 1);
        this.showNextStep = this.showPrevStep + 5;
      }
    },
    handleNext() {
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder == 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = Math.floor(this.userAndDeptArr.length / 5) + 1;
      }
      if (this.showStep < totalStep) {
        this.showStep++;
        this.showPrevStep = 5 * (this.showStep - 1);
        this.showNextStep = this.showPrevStep + 5;
      }
    },
    handlePrev() {
      let reminder = this.userAndDeptArr.length % 5;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.userAndDeptArr.length / 5
      } else {
        totalStep = (this.userAndDeptArr.length / 5) + 1;
      }
      if (this.showStep > 1) {
        this.showStep--;
        this.showPrevStep = 5 * (this.showStep - 1);
        this.showNextStep = this.showPrevStep + 5;
      }
    },
    showCollpase() {
    },
    changeCollapse() {
      if (this.active.length === 1) {
        this.step = 1
        this.beforeStep = 1
        this.afterStep = 5
        this.deptStep = 1
        this.deptBeforeStep = 1
        this.deptAfterStep = 5
      }
      if (this.active.length === 2) {
        this.getReceiveUserLists()
      }
    },
    getReceiveUserLists() {
    },
    // 机构打包下载
    downLoadAllSingleStageFile() {
      this.$download.zip('/oa/content/' + this.contentType.contentId + '/download-zip', {
        agencyId: this.collpaseInfo.agencyId,
        stageId: this.collpaseInfo.stageId,
        deptId: this.collpaseInfo.deptId ? this.collpaseInfo.deptId : ''
      }, this.collpaseInfo.fullName + '-' + '文件汇总')
    },
  }

}
</script>
<style lang="scss">
.top {
  .tag {
    border-radius: 4px;
    line-height: 10px;
    font-size: 12px;
    text-align: center;
    padding: 5px;
    width: 40px;
    height: 10px;
  }

  .is-tag {
    color: #FFFFFF;
    background: #1B77F5;
  }

  .is-read {
    color: #3B8989;
    background: rgb(209, 227, 228);
  }

  .is-no {
    color: #949494;
    background: #E2E2E2;
  }
}

.top-content {
  color: rgb(151, 154, 154);
}

.contents {
  .collpaseItem {
    background: #fff;
    padding: 10px 20px;
    border-radius: 6px;

    .is-color {
      color: #6A94FF;
    }

    .no-color {
      color: #999999
    }
  }
}

.el-collapse-item__header {
  border-top-left-radius: 10px;
  border-bottom: 0;
  height: auto;
  line-height: 20px;
  align-items: initial;
}

.el-collapse-item__wrap {
  border-bottom: 0;

}

.el-collapse-item__header, .el-collapse-item__wrap {
  padding: 10px;
  background-color: #F7FAFF
}

.file-collapse {
  .el-collapse-item__header {

  }

  .el-collapse-item__header, .el-collapse-item__wrap {
    padding: 10px;
    background-color: #fff
  }

  .el-collapse-item__arrow {
    text-align: center;
    height: 20px;
    line-height: 20px;
    font-size: 14px;

    &:before {
      content: '更多材料'+' \e790';
    }
  }

  .el-collapse-item__arrow.is-active {
    transform: rotate(0);
  }

  .el-collapse-item__arrow.is-active {
    &:before {
      content: '收起'+' \e78f';
      transform: rotate(0);
    }
  }
}

.el-collapse-item__arrow {
  height: 20px;
  line-height: 20px;
  font-size: 14px;

  &:before {
    content: '展开'+' \e790';
  }
}

.el-collapse-item__arrow.is-active {
  transform: rotate(0);
}

.el-collapse-item__arrow.is-active {
  &:before {
    content: '收起'+' \e78f';
    transform: rotate(0);
  }
}

.remind:active {
  opacity: .7;
}
</style>
