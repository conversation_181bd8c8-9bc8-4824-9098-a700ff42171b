<template>
  <div>
    <CollapseTransition>
      <div v-if="showCollapse"
           style="display:grid;grid-template-columns: 35% 55%;justify-content: space-between;align-items: center ">
        <div></div>
        <div>
          <div v-for="file in fileArr" class="flex_space m_b_10">
            <div class="flex_center" style="width: 70%">
              <img style="width: 28px;height:28px" :src="isAssetTypeAnImage(file.originalFileName)"/>
              <my-tooltip style="width: 70%" :title="file.originalFileName"></my-tooltip>
            </div>
            <div class="flex_alAndJsCenter" style="user-select: none">
              <div class="pointer  c_999"
                   @click="handleView(file)">
                <i class="el-icon-document-remove"></i>&nbsp;
                <span>预览</span>
              </div>&nbsp;&nbsp;
              <div class="c_title pointer" @click="downloadSingleFile(file)">
                <i class="el-icon-download"></i>&nbsp;
                <span>下载</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </CollapseTransition>
    <div style="text-align: center;user-select: none" @click="changeStatus">
      <div v-if="!showCollapse" class="c_999 pointer">
        <span>更多材料</span>
        <i class="el-icon-arrow-down"></i>
      </div>
      <div v-if="showCollapse" class="c_999 pointer">
        <span>收起</span>
        <i class="el-icon-arrow-up"></i>
      </div>
    </div>
  </div>
</template>
<script>
import MyTooltip from "@/components/Tooltip.vue";
import 'element-ui/lib/theme-chalk/base.css';
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition';
import {Base64 as base64} from "js-base64";
import {downloadFile} from "@/utils/tools";
import {generateRandomStr, getAesString} from "@/pages/utils/tools";
import {mapGetters} from "vuex";

export default {
  components: {MyTooltip, CollapseTransition},
  props: {
    fileArr: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data() {
    return {
      icons: {
        pic: require('@/pages/images/img2/pic.png'),
        word: require('@/pages/images/img2/docx.png'),
        xlsx: require('@/pages/images/img2/xls.png'),
        ppt: require('@/pages/images/img2/ppt.png'),
        pdf: require('@/pages/images/img2/pdf.png'),
        video: require('@/pages/images/img2/vedio.png'),
        file: require('@/pages/images/img2/file.png'),
      },
      showCollapse: false
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'name', 'role']),
    showClass() {
      if (this.showCollapse) {
        return 'flex_grid'
      } else {
        return 'flex_none'
      }
    }
  },
  methods: {
    //判断文件类型
    isAssetTypeAnImage(url) {
      let i = url.lastIndexOf('.'),
          ext = '';
      if (i > -1) {
        ext = url.substring(i + 1);
      }
      //图片
      if (
          [
            'png',
            'jpg',
            'jpeg',
            'bmp',
            'gif',
            'webp',
            'psd',
            'svg',
            'tiff',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.pic;
      } else if (['doc', 'docx'].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.word;
      } else if (['xls', 'xlsx',].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.xlsx;
      } else if (['pdf',].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.pdf;
      } else if (['ppt', 'pptx'].indexOf(
          ext.toLowerCase()
      ) !== -1
      ) {
        return this.icons.ppt;
      } else if (
          [
            'mp4',
            'flv',
            'm3u8',
            'rtmp',
            'hls',
            'rtsp',
            'mp3',
            'flac',
            'm4a',
            'ogg',
            'ape',
            'amr',
            'wma',
            'wav',
            'aac',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.video;
      } else {
        return this.icons.file;
      }
    },
    handleView(item) {
      let watermarkTxt = '';
      if (this.role.role_type == 'stu') {
        watermarkTxt = this.name;
      } else {
        watermarkTxt = this.name + ' ' + this.userInfo.phone;
      }
      window.open(process.env.VUE_APP_KFILE_URL +
          encodeURIComponent(base64.encode(item.ossFileUrl)) + '&watermarkTxt=' + encodeURIComponent(getAesString(watermarkTxt,
              generateRandomStr(16), generateRandomStr(16))))
      // var type = item.ossFileUrl.substring(item.ossFileUrl.lastIndexOf(".") + 1);
      // if (type == 'docxx') {//|| type == 'doc'
      //   let fileUrl = (base64.encode(item.ossFileUrl))
      //   const routeUrl = this.$router.resolve({
      //     path: '/file/view',
      //     query: {
      //       url: fileUrl
      //     }
      //   });
      //   window.open(routeUrl.href, '_blank')
      // } else {
      //   window.open('https://whkj.wuhousmartedu.com/online-preview/onlinePreview?url=' + encodeURIComponent(base64.encode(item.ossFileUrl)))
      // }
    },
    downloadSingleFile(item) {
      downloadFile(item.ossFileUrl, item.originalFileName)
      // window.open(item.ossFileUrl + `?attname=` + item.originalFileName, '_self')
    },
    changeStatus() {
      this.showCollapse = !this.showCollapse;
    }
  }
}
</script>
<style lang="scss">

</style>
