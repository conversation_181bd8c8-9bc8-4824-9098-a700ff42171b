
.next_tree_node--label {
    font-size: 14px;
}

.next-tree-item {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    transition: 0.2s;
    position: relative;
    overflow: hidden;
}

.next-tree-item:hover {
    background: #F7FAFF;
}

.next-tree-item.border {
    border-bottom: 1px solid rgba(204, 204, 204, 0.2);
}

.next-tree-item.show {
    height: 40px;
    opacity: 1;
}

.next-tree-item.disabled {
    color: #ccc !important;
}

.next-tree-item.showchild:before {
    transform: rotate(90deg);
}

.next-tree-item.last:before {
    opacity: 0;
}

.next-tree-item .parent-horizontal-line {
    width: 1px;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0px;
    box-sizing: border-box;
    background-color: rgba(204, 204, 204, 0.9);
}


.next-tree-check {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.next-tree-check-no {
    cursor: pointer;
    display: inline-block;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    box-sizing: border-box;
    width: 14px;
    height: 14px;
    background-color: #fff;
    z-index: 1;
}

.next-tree-check-no:hover {
    border: 1px solid #409eff;
}

.next-tree-label {
    flex: 1;
    display: flex;
    align-items: center;
    height: 100%;
    line-height: 1.2;
}

.next-tree-icon {
    width: 13px;
    height: 13px;
    margin-right: 4px;
}
