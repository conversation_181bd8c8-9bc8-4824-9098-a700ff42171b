<template>
  <div>
    <Header @refresh="refresh"></Header>
    <div style="padding-top: 75px"></div>
    <div class="fs_16 sub-page-container" v-loading="vLoading">
      <div>
        <el-backtop :bottom="150" :right="backTopRight">
          <div style="width: 40px;height:40px" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
            <img v-if="!isPointer" src="@/pages/assets/images/OA/backtop.png">
            <img v-if="isPointer" src="@/pages/assets/images/OA/backtioSelct.png">
          </div>
        </el-backtop>
      </div>
      <!--    被驳回-->
      <div v-if="info.status==='2'">
        <div v-for="(item,index) in info.approveRecords" :key="index"
             class="reject-box fs_14 bg_fff m_b_20 br_5 p_20 box_shadow">
          <div class="reject-content bg_f0 p_20 br_5">
            <div class="flex_center ">
              <div>
                <span class="c_666">驳回人：</span>{{ item.userName }}
              </div>&nbsp;&nbsp;&nbsp;&nbsp;
              <div>
                <span class="c_666">驳回时间：</span>{{ parseTime(item.createTime, '') }}
              </div>
            </div>
            <div class="m_t_10">
              <div class="c_666">修改意见：</div>
              <div class="m_t_10 bg_fff p_10 br_5">{{ item.advice }}</div>
            </div>
          </div>
        </div>

      </div>
      <div :class="(info.status==='2'&&type==='1')?'opacity_6':''" class="top-container box_shadow">
        <div class="title m_t_20" style="display: flex;align-items: center">
          <img v-if="type==='0'&&info.nodeType==='1'" style="width:16px;height:16px;margin-right: 2px"
               src="@/pages/assets/images/OA/forwad_icon.png">
          <div>{{ info.noticeTitle }}</div>

        </div>
        <div class="m_t_20 top-container-center">
          <div class="tip-box ">
            <div>
              <div class="flex_center ">
                <div class="flex_center">
                  <div class="c_tip  username">发起人：</div>
                  <my-tooltip style="width: 160px" :title="info.createBy"></my-tooltip>
                  <!--                <div style="width: 140px">{{ info.createBy }}</div>-->
                </div>
                <div class="flex_center m_l_10">
                  <div class="c_tip username">发文单位：</div>
                  <div>{{ info.agencyName }}/{{ info.createBy }}</div>
                </div>
              </div>
              <div class="flex_center" v-if="info.forwarder">
                <div class="flex_center">
                  <div class="c_tip username">转发人：</div>
                  <div style="width: 160px">{{ info.forwarder }}</div>
                </div>
                <div class="flex_center m_l_10">
                  <div class="c_tip username">转发时间：</div>
                  <div>{{ info.forwarderTime }}</div>
                </div>
              </div>
              <div class="flex_center ">
                <div class="flex_center ">
                  <div class="c_tip username">发文时间：</div>
                  <div style="width: 160px">{{ dayjs()(info.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                </div>
                <div class="flex_center m_l_10">
                  <div class="c_tip username">公文类型：</div>
                  <div>
                    <dict-tag
                        :options="dict.type.notice_type"
                        :value="info.noticeType"/>
                  </div>
                </div>

              </div>
            </div>
            <div class="flex_center " v-if="info.numberFlag == 1">
              <div class="c_tip username2">文号</div>
              <span class="c_tip">：</span>
              <div>
                {{ info.numberAgency }} 【{{ info.numberYear }}】{{ info.numberRef }}号
              </div>
            </div>
          </div>
          <!--  我收到的-->
          <div v-if="type==='0'&&info.nodeType!=='2'">
            <div class="tag" v-if="type==='0'&&info.signFlag==='0'">待签收</div>
            <div class="reject-box" v-if="info.status==='1'&&info.signFlag==='1'">已签收</div>
          </div>
          <!--      v-if="type==='1'"  我发起的-->
          <div v-if="false">
            <div class="reject-box" v-if="info.status==='0'">审核中</div>
            <div class="reject-box" v-if="info.status==='2'">已驳回</div>
            <div class="reject-box" v-if="info.status==='3'">已撤销</div>
          </div>
          <!--我审核的-->
          <div v-if="type==='2'">
            <div class="tag" v-if="info.status==='0'">待审核</div>
            <div class="tag" v-if="info.status==='1'">已通过</div>
            <div class="reject-box" v-if="info.status==='2'">已驳回</div>
          </div>
          <div class="">
            <div class="c_tip username">正文内容：</div>
            <div class="notice-content" style="word-wrap:break-word;word-break:normal; ">{{ info.noticeCon }}
            </div>
          </div>
        </div>
        <div class="m_t_20 fs_14" v-if="info.fileList&&info.fileList.length!==0">
          <div class="c_tip m_t_10">附件：</div>
          <div v-for="item in info.fileList" :key="item.fileId" style="width: 100%;" class="file-item">
            <div class="flex_center" style="width: 80%">
              <img style="width:35px;height:35px" :src="isAssetTypeAnImage(item.fileUrl)">
              <div class="fs_14 " style="width: calc(100% - 35px)">
                {{ item.fileName }}
              </div>
            </div>
            <div class="flex_center" style="width: 20%;justify-content: flex-end">
              <div class="pointer btn_box" @click="viewFile(item.fileUrl)">
                <i class="el-icon-document"></i>&nbsp;
                <span>预览</span>
              </div>&nbsp;&nbsp;&nbsp;&nbsp;
              <div class="pointer btn_box" @click="downloadFile(item)">
                <i class="el-icon-download"></i>&nbsp;
                <span>下载</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="display: flex;align-items: center;justify-content: flex-end">
        <!--        我收到的-->
        <div v-if="type==='0'&&info.nodeType!=='2'" class="m_t_20 flex_alAndJsCenter">
          <div class="pub-btn c_fff bg_linear pointer click_opacity"
               v-if="info.signFlag==='0'"
               @click="handleGet">确认签收
          </div>&nbsp;&nbsp;
          <div class="pub-btn  c_fff bg_linear pointer" @click="handleRelay">转发</div>&nbsp;
          <div class="pub-btn c_333 bg_f0 pointer"
               v-if="info.type==='1'"
               @click="feedBack">活动反馈
          </div>
        </div>
        <!--        我发起的-->
        <div v-if="type==='1'" class="m_t_20 flex_alAndJsCenter">
          <div v-if="info.status!=='3'" class="pub-btn c_revoke bg_revoke click_opacity pointer m_r_10"
               @click="handleCancel">撤销
          </div>
          <div v-if="info.status==='1'" class="pub-btn c_fff bg_linear pointer click_opacity" @click="handleRelay">
            转发
          </div>&nbsp;
          <!--        <div v-if="info.status==='0'"-->
          <!--             class="pub-btn c_fff bg_linear pointer"-->
          <!--             @click="handleRemind"-->
          <!--        >提醒审核-->
          <!--        </div>-->
        </div>
        <!--        我审核的-->
        <div v-if="type==='2'" class="m_t_20 flex_alAndJsCenter">
          <div class="revoke-btn bg_fff c_revoke" v-if="info.status==='0'" @click="handleReject">驳回</div>&nbsp;&nbsp;
          <div class="pub-btn c_fff bg_linear pointer" v-if="info.status==='0'" @click="handlePass">通过</div>
        </div>
      </div>

      <div v-if="type==='1'" :class="(info.status==='2'&&type==='1')?'opacity_6':''"
           class="center-container m_t_20 box_shadow">
        <div class="fs_18 fs_bold accept_uni">接收单位</div>
        <div class="flex_c m_t_10 m_b_20">
          <div class="flex_alAndJsCenter">
            <div class="flex_center">
              <div class="m_r_20 pointer" style="height:25px;"
                   :class="currentInx===0?'is-click':'un-click'"
                   @click="changItem(0)"
              > 应签收({{ countParams.receiveCount ? countParams.receiveCount : countParams.receivePersonCount }})
              </div>
              <div class="m_r_20 pointer" style="height:25px;"
                   :class="currentInx===1?'is-click':'un-click'"
                   @click="changItem(1)"
              > 已签收({{ countParams.signedCount ? countParams.signedCount : countParams.signedPersonCount }})
              </div>
              <div class="m_r_20 pointer" style="height:25px;"
                   :class="currentInx===2?'is-click':'un-click'"
                   @click="changItem(2)"
              > 未签收({{ countParams.unSignedCount ? countParams.unSignedCount : countParams.unSignedPersonCount }})
              </div>
              <div v-if="info.type==='3'" class="pointer" style="height:25px;"
                   :class="currentInx===3?'is-click':'un-click'"
                   @click="changItem(3)"
              > 材料收集
              </div>
            </div>
          </div>
          <div style="display: flex;align-items: center;">
            <div class="search-box m_r_10">
              <div class="input-box fs_14">
                <input v-model="params.keyword" class="input" type="text"
                       @keyup.enter="handleQuery"
                       placeholder="请输入关键字搜索"/>
                <i style="font-size: 16px" class="el-icon-refresh c_999 pointer" @click="clearQuery"></i>&nbsp;
              </div>
              <div class="btn-class" @click="handleQuery">查询</div>
            </div>
            <div v-if="info.status==='1'&&currentInx!==3" @click="postNoticeSignMsg"
                 class="c_fff flex_alAndJsCenter fs_14 br_5 pointer remind_box">
              <img src="@/pages/images/img2/remind.png">&nbsp;
              <div>全部提醒</div>
            </div>
            <div v-if="currentInx===3" class="flex_c">
              <div v-if="updateTimeZip" class="fs_12 c_999 m_r_10">
                最新打包时间:{{ parseTime(updateTimeZip) }}
              </div>
              <div @click="handlePackage" :disabled="status"
                   class="c_fff flex_alAndJsCenter fs_14 br_5 pointer remind_box">
                <i class="el-icon-s-cooperation"></i>&nbsp;
                <div>打包</div>
              </div>&nbsp;&nbsp;
              <div @click="packAgeDownload"
                   class="c_fff flex_alAndJsCenter fs_14 br_5 pointer remind_box">
                <i class="el-icon-download"></i>&nbsp;
                <div>下载</div>
              </div>
            </div>
          </div>

        </div>
        <div v-loading="vLoadingUnit">

          <!--        区管理-->
          <div v-if="isMultiple">
            <!--        应签收-->
            <div v-show="currentInx===0">
              <div v-for="(item,index) in receiveUnitList" :key="item.deptId">
                <!--              <collapse @open="handleOpen" :collpaseInfo="item" :type="currentInx"-->
                <!--                        :content-type="info"-->
                <!--                        @success="loadSuccess"></collapse>-->
                <collapse-receive :current-inx="index" :is-open="isOpen" :collpaseInfo="item" :type="currentInx"
                                  :content-type="info"
                                  @success="loadSuccess"></collapse-receive>
              </div>
              <div class="paging" style="text-align: center" v-if="receiveUnitList.length!==0">
                <div style="width: 160px"></div>
                <el-pagination
                    :page-size="params.pageSize"
                    background
                    :total="unitTotal"
                    layout="prev, pager, next, jumper"
                    :current-page="params.pageNum"
                    @current-change="handleCurrentChange"></el-pagination>
                <div class="page--left">
                  <div style>共{{ unitTotal }}条</div>&nbsp;&nbsp;

                  <el-select v-model="currentPage" style="width: 108px;" @change="changePageList">
                    <el-option v-for="(item,index) in pages"
                               :key="index"
                               :label="item.label"
                               :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div v-if="receiveUnitList.length===0">
                <el-empty></el-empty>
              </div>
            </div>
            <!--        未签收-->
            <div v-if="currentInx===1">
              <div v-for="(item,index) in receiveUnitList" :key="item.deptId">
                <!--              <collapse @open="handleOpen" :collpaseInfo="item" :type="currentInx"-->
                <!--                        :content-type="info"-->
                <!--                        @success="loadSuccess"></collapse>-->
                <collapse-receive :current-inx="index" :is-open="isOpen" :collpaseInfo="item" :type="currentInx"
                                  :content-type="info"
                                  @success="loadSuccess"></collapse-receive>
              </div>
              <div class="paging" style="text-align: center" v-if="receiveUnitList.length!==0">
                <div style="width: 160px"></div>
                <el-pagination
                    :page-size="params.pageSize"
                    background
                    :total="unitTotal"
                    layout="prev, pager, next, jumper"
                    :current-page="params.pageNum"
                    @current-change="handleCurrentChange"></el-pagination>
                <div class="page--left">
                  <div style>共{{ unitTotal }}条</div>&nbsp;&nbsp;

                  <el-select v-model="currentPage" style="width: 108px;" @change="changePageList">
                    <el-option v-for="(item,index) in pages"
                               :key="index"
                               :label="item.label"
                               :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div v-if="receiveUnitList.length===0">
                <el-empty></el-empty>
              </div>
            </div>
            <!--        已签收-->
            <div v-if="currentInx===2">
              <div v-for="(item,index) in receiveUnitList" :key="item.deptId">
                <!--              <collapse @open="handleOpen" :collpaseInfo="item" :type="currentInx" :content-type="info"-->
                <!--                        @success="loadSuccess"></collapse>-->
                <collapse-receive :current-inx="index" :is-open="isOpen" :collpaseInfo="item" :type="currentInx"
                                  :content-type="info"
                                  @success="loadSuccess"></collapse-receive>
              </div>
              <div class="paging" style="text-align: center" v-if="receiveUnitList.length!==0">
                <div style="width: 160px"></div>
                <el-pagination
                    :page-size="params.pageSize"
                    background
                    :total="unitTotal"
                    layout="prev, pager, next, jumper"
                    :current-page="params.pageNum"
                    @current-change="handleCurrentChange"></el-pagination>
                <div class="page--left">
                  <div style>共{{ unitTotal }}条</div>&nbsp;&nbsp;

                  <el-select v-model="currentPage" style="width: 108px;" @change="changePageList">
                    <el-option v-for="(item,index) in pages"
                               :key="index"
                               :label="item.label"
                               :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div v-if="receiveUnitList.length===0">
                <el-empty></el-empty>
              </div>
            </div>
            <!--        材料收集-->
            <div v-if="currentInx===3">
              <div v-for="(item,index) in receiveUnitList" :key="item.deptId">
                <collapse-receive-file :current-inx="index" :is-open="isOpen" :collpaseInfo="item" :type="currentInx"
                                       :content-type="info"></collapse-receive-file>
                <!--              <collapse-file :collpaseInfo="item" :type="currentInx" :content-type="info"></collapse-file>-->
              </div>
              <div class="paging" style="text-align: center" v-if="receiveUnitList.length!==0">
                <div style="width: 160px"></div>
                <el-pagination
                    :page-size="params.pageSize"
                    background
                    :total="unitTotal"
                    layout="prev, pager, next, jumper"
                    :current-page="params.pageNum"
                    @current-change="handleCurrentChange"></el-pagination>
                <div class="page--left">
                  <div style>共{{ unitTotal }}条</div>&nbsp;&nbsp;

                  <el-select v-model="currentPage" style="width: 108px;" @change="changePageList">
                    <el-option v-for="(item,index) in pages"
                               :key="index"
                               :label="item.label"
                               :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div v-if="receiveUnitList.length===0">
                <el-empty></el-empty>
              </div>
            </div>
          </div>
          <!--        其他-->
          <div v-if="!isMultiple" style="background: #F7FAFF;padding:10px;font-size: 13px">
            <!--        应签收-->
            <div v-show="currentInx===0">
              <div v-for="item in showUserAndDeptArr" :key="item.deptId">
                <receive-item :info="item" :content-type="info"></receive-item>
              </div>
              <div class="paging" style="text-align: center" v-if="receiveUnitList.length!==0">
                <div style="width: 160px"></div>
                <el-pagination
                    :page-size="params.pageSize"
                    background
                    :total="receiveUnitList.length"
                    layout="prev, pager, next, jumper"
                    :current-page="params.pageNum"
                    @current-change="unMultipleChangePage"></el-pagination>
                <div class="page--left">
                  <div style>共{{ receiveUnitList.length }}条</div>&nbsp;&nbsp;

                  <el-select v-model="currentPage" style="width: 108px;" @change="unMultipleChangePageList">
                    <el-option v-for="(item,index) in pages"
                               :key="index"
                               :label="item.label"
                               :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div v-if="receiveUnitList.length===0">
                <el-empty></el-empty>
              </div>
            </div>
            <!--        未签收-->
            <div v-if="currentInx===1">
              <div v-for="item in showUserAndDeptArr" :key="item.deptId">
                <receive-item :info="item" :content-type="info"></receive-item>
              </div>
              <div class="paging" style="text-align: center" v-if="receiveUnitList.length!==0">
                <div style="width: 160px"></div>
                <el-pagination
                    :page-size="params.pageSize"
                    background
                    :total="receiveUnitList.length"
                    layout="prev, pager, next, jumper"
                    :current-page="params.pageNum"
                    @current-change="unMultipleChangePage"></el-pagination>
                <div class="page--left">
                  <div style>共{{ receiveUnitList.length }}条</div>&nbsp;&nbsp;

                  <el-select v-model="currentPage" style="width: 108px;" @change="unMultipleChangePageList">
                    <el-option v-for="(item,index) in pages"
                               :key="index"
                               :label="item.label"
                               :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div v-if="receiveUnitList.length===0">
                <el-empty></el-empty>
              </div>
            </div>
            <!--        已签收-->
            <div v-if="currentInx===2">
              <div v-for="item in showUserAndDeptArr" :key="item.deptId">
                <receive-item :info="item" :content-type="info"></receive-item>
              </div>
              <div class="paging" style="text-align: center" v-if="receiveUnitList.length!==0">
                <div style="width: 160px"></div>
                <el-pagination
                    :page-size="params.pageSize"
                    background
                    :total="receiveUnitList.length"
                    layout="prev, pager, next, jumper"
                    :current-page="params.pageNum"
                    @current-change="unMultipleChangePage"></el-pagination>
                <div class="page--left">
                  <div style>共{{ receiveUnitList.length }}条</div>&nbsp;&nbsp;

                  <el-select v-model="currentPage" style="width: 108px;" @change="unMultipleChangePageList">
                    <el-option v-for="(item,index) in pages"
                               :key="index"
                               :label="item.label"
                               :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div v-if="receiveUnitList.length===0">
                <el-empty></el-empty>
              </div>
            </div>
            <!--        材料收集-->
            <div v-if="currentInx===3">
              <div v-for="item in showUserAndDeptArr" :key="item.deptId" class="collpaseItem m_b_10">
                <receive-file-item :info="item" :content-type="info"></receive-file-item>
                <!--                <receive-item :info="item" :content-type="info"></receive-item>-->
              </div>
              <div class="paging" style="text-align: center" v-if="receiveUnitList.length!==0">
                <div style="width: 160px"></div>
                <el-pagination
                    :page-size="params.pageSize"
                    background
                    :total="receiveUnitList.length"
                    layout="prev, pager, next, jumper"
                    :current-page="params.pageNum"
                    @current-change="unMultipleChangePage"></el-pagination>
                <div class="page--left">
                  <div style>共{{ receiveUnitList.length }}条</div>&nbsp;&nbsp;

                  <el-select v-model="currentPage" style="width: 108px;" @change="unMultipleChangePageList">
                    <el-option v-for="(item,index) in pages"
                               :key="index"
                               :label="item.label"
                               :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div v-if="receiveUnitList.length===0">
                <el-empty></el-empty>
              </div>
            </div>
          </div>
        </div>

      </div>
      <sign-dialog ref="signDialogRef"
                   :info="signInfo"
                   @forward="signForward"
                   @success="success"></sign-dialog>
      <audit-dialog ref="auditDialogRef"
                    @success="success"
                    :approve-type="approveType"
                    :title="title" :info="dialogInfo"></audit-dialog>
      <feedback ref="feedbackDialogRef"
                :info="info"
                @success="feedbackSuccess"></feedback>

      <!--   预览 图片-->
      <el-image-viewer v-if="showViewImg"
                       :on-close="closeViewer"
                       :url-list="[url]"
      ></el-image-viewer>
      <!--    转发-->
      <!--      <forward-select ref="unitRef2"-->
      <!--                      :title="title"-->
      <!--                      @confirm="unitSave"></forward-select>-->
      <!--转发2-->
      <unit-tree ref="unitRef"
                 :if-forward="isForward"
                 :is-clear="true"
                 :title="title"
                 @closeDialog="closeForward"
                 @confirm="unitSave"></unit-tree>
      <!--    公用弹窗-->
      <public-dialog ref="publicDialogRef"
                     :info="publicInfo"
                     :audit-type="publicType"
                     :tipTitle="pubTitle"
                     @submit="pubSubmit">

      </public-dialog>

      <!--  确定弹窗-->
      <pub-dialog ref="pubDialogRef"
                  :tipTitle="publicTitle"
                  @submit="publicSubmit"></pub-dialog>
      <pub-dialog ref="pubDialogRef2"
                  :tipTitle="publicTitle"
                  @submit="publicSubmit2"></pub-dialog>

      <!--    转发-->
      <transmit-dialog ref="transmitRef" :tip-title="tipTitle" :warningTip="warningTip"
                       @transmitSuccess="handleZf2"></transmit-dialog>

      <!--      压缩文件-->
      <file-dialog ref="fileRef" :url="fileUrl"
                   @confirm="fileConfirm"></file-dialog>
    </div>

  </div>
</template>

<script>
import signDialog from "@/pages/oa/components/signDialog.vue";
import Collapse from "@/pages/oa/component/collapse.vue";
import {
  forward,
  getContent,
  getReceiveUnitList,
  getZipRecord,
  handlePackageZip,
  OASignCount,
  pushNoticeSignMsg
} from "@/api/OA";
import dayjs from "dayjs";
import AuditDialog from "@/pages/oa/component/auditDialog.vue";
import Feedback from "@/pages/oa/component/feedback.vue";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer.vue'
import {parseTime} from '@/utils/tools'

// let base64 = require('js-base64').Base64
import {mapGetters} from 'vuex'
import ForwardSelect from "@/pages/oa/component/forwardSelect.vue";
import PublicDialog from "@/pages/oa/components/publicDialog.vue";
import UnitTree from "@/pages/oa/components/unitTree.vue";
import CollapseFile from "@/pages/oa/components/collapaseFolder/CollapseFile.vue";
import PubDialog from "@/pages/oa/components/pubDialog.vue";
import {Loading} from 'element-ui';
import MyTooltip from "@/components/Tooltip.vue";
import TransmitDialog from "@/pages/oa/components/transmitDialog.vue";
import Header from "@/pages/layout/pageLayout/Header.vue";
import FileDialog from "@/pages/file/components/fileDialog.vue";
import CollapseReceive from "@/pages/oa/components/collapaseFolder/collapseReceive.vue";
import CollapseReceiveFile from "@/pages/oa/components/collapaseFolder/collapseReceiveFile.vue";
import ReceiveItem from "@/pages/oa/components/collapaseFolder/receiveItem.vue";
import ReceiveFileItem from "@/pages/oa/components/collapaseFolder/receiveFileItem.vue";
import {generateRandomStr, getAesString} from "@/pages/utils/tools";

let base64 = require('js-base64').Base64
export default {
  dicts: ['notice_type'],
  components: {
    ReceiveFileItem,
    ReceiveItem,
    CollapseReceiveFile,
    CollapseReceive,
    FileDialog,
    Header,
    TransmitDialog,
    MyTooltip,
    PubDialog,
    CollapseFile,
    UnitTree,
    PublicDialog,
    ForwardSelect,
    Feedback,
    AuditDialog,
    Collapse,
    signDialog,
    ElImageViewer,
  },
  data() {
    return {
      isForward: false,
      isPointer: false,
      icons: {
        pic: require('@/pages/images/img2/pic.png'),
        word: require('@/pages/images/img2/docx.png'),
        xlsx: require('@/pages/images/img2/xls.png'),
        ppt: require('@/pages/images/img2/ppt.png'),
        pdf: require('@/pages/images/img2/pdf.png'),
        video: require('@/pages/images/img2/vedio.png'),
        file: require('@/pages/images/img2/file.png'),
      },
      tagArr: [
        {
          title: '应签收'
        },
        {
          title: '已签收'
        },
        {
          title: '未签收'
        },
        // {
        //   title: '材料列表'
        // }
      ],
      currentInx: 0,
      type: '',
      infoDetail: {},
      info: {},
      receiveUnitList: [],//接收单位
      title: '',
      dialogInfo: {},
      approveType: 0,
      signInfo: {},
      feedbackInfo: '',
      title1: '',
      receivePeoples: [],//转发人员
      userInfos: '',
      src: '',
      vLoading: false,
      showViewImg: false,
      url: '',
      docUrl: '',
      listQuery: {
        receivePeoples: [],
        agencyIds: [],
        deptIds: []
      },
      publicInfo: {},
      publicType: 0,
      pubTitle: '',
      downLoadAll: false,
      vLoadingUnit: false,
      countParams: {
        receiveCount: '',//全部单位数量
        receivePersonCount: '',//全部人数
        signedCount: '',//已签收单位数量
        signedPersonCount: '',//已签收人数
        unSignedCount: '',//未签收单位数量
        unSignedPersonCount: '',//未签收人数
      },
      params: {
        signFlag: '',
        contentId: '',
        pageNum: 1,
        pageSize: 10,
        keyword: ''
      },
      unitTotal: 0,
      publicTitle: '',
      lastFileUrl: '',
      timer: '',
      status: false,
      clickType: 0,
      updateTimeZip: '',
      warningTip: '',
      tipTitle: '',
      fileUrl: '',
      tooltipWidth: 0,
      currentPage: 1,
      pages: [
        {
          label: '10条/页',
          value: 1
        },
        {
          label: '20条/页',
          value: 2
        },
        {
          label: '30条/页',
          value: 3
        },
        {
          label: '40条/页',
          value: 4
        }
      ],
      backTopRight: 0,
      isMultiple: false,

      showStep: 1,
      showPrevStep: 0,
      showNextStep: 5,
      showStepAll: false,
      total: 0,
      isOpen: false
    }
  },
  computed: {
    ...mapGetters(['role', 'userInfo', 'name']),
    tagStyle() {
      if (this.type === '0' || this.type === '2') {
        return '#1B77F5'
      } else {
        return '#CCCCCC'
      }
    },
    showUserAndDeptArr() {
      let reminder = this.receiveUnitList.length % this.params.pageSize;
      let totalStep;
      if (reminder !== 0) {
        totalStep = this.receiveUnitList.length / this.params.pageSize
      } else {
        totalStep = (this.receiveUnitList.length / this.params.pageSize) + 1;
      }
      if (this.receiveUnitList.length > this.params.pageSize) {
        this.showStepAll = true;
      }
      if (this.showStep === 1) {
        if (this.receiveUnitList.length > 1 && this.receiveUnitList.length < 6) {
          return this.receiveUnitList
        } else {
          return this.receiveUnitList.slice(0, this.params.pageSize)
        }
      } else {
        return this.receiveUnitList.slice(this.showPrevStep, this.showNextStep);
      }
    }
  },
  mounted() {
    this.backTopRight = Math.floor((((window.innerWidth - 1200) / 2) * 2) / 3);
    this.type = this.$route.query.type
    this.loadData()
  },
  methods: {
    parseTime,
    dayjs() {
      return dayjs
    },
    // 公文详情
    async loadData() {
      this.vLoading = true;
      let id = this.$route.query.id;
      let params = {
        agencyId: this.role.agency_id
      }
      getContent(id, params).then(res => {
        if (res.code == 200) {
          this.info = res.data;
          if (this.type === '1') {
            this.params.contentId = res.data.contentId;
            this.getReceiveUnitLists(res.data.contentId);
            this.getOASignCount(res.data.contentId)
          }
        }
      }).finally(() => {
        this.vLoading = false
      })
    },
    // 接收单位
    getReceiveUnitLists(id) {
      this.receiveUnitList = [];
      if (this.currentInx === 1) {
        this.params.signFlag = '1'
      } else if (this.currentInx === 2) {
        this.params.signFlag = '0'
      } else {
        this.params.signFlag = ''
      }
      this.vLoadingUnit = true;
      let that = this;
      getReceiveUnitList(this.params).then(res => {
        if (res.code == 200) {
          if (res.rows.length > 1) {
            that.isMultiple = true;
            that.receiveUnitList = res.rows;
            that.unitTotal = res.total;
          } else if (res.rows.length === 1) {
            that.isMultiple = false;
            let obj = res.rows[0];
            let arr = JSON.parse(JSON.stringify(obj.userList));
            obj.deptVOS.forEach((item, index) => {
              arr = [...arr, ...item.userList]
            })
            this.total = arr.length
            this.receiveUnitList = arr;
            // this.unitTotal = res.total;
          }
        }

      }).finally(() => {
        this.vLoadingUnit = false;
      })
    },
    handleCurrentChange(page) {
      if (this.isMultiple) {
        this.params.pageNum = page;
        this.getReceiveUnitLists();
      }
    },
    unMultipleChangePage(page) {
      this.showStep = page;
      let reminder = this.receiveUnitList.length % this.params.pageSize;
      let totalStep;
      if (reminder == 0) {
        totalStep = this.receiveUnitList.length / this.params.pageSize
      } else {
        totalStep = Math.floor(this.receiveUnitList.length / this.params.pageSize) + 1;
      }
      if (this.showStep <= totalStep) {
        this.showPrevStep = this.params.pageSize * (this.showStep - 1);
        this.showNextStep = this.showPrevStep + this.params.pageSize;
      }
    },
    unMultipleChangePageList(val) {
      this.params.pageNum = 1;
      this.params.pageSize = 10;
      this.params.pageSize = this.params.pageSize * val;
      this.getReceiveUnitLists();
    },
    changePageList(val) {
      this.params.pageNum = 1;
      this.params.pageSize = 10;
      this.params.pageSize = this.params.pageSize * val;
      this.getReceiveUnitLists();
      // this.params.pageNum=val;
    },
    // 全部提醒·
    postNoticeSignMsg() {
      this.$confirm('确定发起提醒吗？', '提示', {
        type: 'warning',
      }).then(() => {
        let params = {
          isAll: true,
          contentId: this.info.contentId
        };
        pushNoticeSignMsg(params).then(res => {
          if (res.code == 200) {
            this.$message({
              message: '操作成功', type: 'success'
            })
            this.loadData()
          }
        })
      })

    },
    handleOpen() {
    },
    // 打包
    handlePackage() {
      // 打包
      handlePackageZip(this.info.contentId).then((res) => {
        if (res.code == 200) {
          this.checkGetZipStatus();
          this.clickType = 0;
        }
      }).finally(() => {
      })
    },
    // 检测状态并赋值
    checkStatus() {
      getZipRecord(this.info.contentId).then(res => {
        if (res.data && res.data.url) {
          this.updateTimeZip = res.data.updateTime
          this.status = true;
        } else {
          this.status = false;
        }
      })
    },
    // 检测状态
    checkGetZipStatus() {
      let downloadLoadingInstance = Loading.service({
        text: '正在打包，请稍候',
        spinner: 'el-icon-loading',
        lock: true,
        background: 'rgba(0, 0, 0, 0.7)',
      });
      let time = 20;
      let timer = setInterval(() => {
        if (time === 0) {
          downloadLoadingInstance.close();
          clearInterval(timer);
        } else {
          time--;
          getZipRecord(this.info.contentId).then(res => {
            if (res.code == 200) {
              if (res.data && res.data.url) {
                // 正在打包
                this.status = true;
                clearInterval(timer);
                downloadLoadingInstance.close();
                if (res.data.status == '0') {
                  this.$refs.pubDialogRef.showDialog = true;
                  this.clickType = 1;//下载上一次
                  this.publicTitle = '新的材料正在打包中，是否下载上一次压缩包？';
                  this.lastFileUrl = res.data.url;
                } else if (res.data.status == '1') {
                  this.updateTimeZip = res.data.updateTime;
                  window.open(res.data.url, '_self');
                }
              } else {
                if (time == 1 && res.data && !res.data.url && res.data.status === '0') {
                  downloadLoadingInstance.close();
                  this.$message({
                    message: '当前打包文件过大，完成打包需要时间,请耐心等待....',
                    type: 'warning'
                  })
                }
              }
            }
          })
        }
      }, 1000)
    },
    // 确定
    publicSubmit() {
      //下载上一次
      if (this.clickType == 1) {
        window.open(this.lastFileUrl, '_self');
        this.lastFileUrl = '';
        this.clickType = 0;
      } else if (this.clickType == 2) {
        this.handlePackage()
      }
    },
    publicSubmit2() {

    },
    // 下载全部
    packAgeDownload() {
      // console.log(this.clickType)
      getZipRecord(this.info.contentId).then(res => {
        if (res.data && res.data.url) {
          if (res.data.status == '0') {
            this.$refs.pubDialogRef.showDialog = true;
            this.clickType = 1;//下载上一次
            this.publicTitle = '新的材料正在打包中，是否下载上一次压缩包？';
            this.lastFileUrl = res.data.url;
          } else if (res.data.status == '1') {
            this.updateTimeZip = res.data.updateTime;
            window.open(res.data.url, '_self');
          }
        } else if (!res.data) {
          this.$refs.pubDialogRef.showDialog = true;
          this.clickType = 2;
          this.publicTitle = '检测到还未完成打包，是否现在打包？'
        } else if (res.data && !res.data.url) {
          this.$message({
            message: '打包还未完成，请耐心等待，稍后再试...',
            type: 'warning'
          })
        }
      })
      // this.$download.zip('/oa/content/' + this.info.contentId + '/download-zip', '', '打包文件汇总')
    },
    // 下载所有文件
    downLoadAllFile() {
      this.downLoadAll = true;
      this.pubTitle = '是否打包下载所有材料？';
      this.publicType = 2
      this.$refs.publicDialogRef.showDialog = true;
    },
    // 公用弹窗确定
    pubSubmit() {
      if (this.downLoadAll) {
        this.packAgeDownload()
      } else {
        this.loadData()
      }
      this.downLoadAll = false;
    },
    // 签收统计
    getOASignCount(id) {
      let params = {
        contentId: id
      }
      OASignCount(params).then(res => {
        if (res.code === 200) {
          this.countParams = res.data;
        }
      })
    },
    getTitleWidth(wid) {
      this.tooltipWidth = wid;
    },
    isAssetTypeAnImage(url) {
      let i = url.lastIndexOf('.'),
          ext = '';
      if (i > -1) {
        ext = url.substring(i + 1);
      }
      //图片
      if (['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff',].indexOf(ext.toLowerCase()) !== -1
      ) {
        return this.icons.pic;
      } else if (['doc', 'docx'].indexOf(ext.toLowerCase()) !== -1) {
        return this.icons.word;
      } else if (['xls', 'xlsx',].indexOf(ext.toLowerCase()) !== -1) {
        return this.icons.xlsx;
      } else if (['pdf',].indexOf(ext.toLowerCase()) !== -1) {
        return this.icons.pdf;
      } else if (['ppt', 'pptx'].indexOf(ext.toLowerCase()) !== -1) {
        return this.icons.ppt;
      } else if ([
        'mp4',
        'flv',
        'm3u8',
        'rtmp',
        'hls',
        'rtsp',
        'mp3',
        'flac',
        'm4a',
        'ogg',
        'ape',
        'amr',
        'wma',
        'wav',
        'aac',
      ].indexOf(ext.toLowerCase()) !== -1) {
        return this.icons.video;
      } else {
        return this.icons.file;
      }
    },
    // 搜索
    handleQuery() {
      if (this.params.keyword) {
        this.isOpen = true;
      }
      this.queryGetUnitLists()
    },
    queryGetUnitLists() {
      this.receiveUnitList = [];
      if (this.currentInx === 1) {
        this.params.signFlag = '1'
      } else if (this.currentInx === 2) {
        this.params.signFlag = '0'
      } else {
        this.params.signFlag = ''
      }
      this.vLoadingUnit = true;
      let that = this;
      getReceiveUnitList(this.params).then(res => {
        if (res.code == 200) {
          if (this.isMultiple) {
            that.receiveUnitList = res.rows;
            that.unitTotal = res.total;
          } else {
            if (res.rows.length === 1) {
              let obj = res.rows[0];
              let arr = JSON.parse(JSON.stringify(obj.userList));
              obj.deptVOS.forEach((item, index) => {
                arr = [...arr, ...item.userList]
              })
              this.total = arr.length
              this.receiveUnitList = arr;
            }
          }
        }
      }).finally(() => {
        this.vLoadingUnit = false;
      })
    },
    clearQuery() {
      this.isOpen = false;
      this.params.keyword = '';
      this.queryGetUnitLists()
    },
    refresh() {
      this.$router.back()
    },
    // 提醒回调
    loadSuccess() {
    },
    //判断文件是否为图片
    isImage(url) {
      let i = url.lastIndexOf('.'),
          ext = '';
      if (i > -1) {
        ext = url.substring(i + 1);
      }
      //图片
      if (
          [
            'png',
            'jpg',
            'jpeg',
            'bmp',
            'gif',
            'webp',
            'psd',
            'svg',
            'tiff',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return 1;
      } else if (
          ['doc', 'xls', 'ppt', 'pdf', 'docx', 'xlsx', 'pptx'].indexOf(
              ext.toLowerCase()
          ) !== -1
      ) {
        return 2;
      } else if (
          [
            'mp4',
            'flv',
            'm3u8',
            'rtmp',
            'hls',
            'rtsp',
            'mp3',
            'flac',
            'm4a',
            'ogg',
            'ape',
            'amr',
            'wma',
            'wav',
            'aac',
          ].indexOf(ext.toLowerCase()) !== -1
      ) {
        return 3;
      } else {
        return 1000;
      }
    },
    fileConfirm() {
    },
    // 预览
    viewFile(url) {
      let watermarkTxt = ''
      if (this.role.role_type == 'stu') {
        watermarkTxt = this.name;
      } else {
        watermarkTxt = this.name + this.userInfo.phone;
      }
      window.open(process.env.VUE_APP_KFILE_URL +
          encodeURIComponent(base64.encode(url)) + '&watermarkTxt=' + encodeURIComponent(getAesString(watermarkTxt,
              generateRandomStr(16), generateRandomStr(16))))
    },
    // 图片关闭
    closeViewer() {
      this.showViewImg = false
    },
    // 下载
    downloadFile(item) {
      let params = {
        contentId: item.contentId,
        fileId: item.fileId
      }
      // oaDownload(params).then(res => {
      //   console.log(res)
      // })
      this.$download.file(item.fileName, '', '/oa/file/download', params)
      // downloadFile(item.fileUrl, item.fileName)
      // window.open(item.fileUrl + '?attname=' + item.fileName, '_self')
    },
    changItem(inx) {
      this.currentInx = inx;
      this.params.pageNum = 1;
      this.params.pageSize = 10;
      this.isOpen = false;
      this.params.keyword = ''
      this.queryGetUnitLists()
      // this.getReceiveUnitLists(this.info.contentId);
      if (inx == 3) {
        this.checkStatus()
      }
      this.currentPage = 1;
    },
    // 撤销
    handleCancel() {
      this.publicType = 1
      let infos = JSON.parse(JSON.stringify(this.info));
      infos.status = 3;
      this.pubTitle = '是否撤销公文' + "'" + this.info.noticeTitle + "'" + '？';
      this.publicInfo = infos;
      this.$refs.publicDialogRef.showDialog = true;
    },
    // 提醒审核
    handleRemind() {
    },
    // 通过
    handlePass() {
      this.dialogInfo = this.info
      this.title = '通过'
      this.approveType = 0
      this.$refs.auditDialogRef.showDialog = true
    },
    // 驳回
    handleReject() {
      this.dialogInfo = this.info
      this.title = '驳回'
      this.approveType = 1
      this.$refs.auditDialogRef.showDialog = true
    },
    success() {
      this.loadData()
    },
    // 签收并转发
    signForward() {
      this.title = '接收单位选择';
      this.$refs.unitRef.showDialog = true
    },
    // 转发
    handleRelay() {
      this.title = '接收单位选择';
      this.isForward = true;
      this.$refs.unitRef.showDialog = true
    },
    closeForward() {
      // console.log('close')
      this.loadData()
    },
    unitSave(data) {
      this.listQuery.receivePeoples = []
      let userList = _.clone(data)
      let showLabel = ''
      if (userList.deptIds.length !== 0) {
        userList.deptIds.forEach((item, index) => {
          if (index === userList.deptIds.length - 1) {
            showLabel += item.label
          } else {
            showLabel += item.label + ','
          }
        })
      }
      if (userList.receivePeoples.length !== 0) {
        userList.receivePeoples.forEach((item, index) => {
          if (index === userList.receivePeoples.length - 1) {
            showLabel += item.label
          } else {
            showLabel += item.label + ','
          }
        })
        this.listQuery.receivePeoples = userList.receivePeoples.map(item => {
          return {
            signBy: item.userId,
            deptId: item.deptId || '',
            agencyId: item.agencyId,
            stageId: item.stageId || ''
          };
        })
      }
      this.userInfos = showLabel
      // this.$refs.transmitRef.showDialog = true;
      // this.tipTitle = '是否转发给' + this.userInfo + '?';
      this.handleZf()
    },
    // 反馈
    feedBack() {
      if (this.info.signFlag === '0') {
        this.$message({
          message: '请先签收当前公文',
          type: 'warning'
        })
        return
      }
      this.$refs.feedbackDialogRef.showDialog = true
    },
    feedbackSuccess() {
      this.loadData()
    },
    handleZf2() {
      let that = this
      let params = {
        contentId: that.info.contentId,
        receivePeoples: that.listQuery.receivePeoples,
        deptIds: that.listQuery.deptIds
      }
      forward(params).then(res => {
        if (res.code === 200) {
          this.$message({
            message: '转发成功',
            type: 'success',
          });
          that.$confirm(res.msg, '提示', {
            cancelButtonText: '关闭',
            showConfirmButton: false,
            type: 'success',
          }).then(() => {
          }).finally(() => {
            this.pubSubmit()
          })
        }
      })
    },
    handleZf() {
      let that = this
      this.$confirm('是否转发给' + this.userInfos + '?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let params = {
          contentId: that.info.contentId,
          receivePeoples: that.listQuery.receivePeoples,
          deptIds: that.listQuery.deptIds
        }
        forward(params).then(res => {
          if (res.code === 200) {
            this.$message({
              message: '转发成功',
              type: 'success',
            });
            this.isForward = false
            this.loadData()
          }
        })
      }).catch((action) => {
        if (action == 'cancel') {
          this.isForward = false;
        }
      })
    },
    // 签收
    handleGet() {
      this.signInfo = this.info
      this.$refs.signDialogRef.showDialog = true
    },
    handleMouseEnter() {
      this.isPointer = true;
    },
    handleMouseLeave() {
      this.isPointer = false;
    },
    // 获取屏幕高度
    getCurrentY() {
    }
  },
  beforeDestroy() {
    // window.removeEventListener('scroll', this.getCurrentY)
  }
}
</script>
<style lang="scss" scoped>
.top-container {
  padding: 20px;
  background: #fff;
  font-size: 16px;
  border-radius: 5px;
  position: relative;

  .top-container-center {
    background: #F7FAFF;
    padding: 20px;
    font-size: 14px;
    border-radius: 5px;

    .tip-box {
      border-radius: 6px;
      z-index: 100;
    }

    .notice-content {
      margin-top: 5px;
      border-radius: 6px;
      padding: 10px;
      color: #333333;
      font-size: 14px;
      text-indent: 30px;
      background: #fff
    }
  }

  .title {
    //color: #0F4444;
    font-size: 20px;
    font-weight: bold;
  }


  .tag {
    position: absolute;
    font-size: 14px;
    color: #fff;
    width: 67px;
    height: 30px;
    line-height: 28px;
    text-align: center;
    right: -3px;
    top: 0;
    background: url("@/pages/images/img2/status.png") no-repeat;
    background-size: 100% 100%;
    //border-radius: 0px 4px 4px 4px;
  }

  .reject-box {
    position: absolute;
    font-size: 14px;
    color: #fff;
    width: 67px;
    height: 30px;
    line-height: 28px;
    text-align: center;
    right: -3px;
    //border-radius: 6px;
    top: 0;
    background: url("@/pages/images/img2/pass.png");
    background-size: 100% 100%;
  }

  .file-item {
    margin-top: 10px;
    border-radius: 6px;
    background: #F7FAFF;
    padding: 10px;
    color: #666666;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn_box {
      background: #F0F0F0;
      border-radius: 5px;
      padding: 5px 15px;
    }

    .btn_box:active {
      opacity: .7;
    }

    .btn_box:hover {
      color: #4C94F7;
    }
  }

}

.reject {
  background: #fff !important;
  border-color: #FD8080 !important;
  color: #FD8080 !important;
  font-family: "微软雅黑", Arial;
}

.get {
  background: #fff !important;
  border-color: #3888F7 !important;
  color: #3888F7 !important;
  font-family: "微软雅黑", Arial, sans-serif;
}

.pass {
  border-color: #3888F7 !important;
  background: #3888F7 !important;
  color: #fff !important;
  font-family: "微软雅黑", Arial, sans-serif;
}

.center-container {
  font-size: 16px;
  background: #fff;
  border-radius: 6px;
  padding: 20px;

  .accept_uni {
    position: relative;
    padding-left: 10px;
  }

  .accept_uni:before {
    position: absolute;
    content: '';
    top: 3px;
    left: 0;
    height: 80%;
    width: 2px;
    border-radius: 4px;
    background: #3888F7;
  }

  .remind_box {
    width: 100px;
    height: 34px;
    background: #1B77F5;
  }

  .remind_box:active {
    opacity: .7;
  }

  .un-click {
    color: #999999
  }

  .is-click {
    position: relative;
    //font-weight: bold;
    color: #3888F7;
  }

  .is-click:before {
    content: "";
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 100%;
    border-radius: 5px;
    border-bottom: 3px solid #3888F7;
    box-sizing: border-box;
    opacity: .9;
  }
}

.disabled {
  border-color: rgb(169, 169, 169) !important;
  background: rgb(169, 169, 169) !important;
  color: #fff !important;
  font-family: "微软雅黑", Arial;;
  cursor: default;
}

.pub-btn {
  width: 80px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  user-select: none;
  box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.1);
}

.revoke-btn {
  user-select: none;
  width: 78px;
  height: 30px;
  line-height: 30px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.1);
  border: 1px solid #FC5353;
}

.paging {
  margin-top: 20px;
  padding: 0 20px;
  //text-align: center;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .page--left {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;

    ::v-deep.el-input__inner {
      height: 32px !important;
    }
  }
}

.search-box {
  width: 280px;
  font-size: 14px !important;
  height: 32px;
  margin-top: 2px;
  line-height: 32px;
  border-radius: 4px;
  border: 1px solid #1B77F5;
  display: flex;
  position: relative;

  .input-box {
    flex: 1;
    display: flex;
    align-items: center;

    .input {
      width: 100%;
      box-sizing: border-box;
      height: 30px;
      padding: 0 6px 0 10px;
      line-height: 30px;
      font-size: 14px;
      outline: none;
      border: none;

      &::-webkit-input-placeholder {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.4);
      }
    }
  }

  .btn-class {
    width: 80px;
    height: 32px;
    cursor: pointer;
    background: #1B77F5;
    text-align: center;
    color: #fff;
    line-height: 32px;
  }

}

.collpaseItem {
  background: #fff;
  padding: 10px 20px;
  border-radius: 6px;

  .is-color {
    color: #6A94FF;
  }

  .no-color {
    color: #999999
  }
}

</style>
