<template>
  <el-dialog
      class="reset-dialog"
      :visible.sync="showDialog"
      append-to-body
      :show-close="false"
      :width="'400px'"
      @open="handleOpen"
      @close="dialogClosed"
  >
    <template slot="title">
      <div class="flex_space">
        <div class="fs_bold fs_18">信息反馈</div>
        <div class="closeIcon pointer" @click="dialogClosed">
          <i class="el-icon-error icon fs_18"></i>
        </div>
      </div>
    </template>
    <div>
      <el-form inline ref="formRef" :model="params" :rules="rules">
        <el-form-item label="反馈状态：" required>
          <el-radio-group v-model="params.feedbackStatus">
            <el-radio label="0">同意</el-radio>
            <el-radio label="1">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="反馈信息：" prop="informationFeedback">
          <el-input style="width:250px"
                    :rows="4"
                    v-model="params.informationFeedback"
                    type="textarea"
                    placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div style="text-align: center">
      <el-button class="cancel" @click="dialogClosed">取消</el-button>&nbsp;&nbsp;
      <el-button class="pass" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {signContent} from "@/api/OA";

export default {
  props: {
    info: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  data() {
    return {
      showDialog: false,
      advice: "",
      params: {
        feedbackStatus: '',
        informationFeedback: '',
        contentId: ''
      },
      rules: {
        informationFeedback: [
          {
            required: true, message: '请填写反馈信息', trigger: blur
          }
        ]
      }
    }
  },
  methods: {
    handleOpen() {
      this.advice = ''
    },
    dialogClosed() {
      this.showDialog = false
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if(this.params.feedbackStatus===''){
            this.$message({
              message:'请选择反馈状态',
              type:'warning'
            })
            return
          }
          this.params.contentId=this.info.contentId
          signContent(this.params).then(res => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "反馈成功"
              })
              this.dialogClosed()
              this.$emit('success')
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.closeIcon {
  font-size: 20px;
  cursor: pointer;
  opacity: .8;
  color: rgb(112, 112, 112);
  top: 5px;
}

::v-deep {
  .el-dialog {
    border-radius: 10px !important;
  }
}

::v-deep {
  .el-dialog__body {
    padding: 0 20px 20px 20px !important;
  }
}

.cancel {
  background: #fff !important;
  border-color: #3888F7 !important;
  color: #3888F7 !important;
  font-family: "微软雅黑", Arial;
}

.pass {
  border-color: #3888F7 !important;
  background: #3888F7 !important;
  color: #fff !important;
  font-family: "微软雅黑", Arial;
}

</style>
