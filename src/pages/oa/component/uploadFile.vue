<template>
  <el-upload class="upload-demo"
             action="https://jsonplaceholder.typicode.com/posts/"
             accept=""
             ref="upload"
             :on-change="selectChange"
             :show-file-list="false"
             :http-request="httpRequest"
             multiple
             :file-list="fileList">
    <el-button>点击上传</el-button>
  </el-upload>
</template>
<script>
import axios from "axios";
import {aesDecrypt2} from "@/pages/utils/tools";
import {getSessionToken} from "@/utils/local";

export default {
  data() {
    return {
      fileList: [],
      submitData: {
        fileName: '',
        fileUrl: ''
      }
    }
  },
  methods: {
    httpRequest() {
      let that = this
      let formData = new FormData()
      this.fileList.forEach(item => {
        formData.append('file', item.raw)
      })
      const loading = this.$loading({
        lock: true,
        text: '文件上传中',
        spinner: "el-icon-loading",
        background: 'rgba(0,0,0,0.7)'
      })
      axios({
        headers: {
          Authorization: 'Bearer ' + getSessionToken(),
          'Content-Type': 'multipart/form-data'
        },
        url: process.env.VUE_APP_BASE_API + '/oa/file/put-file',
        method: 'post',
        name: 'file',
        data: formData,
        timeout: 200000000
      }).then(res => {
        if (res.data && !res.data.code && (typeof res.data == 'string')) {
          let result = JSON.parse(aesDecrypt2(res.data))
          if (result.code == 200) {
            that.$message({
              message: '上传成功',
              type: 'success'
            })
            that.submitData.fileName = result.data.originalFileName
            that.submitData.fileUrl = result.data.ossFileUrl
            loading.close()
            that.$emit('uploadSuccess', that.submitData)
            that.fileList = []
            that.submitData = {
              fileName: '',
              fileUrl: ''
            }
          }
        }
        if (res.data.code === 200) {
          // that.$message({
          //   message: '上传成功',
          //   type: 'success'
          // })
          // that.submitData.fileName = res.data.data.originalFileName
          // that.submitData.fileUrl = res.data.data.ossFileUrl
          // loading.close()
          // that.$emit('uploadSuccess', that.submitData)
          // that.fileList = []
          // that.submitData = {
          //   fileName: '',
          //   fileUrl: ''
          // }
        }
      })
    },
    selectChange(file, fileList) {
      this.fileList = fileList
    },
  }
}
</script>
<style lang="scss" scoped></style>
