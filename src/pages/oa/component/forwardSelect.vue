<template>
  <el-dialog class="reset_dialog"
             :visible.sync="showDialog"
             append-to-body
             :width="'65%'"
             :show-close="false"
             @open="handleOpen"
             @close="dialogClosed"
  >
    <template slot="title">
      <div class="flex_space">
        <div class="fs_bold">{{ title }}</div>
        <div class="closeIcon pointer" @click="dialogClosed">
          <i class="el-icon-error icon fs_18"></i>
        </div>
      </div>
    </template>
    <div>
      <!--      <div v-if="!currentType">-->
      <!--        <div class=" flex_center pointer"-->
      <!--             :class="currentType=== item.agencyId ? 'list active' : 'list'"-->
      <!--             v-for="(item,index) in agency"-->
      <!--             @click="handleClick(item)"-->
      <!--             :key="index">-->
      <!--          <img src="@/assets/images/2470.png">&nbsp;-->
      <!--          <div>{{ item.agencyName }}</div>-->
      <!--        </div>-->
      <!--      </div>-->
      <div>
        <!--        <div class="pointer back m_b_10" @click="back">返回上一级</div>-->
        <div class="flex">
          <div style="flex:1;margin-left:10px;">
            <el-input style="width:70%;margin:10px 0;"
                      v-model="filterKeyDept"
                      placeholder="输入关键字筛选部门"></el-input>
            <el-table :data="filterDeptList"
                      border
                      ref="deptRef"
                      highlight-current-row
                      @selection-change="selectChangeDept"
                      v-loading="vLoading2"
                      @select="selectRowDept"
                      :empty-text="deptEmptyText"
                      @cell-click="cellClickDept"
                      :row-key="getRowKey"
                      style="height:400px;overflow-y: auto"
                      :expand-row-keys="expand" @expand-change="expandChange">
              <el-table-column type="selection"></el-table-column>
              <el-table-column label="部门" prop="label" class="pointer">
                <template slot-scope="scope">
                  <span class="pointer">{{ scope.row.label }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div style="flex:1;margin-left:10px;">
            <el-input style="width:70%;margin:10px 0;"
                      v-model="filterKeyUser"
                      placeholder="输入关键字筛选人员"></el-input>
            <el-table :data="filterUserList"
                      border
                      ref="userRef"
                      highlight-current-row
                      @selection-change="selectChangeUser"
                      v-loading="vLoading3"
                      :empty-text="userEmptyText"
                      @select="selectRowUser"
                      :row-key="getRowKey"
                      style="height:400px;overflow-y: auto"
                      :expand-row-keys="expand" @expand-change="expandChange">
              <el-table-column type="selection"></el-table-column>
              <el-table-column label="用户" prop="label">
                <template slot-scope="scope">
                  <div class="pointer">{{ scope.row.label }}</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div style="border:1px solid #EBEEF5;
                      flex:1;margin:60px 0 0 10px;
                      height:397px;overflow-y: auto">
            <div style="border-bottom:1px solid #EBEEF5;padding:10px;" class="flex_space aligin_center">
              <div>已选择列表</div>
              <div class="pointer back " @click="clearList">清空列表</div>
            </div>
            <div class="m_t_10" style="padding:0 10px;">
              <div class="m_b_10 flex_space"
                   style="background: #fafafa;border-radius: 10px;padding:10px;"
                   v-for="(item,index) in currentSelect" :key="item.id">
                <div>{{ item.label }}</div>
                <div class="pointer" @click="deleteCurrent(item,index)">删除</div>
              </div>
              <el-empty v-if="currentSelect.length===0" description="暂无选择"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" style="text-align: center">
      <el-button class="cancel" @click="dialogClosed">取消</el-button>&nbsp;
      <el-button class="submit" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {mapGetters} from 'vuex';
import {agencyList, deptUserList, getDeptTree} from "@/api/OA";
import {split} from "lodash";

export default {
  components: {},
  props: {
    title: {
      type: String,
      default: '',
    },
    dialogWidth: {
      type: Number,
      default: 500
    }
  },
  data() {
    return {
      agency: [
        {
          agencyName: '机构内用户',
          agencyId: 1,
          type: 1
        },
        {
          agencyName: '跨机构用户',
          agencyId: 2,
          type: 2
        },
        {
          agencyName: '常用分组',
          agencyId: 3,
          type: 3
        },
      ],
      currentType: '',
      currentInx: '',
      showNext: false,
      showDialog: false,
      agencyList: [],
      expand: [],
      getRowKey(row) {
        return row.id
      },
      deptEmptyText: '点击左侧机构，查看部门',
      userEmptyText: '点击左侧部门，查看人员',
      deptList: [],
      userList: [],
      vLoading1: false,
      vLoading2: false,
      vLoading3: false,
      agencySelect: [],
      deptSelect: [],
      userSelect: [],
      currentSelect: [],
      params: {
        agencyIds: [],
        stageIds: [],
        deptIds: [],
        receivePeoples: []
      },
      filterKeyAgency: '',
      filterKeyDept: '',
      filterKeyUser: ''
    }
  },
  computed: {
    ...mapGetters(['role']),
    select() {
      return [...this.agencySelect, ...this.deptSelect, ...this.userSelect]
    },
    filterAgencyList() {
      if (!this.filterKeyAgency) {
        return this.agencyList
      } else {
        return _.filter(this.agencyList, item => {
          if (item.label.includes(this.filterKeyAgency)) {
            return item
          }
        })
      }
    },
    filterDeptList() {
      if (!this.filterKeyDept) {
        return this.deptList
      } else {
        return _.filter(this.deptList, item => {
          if (item.label.includes(this.filterKeyDept)) {
            return item
          }
        })
      }
    },
    filterUserList() {
      if (!this.filterKeyUser) {
        return this.userList
      } else {
        return _.filter(this.userList, item => {
          if (item.label.includes(this.filterKeyUser)) {
            return item
          }
        })
      }
    }
  },
  methods: {
    dialogClosed() {
      this.showDialog = false
    },
    handleClick(item) {
      this.currentType = item.agencyId
      this.currentInx = item
      this.showNext = true
      if (item.type === 2) {
        this.getAgency()
      } else if (item.type === 1) {
        this.getDeptTreeData()
      }
    },
    back() {
      this.currentType = ''
      this.currentInx = ''
    },
    handleOpen() {
      this.userEmptyText = '请先点击左侧部门，查看人员'
      this.currentType = ''
      this.currentInx = ''
      this.deptList = []
      this.currentSelect = []
      this.agencyList = []
      this.userList = []
      this.currentType = ''
      this.filterKeyUser = ''
      this.filterKeyDept = ''
      this.filterKeyAgency = ''
      this.showNext = false
      this.getDeptTreeData()
    },
    agencyTranslateTree(arr, result) {
      arr.map((item, index) => {
        result.push({
          id: item.agencyId,
          label: item.agencyName,
          type: 'agency'
        })
      });
    },
    // 跨机构获取
    async getAgency() {
      this.vLoading1 = true
      agencyList().then(res => {
        let data = res.data
        let obj = []
        this.agencyTranslateTree(data, obj)
        this.agencyList = obj;
      }).finally(() => {
        this.vLoading1 = false
      })
    },
    // 机构内用户
    getDeptTreeData() {
      getDeptTree({agencyId: this.role.agency_id}).then(res => {
        let data = res.data
        let obj = [];
        this.deptTranslateTree(data, obj);
        this.deptList = obj;
        if (this.deptList.length === 0) {
          this.deptEmptyText = '暂无数据'
        }
        this.$forceUpdate()
      }).finally(() => {
        this.vLoading2 = false
      })
    },
    // 点击机构
    async cellClick(row) {
      this.vLoading2 = true
      this.deptList = []
      getDeptTree({agencyId: row.id}).then(res => {
        let data = res.data
        let obj = [];
        this.deptTranslateTree(data, obj);
        this.deptList = obj;
        if (this.deptList.length === 0) {
          this.deptEmptyText = '暂无数据'
        }
        this.$forceUpdate()
      }).finally(() => {
        this.vLoading2 = false
      })
    },
    // 点击部门
    async cellClickDept(row) {
      let params = [row.id]
      this.vLoading3 = true
      this.userList = []
      deptUserList(params).then(res => {
        let data = res.data
        let obj = [];
        this.userTranslateTree(data, obj);
        this.userList = obj;
        if (this.userList.length === 0) {
          this.userEmptyText = '暂无数据'
        }
        this.$forceUpdate()
      }).finally(() => {
        this.vLoading3 = false
      })
      // const {data} = await deptUserList(params);

    },
    // 展开行
    async expandChange(row, expandRows) {
      if (expandRows.length) {
        this.expand = []
        if (row) {
          this.expand.push(row.id)
        } else {
          this.expand = []
        }
      }
    },
    selectChange(arr) {
      this.agencySelect = arr
    },
    // 单个选择机构
    selectRowAgency(select, row) {
      if (this.currentSelect.includes(row)) {
        for (let i = 0; i < this.currentSelect.length; i++) {
          if (this.currentSelect[i].id === row.id) {
            this.currentSelect.splice(i, 1)
          }
        }
      } else {
        this.currentSelect.push(row)
      }
    },
    selectChangeDept(arr) {
      this.deptSelect = arr
    },
    // 单个选择部门列表
    selectRowDept(select, row) {
      if (this.currentSelect.includes(row)) {
        for (let i = 0; i < this.currentSelect.length; i++) {
          if (this.currentSelect[i].id === row.id) {
            this.currentSelect.splice(i, 1)
          }
        }
      } else {
        this.currentSelect.push(row)
      }
    },
    selectChangeUser(arr) {
      this.userSelect = arr
    },
    // 点击用户表的row
    selectRowUser(select, row) {
      if (this.currentSelect.includes(row)) {
        for (let i = 0; i < this.currentSelect.length; i++) {
          if (this.currentSelect[i].id === row.id) {
            this.currentSelect.splice(i, 1)
          }
        }
      } else {
        this.currentSelect.push(row)
      }
    },
    // 删除操作
    deleteCurrent(item, index) {
      for (let i = 0; i < this.currentSelect.length; i++) {
        if (this.currentSelect[i].id === item.id) {
          this.currentSelect.splice(i, 1)
        }
      }
      if (item.type === 'agency') {
        this.$refs.agencyRef.toggleRowSelection(item)
      } else if (item.type === 'dept') {
        this.$refs.deptRef.toggleRowSelection(item)
      } else if (item.type === 'user') {
        this.$refs.userRef.toggleRowSelection(item)
      }
      this.$forceUpdate()
    },
    // 清空列表
    clearList() {
      this.userSelect = []
      this.deptSelect = []
      this.agencySelect = []
      this.currentSelect = []
      this.$refs.agencyRef.clearSelection()
      this.$refs.deptRef.clearSelection()
      this.$refs.userRef.clearSelection()
    },
    deptTranslateTree(arr, result) {
      arr.map((item, index) => {
        result.push({
          id: item.department.deptId,
          label: item.department.deptName,
          checked: false,
          type: 'dept',
          children: [],
        });
        if (item.children.length !== 0) {
          this.deptTranslateTree(item.children, result[index].children);
        }
      });
    },
    //user转换
    userTranslateTree(arr, result) {
      arr.map((item, index) => {
        result.push({
          id: item.userId,
          label: item.userName,
          type: 'user',
          deptId: item.deptId,
          agencyId: item.agencyId
        });
      });
    },
    // 提交
    submit() {
      if (this.currentSelect.length !== 0) {
        for (let i = 0; i < this.currentSelect.length; i++) {
          if (this.currentSelect[i].type === 'agency') {
            this.params.agencyIds.push(this.currentSelect[i])
          } else if (this.currentSelect[i].type === 'dept') {
            this.params.deptIds.push(this.currentSelect[i])
          } else if (this.currentSelect[i].type === 'user') {
            this.params.receivePeoples.push(this.currentSelect[i])
          }
        }
        this.$emit('confirm', this.params);
        this.dialogClosed()
      } else {
        this.$message({
          message: '当前未选择',
          type: 'warning'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.reset_dialog {
  font-size: 16px;

  .closeIcon {
    font-size: 20px;
    cursor: pointer;
    opacity: .8;
    color: rgb(112, 112, 112);
    top: 5px;
  }

  .list {
    height: 40px;
    background: #F9F9F9;
    border-radius: 7px;
    margin-bottom: 12px;
    align-items: center;
    display: flex;
    padding: 0 12px;
    color: #000000;

    image {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
  }

  .left-box {
    width: 48%;
    height: 400px;
    padding: 0 10px;
    border-right: 1px solid #EAEAEA;

    .left-content {
      overflow-y: auto;
      height: 390px;
    }

    .active {
      background: #a2c8c8 !important;
    }
  }

  .left-box::-webkit-scrollbar {
    display: none;
  }

  .right-box {
    width: 50%;
    overflow-y: auto;
    padding: 0 10px;
    height: 400px;

    .right-content {
      height: 85%;
    }

    .btn-box {
      .cancel {
        background: #fff !important;
        border-color: #3888F7 !important;
        color: #3888F7 !important;
        font-family: "微软雅黑", Arial;
      }

      .submit {
        border-color: #3888F7 !important;
        background: #3888F7 !important;
        color: #fff !important;
        font-family: "微软雅黑", Arial;
      }
    }
  }
}

.active {
  background: #a2c8c8 !important;
}

.list {
  height: 40px;
  background: #f6fafb;
  border-radius: 7px;
  margin-bottom: 12px;
  align-items: center;
  display: flex;
  padding: 0 12px;
  color: #000000;

  image {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}

.back {
  width: 80px;
  padding: 4px;
  //font-size: 15px;
  text-align: center;
  background: #3888f7;
  border-radius: 15px;
  color: #fff;
}

::v-deep .el-dialog {
  border-radius: 10px !important;
}

::v-deep {
  .el-dialog__body {
    padding: 10px 20px 20px 20px !important;
  }
}

::v-deep.el-table.cell {
  display: flex !important;
  align-items: center;
  justify-content: center;
}
</style>
