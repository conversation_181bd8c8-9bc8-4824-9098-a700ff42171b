<template>
  <el-dialog
      class="reset-dialog"
      :visible.sync="showDialog"
      append-to-body
      :width="'400px'"
      @open="handleOpen"
      @close="dialogClosed"
  >
    <template slot="title">
      <div class="flex_alAndJsCenter">
        <img
            src="@/pages/images/img2/tips.png">&nbsp;
        <div class="fs_bold fs_18">{{ title }}</div>
      </div>
    </template>
    <div>
      <el-form inline>
        <el-form-item label="公文标题：">
          {{ info.noticeTitle }}
        </el-form-item>
        <el-form-item v-if="approveType===0" label="通过原因：">
          <el-input style="width:250px"
                    :rows="4"
                    v-model="advice"
                    type="textarea"
                    placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item v-if="approveType===1" label="驳回原因：">
          <el-input style="width:250px"
                    :rows="4"
                    v-model="advice"
                    type="textarea"
                    placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div style="text-align: center">
      <el-button class="cancel" @click="dialogClosed">取消</el-button>&nbsp;&nbsp;
      <el-button class="pass" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {approve, signContent} from "@/api/OA";

export default {
  props: {
    info: {
      type: Object,
      default: function () {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    approveType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showDialog: false,
      advice: ""
    }
  },
  methods: {
    handleOpen() {
      this.advice = ''
    },
    dialogClosed() {
      this.showDialog = false
    },
    handleSubmit() {
      if (this.advice) {
        if (this.approveType === 0) {
          approve({
            advice: this.advice, contentId: this.info.contentId,
            approveCode: '0'
          }).then(res => {
            if (res.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success"
              })
              this.dialogClosed()
              this.$emit('success')
            }
          })
        } else if (this.approveType === 1) {
          approve({
            advice: this.advice,
            contentId: this.info.contentId, approveCode: '1'
          }).then(res => {
            if (res.code === 200) {
              this.$message({
                message: "驳回成功",
                type: "success"
              })
              this.dialogClosed()
              this.$emit('success')
            }
          })

        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.closeIcon {
  font-size: 20px;
  cursor: pointer;
  opacity: .8;
  color: rgb(112, 112, 112);
  top: 5px;
}

::v-deep {
  .el-dialog {
    border-radius: 10px !important;
  }
}

::v-deep {
  .el-dialog__body {
    padding: 0 20px 20px 20px !important;
  }
}

.cancel {
  background: #F0F0F0 !important;
  color: #333333 !important;
  border-color: #F0F0F0 !important;
}

.pass {
  background: linear-gradient(90deg, #64A3F8 0%, #1B77F5 100%);
  color: #fff !important;
  border-color: #fff !important;
}
</style>
