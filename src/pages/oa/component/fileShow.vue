<template>
  <el-dialog
      class="reset-dialog"
      :visible.sync="showDialog"
      append-to-body
      :show-close="false"
      @open="handleOpen"
      @close="dialogClosed"
  >
    <template slot="title">
      <div class="flex_space">
        <div class="fs_bold fs_18">文档预览</div>
        <div class="closeIcon pointer" @click="dialogClosed">
          <i class="el-icon-error icon fs_18"></i>
        </div>
      </div>
    </template>
    <div>
      <div ref="docRef"></div>
    </div>
    <!--    <div style="text-align: center">-->
    <!--      <el-button class="cancel" @click="dialogClosed"></el-button>&nbsp;&nbsp;-->
    <!--    </div>-->
  </el-dialog>
</template>
<script>
import {signContent} from "@/api/OA";
import axios from 'axios'

let docx = require("docx-preview")
export default {
  props: {
    url: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      showDialog: false,
      advice: ""
    }
  },
  methods: {
    handleOpen() {
      axios({
        method: 'get',
        responseType: 'blob',
        url: this.url
      }).then(res => {
        docx.renderAsync(res.data, this.$refs.docRef)
      })
    },
    dialogClosed() {
      this.showDialog = false
    },
  }
}
</script>
<style lang="scss" scoped>
.closeIcon {
  font-size: 20px;
  cursor: pointer;
  opacity: .8;
  color: rgb(112, 112, 112);
  top: 5px;
}

::v-deep {
  .el-dialog {
    border-radius: 10px !important;
  }
}

::v-deep {
  .el-dialog__body {
    padding: 0 20px 20px 20px !important;
  }
}

.cancel {
  background: #fff !important;
  border-color: #3888F7 !important;
  color: #3888F7 !important;
  font-family: "微软雅黑", Arial;
}

.pass {
  border-color: #3888F7 !important;
  background: #3888F7 !important;
  color: #fff !important;
  font-family: "微软雅黑", Arial;
}

</style>
