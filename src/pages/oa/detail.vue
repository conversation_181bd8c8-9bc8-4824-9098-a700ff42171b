<template>
  <div class="fs_16" v-loading="vLoading">
    <!--    被驳回-->
    <div v-if="info.status==='2'">
      <div v-for="(item,index) in info.approveRecords" :key="index"
           class="reject-box fs_14 bg_fff m_b_20 br_5 p_20 box_shadow">
        <div class="reject-content bg_f0 p_20 br_5">
          <div class="flex_center ">
            <div>
              <span class="c_666">驳回人：</span>{{ item.userName }}
            </div>&nbsp;&nbsp;&nbsp;&nbsp;
            <div>
              <span class="c_666">驳回时间：</span>{{ parseTime(item.createTime, '') }}
            </div>
          </div>
          <div class="m_t_10">
            <div class="c_666">修改意见：</div>
            <div class="m_t_10 bg_fff p_10 br_5">{{ item.advice }}</div>
          </div>
        </div>
      </div>

    </div>
    <div class="top-container">
      <div class="title">{{ info.noticeTitle }}</div>
      <div class="m_t_20 top-container-center">
        <div class="tip-box ">
          <div class="flex_center">
            <div class="flex_center">
              <div class="c_tip w_80">发文单位：</div>
              <div>{{ info.agencyName }}</div>
            </div>
            <div class="flex_center m_l_40">
              <div class="c_tip w_80">发文时间：</div>
              <div>{{ dayjs()(info.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
            </div>
          </div>
          <div class="flex_center m_t_10">
            <div class="flex_center">
              <div class="c_tip w_80">发起人：</div>
              <div>{{ info.createBy }}</div>
            </div>
            <div class="flex_center m_l_40">
              <div class="c_tip w_80">公文类型：</div>
              <div>
                <dict-tag
                    :options="dict.type.notice_type"
                    :value="info.noticeType"/>
              </div>
            </div>
          </div>
          <div class="flex_center m_t_10">
            <div class="flex_center ">
              <div class="c_tip w_80">文号：</div>
              <div>
                {{ info.numberAgency }} 【{{ info.numberYear }}】{{ info.numberRef }}号
              </div>
            </div>
          </div>
        </div>
        <div class="tag">
          <span v-if="type==='0'&&info.signFlag==='0'">待签收</span>
          <span v-if="type==='0'&&info.signFlag==='1'">已签收</span>
          <span v-if="type==='1'&&info.status==='0'">审核中</span>
          <span v-if="type==='2'&&info.status==='0'">待审核</span>
        </div>
        <div class="m_t_10">
          <div class="c_tip">正文内容：</div>
          <div class="notice-content">{{ info.noticeCon }}</div>
        </div>
      </div>
      <div class="m_t_20 fs_14">
        <div class="c_tip m_t_10">附件：</div>
        <div v-for="item in info.fileList" :key="item.fileId" class="file-item">
          <div>
            {{ item.fileName }}
          </div>
          <div class="flex_center">
            <div class="pointer btn_box" @click="viewFile(item.fileUrl)">
              <i class="el-icon-document"></i>&nbsp;
              <span>预览</span>
            </div>&nbsp;&nbsp;&nbsp;&nbsp;
            <div class="pointer btn_box" @click="downloadFile(item)">
              <i class="el-icon-download"></i>&nbsp;
              <span>下载</span>
            </div>
          </div>
        </div>
        <!--        我收到的-->
        <div v-if="type==='0'" class="m_t_20" style="text-align: center">
          <el-button class="get" v-if="info.signFlag==='0'" @click="handleGet">签收</el-button>&nbsp;&nbsp;
          <!--          <el-button class="disabled" v-if="info.signFlag==='1'">已签收</el-button>&nbsp;&nbsp;-->
          <el-button class="pass" @click="handleRelay">转发</el-button>&nbsp;
          <el-button class="pass" v-if="info.type==='1'" @click="feedBack">活动反馈</el-button>
        </div>

        <!--        我发起的-->
        <div v-if="type==='1'" class="m_t_20" style="text-align: center">
          <!--          <el-button class="disabled" v-if="info.status==='0'">待审核</el-button>-->
          <el-button class="reject" v-if="info.status==='1'">已驳回</el-button>&nbsp;&nbsp;
          <el-button class="pass" v-if="info.status==='2'">已通过</el-button>
          <el-button class="pass" v-if="info.status==='0'" @click="handleCancel">撤销</el-button>
          <el-button class="disabled" v-if="info.status==='3'" @click="handleCancel">已撤销</el-button>
        </div>
        <!--        我审核的-->
        <div v-if="type==='2'" class="m_t_20" style="text-align: center">
          <el-button class="disabled" v-if="info.status!=='0'">已审核</el-button>
          <el-button class="reject" v-if="info.status==='0'" @click="handleReject">驳回</el-button>&nbsp;&nbsp;
          <el-button class="pass" v-if="info.status==='0'" @click="handlePass">通过</el-button>
        </div>
      </div>
    </div>
    <div class="center-container m_t_20">
      <div class="fs_18 fs_bold accept_uni">接收单位</div>
      <div class="flex_c m_t_10 m_b_20">
        <div class="flex_alAndJsCenter">
          <div class="m_r_20 pointer" style="height:25px;"
               :class="currentInx===index?'is-click':'un-click'"
               v-for="(item,index) in tagArr"
               :key="index"
               @click="changItem(index)"
          >{{ item.title }}
          </div>
        </div>
        <div class="c_fff flex_alAndJsCenter fs_14 br_5 pointer remind_box">
          <img src="@/pages/images/img2/remind.png">&nbsp;
          <div>全部提醒</div>
        </div>
      </div>
      <div v-if="currentInx===0">
        <div v-for="item in receiveUnitList" :key="item.deptId">
          <collapse :collpaseInfo="item" :type="currentInx" :content-type="info"></collapse>
        </div>
        <div v-if="receiveUnitList.length===0">
          <el-empty></el-empty>
        </div>
      </div>
      <div v-if="currentInx===1">
        <div v-for="item in receiveUnitList" :key="item.deptId">
          <collapse :collpaseInfo="item" :type="currentInx" :content-type="info"></collapse>
        </div>
        <div v-if="receiveUnitList.length===0">
          <el-empty></el-empty>
        </div>
      </div>
      <div v-if="currentInx===2">
        <div v-for="item in receiveUnitList" :key="item.deptId">
          <collapse :collpaseInfo="item" :type="currentInx" :content-type="info"></collapse>
        </div>
        <div v-if="receiveUnitList.length===0">
          <el-empty></el-empty>
        </div>
      </div>
    </div>
    <sign-dialog ref="signDialogRef"
                 :info="signInfo"
                 @success="success"></sign-dialog>
    <audit-dialog ref="auditDialogRef"
                  @success="success"
                  :approve-type="approveType"
                  :title="title" :info="dialogInfo"></audit-dialog>
    <feedback ref="feedbackDialogRef"
              :info="info"
              @success="feedbackSuccess"></feedback>

    <!--   预览 图片-->
    <el-image-viewer v-if="showViewImg"
                     :on-close="closeViewer"
                     :url-list="[url]"
    ></el-image-viewer>
    <!--    转发-->
    <forward-select ref="unitRef2"
                    :title="title"
                    @confirm="unitSave"></forward-select>
    <!--转发2-->
    <unit-tree ref="unitRef"
               :title="title"
               @confirm="unitSave"></unit-tree>
    <!--    公用弹窗-->
    <public-dialog ref="publicDialogRef"
                   :info="publicInfo"
                   :audit-type="publicType"
                   :tipTitle="pubTitle"
                   @submit="pubSubmit">

    </public-dialog>
  </div>
</template>

<script>
import signDialog from "@/pages/oa/components/signDialog.vue";
import Collapse from "@/pages/oa/component/collapse.vue";
import {forward, getContent, getReceiveUnitList} from "@/api/OA";
import dayjs from "dayjs";
import AuditDialog from "@/pages/oa/component/auditDialog.vue";
import Feedback from "@/pages/oa/component/feedback.vue";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer.vue'
import {mapGetters} from 'vuex'
import ForwardSelect from "@/pages/oa/component/forwardSelect.vue";
import PublicDialog from "@/pages/oa/components/publicDialog.vue";
import UnitTree from "@/pages/oa/components/unitTree.vue";
import {generateRandomStr, getAesString} from "@/pages/utils/tools";

let base64 = require('js-base64').Base64

export default {
  dicts: ['notice_type'],
  components: {
    UnitTree,
    PublicDialog,
    ForwardSelect,
    Feedback,
    AuditDialog,
    Collapse,
    signDialog,
    ElImageViewer,
  },
  data() {
    return {
      tagArr: [
        {
          title: '全部'
        },
        {
          title: '已签收'
        },
        {
          title: '未签收'
        }
      ],
      currentInx: 0,
      type: '',
      isClear: true,
      infoDetail: {},
      info: {},
      receiveUnitList: [],//接收单位
      title: '',
      dialogInfo: {},
      approveType: 0,
      signInfo: {},
      feedbackInfo: '',
      title1: '',
      receivePeoples: [],//转发人员
      userInfo: '',
      src: '',
      vLoading: false,
      showViewImg: false,
      url: '',
      docUrl: '',
      listQuery: {
        receivePeoples: [],
        agencyIds: [],
        deptIds: []
      },
      publicInfo: {},
      publicType: 0,
      pubTitle: ''
    }
  },
  computed: {
    ...mapGetters(['role', 'userInfo', 'name']),
    tagStyle() {
      if (this.type === '0' || this.type === '2') {
        return '#1B77F5'
      } else {
        return '#CCCCCC'
      }
    }
  },
  mounted() {
    this.type = this.$route.query.type
    this.loadData()
  },
  methods: {
    dayjs() {
      return dayjs
    },
    async loadData() {
      this.vLoading = true
      getContent(this.$route.query.id).then(res => {
        this.info = res.data
        this.getReceiveUnitLists(res.data.contentId)
      }).finally(() => {
        this.vLoading = false
      })
    },
    // 接收单位
    async getReceiveUnitLists(id) {
      let params = {
        signFlag: ''
      }
      if (this.currentInx === 1) {
        params.signFlag = '1'
      } else if (this.currentInx === 2) {
        params.signFlag = '0'
      }
      const {data} = await getReceiveUnitList({
        contentId: id,
        ...params
      });
      this.receiveUnitList = data;
    },
    // 预览
    viewFile(url) {
      let watermarkTxt = ''
      if (this.role.role_type == 'stu') {
        watermarkTxt = this.name;
      } else {
        watermarkTxt = this.name + ' ' + this.userInfo.phone;
      }
      window.open(process.env.VUE_APP_KFILE_URL +
          encodeURIComponent(base64.encode(url)) + '&watermarkTxt=' + encodeURIComponent(getAesString(watermarkTxt,
              generateRandomStr(16), generateRandomStr(16))))
    },
    // 图片关闭
    closeViewer() {
      this.showViewImg = false
    },
    // 下载
    downloadFile(item) {
      window.open(item.fileUrl + `?attname=` + item.fileName, '_self')
    },
    changItem(inx) {
      this.currentInx = inx
      this.getReceiveUnitLists(this.info.contentId)
    },
    // 公用弹窗确定
    pubSubmit() {
      this.loadData()
    },
    // 撤销
    handleCancel() {
      this.publicType = 1
      let infos = JSON.parse(JSON.stringify(this.info));
      infos.status = 3;
      this.pubTitle = '是否撤销该公文？'
      this.publicInfo = infos
      this.$refs.publicDialogRef.showDialog = true
      // this.$confirm('确定撤销公文吗？', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // }).then(() => {
      //   let infos = that.info;
      //   infos.status = 3;
      //   // putContent(infos).then((res) => {
      //   //   if (res.code === 200) {
      //   //     this.$message({
      //   //       message: '撤销成功',
      //   //       type: 'warning',
      //   //     });
      //   //   }
      //   //   this.$router.push({
      //   //     path: '/oa'
      //   //   })
      //   // });
      // }).catch(() => {
      // })
    },
    // 通过
    handlePass() {
      this.dialogInfo = this.info
      this.title = '通过'
      this.approveType = 0
      this.$refs.auditDialogRef.showDialog = true
    },
    // 驳回
    handleReject() {
      this.dialogInfo = this.info
      this.title = '驳回'
      this.approveType = 1
      this.$refs.auditDialogRef.showDialog = true
    },
    success() {
      this.loadData()
    },
    // 转发
    handleRelay() {
      this.title = '接收单位选择'
      this.$refs.unitRef.showDialog = true
    },
    unitSave(data) {
      this.listQuery.receivePeoples = []
      let userList = _.clone(data)
      let showLabel = ''
      if (userList.deptIds.length !== 0) {
        showLabel += userList.deptIds.map(item => {
          return item.label
        }).toString() + ','
        this.listQuery.deptIds = userList.deptIds.map(item => {
          return item.id;
        })
      }
      if (userList.receivePeoples.length !== 0) {
        showLabel += userList.receivePeoples.map(item => {
          return item.label
        }).toString() + ','
        this.listQuery.receivePeoples = userList.receivePeoples.map(item => {
          return {
            signBy: item.userId,
            deptId: item.deptId,
            agencyId: item.agencyId,
          };
        })
      }
      this.userInfo = showLabel
      this.handleZf()
    },
    // 反馈
    feedBack() {
      if (this.info.signFlag === '0') {
        this.$message({
          message: '请先签收当前公文',
          type: 'warning'
        })
        return
      }
      this.$refs.feedbackDialogRef.showDialog = true
    },
    feedbackSuccess() {
      this.loadData()
    },
    handleZf() {
      let that = this
      this.$confirm('是否转发给' + this.userInfo + '?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let params = {
          contentId: that.info.contentId,
          receivePeoples: that.listQuery.receivePeoples,
          deptIds: that.listQuery.deptIds
        }
        forward(params).then(res => {
          if (res.code === 200) {
            this.$message({
              message: '转发成功',
              type: 'success',
            });
          }
        })
      }).catch(() => {

      })
    },
    // 签收
    handleGet() {
      this.signInfo = this.info
      this.$refs.signDialogRef.showDialog = true
    }
  }
}
</script>
<style lang="scss" scoped>
.top-container {
  padding: 20px;
  background: #fff;
  font-size: 16px;
  border-radius: 5px;
  position: relative;

  .top-container-center {
    background: #F7FAFF;
    padding: 20px;
    font-size: 14px;
    border-radius: 5px;

    .tip-box {
      border-radius: 6px;
      z-index: 100;
    }

    .notice-content {
      margin-top: 5px;
      border-radius: 6px;
      padding: 10px;
      color: #333333;
      font-size: 14px;
      text-indent: 30px;
      background: #fff
    }
  }

  .title {
    //color: #0F4444;
    font-size: 20px;
    font-weight: bold;
  }


  .tag {
    position: absolute;
    font-size: 14px;
    color: #fff;
    width: 67px;
    height: 30px;
    line-height: 28px;
    text-align: center;
    right: -3px;
    top: 0;
    background: url("@/pages/images/img2/status.png") no-repeat;
    background-size: 100% 100%;
    //border-radius: 0px 4px 4px 4px;
  }

  .file-item {
    margin-top: 10px;
    border-radius: 6px;
    background: #F7FAFF;
    padding: 10px;
    color: #666666;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn_box {
      background: #F0F0F0;
      border-radius: 5px;
      padding: 5px 15px;
    }
  }

  .reject {
    background: #fff !important;
    border-color: #FD8080 !important;
    color: #FD8080 !important;
    font-family: "微软雅黑", Arial;
  }

  .get {
    background: #fff !important;
    border-color: #3888F7 !important;
    color: #3888F7 !important;
    font-family: "微软雅黑", Arial, sans-serif;
  }

  .pass {
    border-color: #3888F7 !important;
    background: #3888F7 !important;
    color: #fff !important;
    font-family: "微软雅黑", Arial, sans-serif;
  }
}

.center-container {
  font-size: 16px;
  background: #fff;
  border-radius: 6px;
  padding: 20px;

  .accept_uni {
    position: relative;
    padding-left: 10px;
  }

  .accept_uni:before {
    position: absolute;
    content: '';
    top: 3px;
    left: 0;
    height: 80%;
    width: 2px;
    border-radius: 4px;
    background: #3888F7;
  }

  .remind_box {
    width: 100px;
    height: 32px;
    background: linear-gradient(90deg, #64A3F8 0%, #1B77F5 100%);
  }

  .un-click {
    color: #999999
  }

  .is-click {
    position: relative;
    //font-weight: bold;
    color: #3888F7;
  }

  .is-click:before {
    content: "";
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 100%;
    border-radius: 5px;
    border-bottom: 3px solid #3888F7;
    box-sizing: border-box;
    opacity: .9;
  }
}

.disabled {
  border-color: rgb(169, 169, 169) !important;
  background: rgb(169, 169, 169) !important;
  color: #fff !important;
  font-family: "微软雅黑", Arial;;
  cursor: default;
}

</style>
