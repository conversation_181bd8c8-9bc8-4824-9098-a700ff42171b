<template>
  <div>
    <Header @refresh="refresh"></Header>
    <div style="padding-top: 75px"></div>
    <div class="sub-page-container oa-container">
      <div>
        <el-backtop :bottom="150" :right="backTopRight">
          <div style="width: 40px;height:40px" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
            <img v-if="!isPointer" src="@/pages/assets/images/OA/backtop.png">
            <img v-if="isPointer" src="@/pages/assets/images/OA/backtioSelct.png">
          </div>
        </el-backtop>
      </div>
      <div class="header-top flex just_between txt-select">
        <div :class="newOa?'left_width':'left_width_100'" class="left_tab flex aligin_center box_shadow">
          <div v-for="(item,index) in itemArr"
               :key="index" style="display: flex;align-items: center">
            <div class="pointer" style="height:25px;"
                 :class="currentInx===index?'is-click':'un-click'"
                 @click="changItem(index)"
            >{{ item.title }}
            </div>
            <el-divider style="margin:0 10px;" v-if="(index===0)&&itemArr.length>1"
                        direction="vertical"></el-divider>
          </div>
        </div>
        <div v-if="newOa" class="right_button box_shadow" @click="addDocument">
          <div>
            <img style="width: 30px;height:30px;" src="@/pages/images/img2/add.png">
          </div>&nbsp;
          <div>新建公文</div>
        </div>
      </div>
      <div v-loading="vLoading">
        <div class="card_content box_shadow">
          <!--我收到的筛选搜索-->
          <div v-if="currentInx===0" class="filter-box flex_c fs_14 ">
            <el-form :inline="true" class="search_class ">
              <div class="flex_c">
                <el-form-item label="签收状态:">
                  <el-select @change="handleQuery" v-model="params.signFlag" style="width: 120px" clearable>
                    <el-option v-for="(item,index) in  signStatusOption"
                               :key="index"
                               :label="item.label"
                               :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="紧急程度:">
                  <el-select @change="handleQuery" v-model="params.urgentLevel" style="width: 120px" clearable>
                    <el-option v-for="(item,index) in dict.type['notice_urgent_level']"
                               :key="index"
                               :label="item.label"
                               :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="发文单位:">
                  <el-select @change="handleQuery" v-model="params.sendAgencyId" clearable style="width: 200px">
                    <el-option v-for="(item,index) in unitList"
                               :label="item.fullName"
                               :value="item.agencyId"
                               :key="index"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item>
                  <div class="search-box">
                    <div class="input-box">
                      <input v-model="params.noticeTitle" class="input" type="text"
                             @keyup.enter="handleQuery"
                             placeholder="请输入关键字搜索"/>
                      <i style="font-size: 16px" class="el-icon-refresh c_999 pointer" @click="clearQuery"></i>&nbsp;
                    </div>
                    <div class="btn-class" @click="handleQuery">查询</div>
                  </div>
                </el-form-item>
                <el-form-item>
                  <div class="open-icon flex_alAndJsCenter" @click="closeSearchMore">
                    <img style="width: 5px" src="@/pages/assets/images/OA/open_icon.png">
                  </div>
                </el-form-item>
              </div>
              <div v-if="showSearchMore" style="height: 42px" class="flex_c">
                <div class="flex_alAndJsCenter">
                  <el-form-item style="margin-right: 10px;" label="发文日期:">
                    <el-date-picker
                        @change="handleStartTime"
                        v-model="time"
                        class="date-picker--class"
                        type="datetimerange"
                        start-placeholder="请选择日期"
                        end-placeholder="请选择日期"
                        :default-time="['00:00:00', '23:59:59']">
                    </el-date-picker>
                    <i class="el-icon-date c_999 date-time--icon"></i>
                  </el-form-item>
                  <el-form-item label="阅读状态:">
                    <el-select @change="handleQuery" v-model="params.consultFlag" style="width: 200px" clearable>
                      <el-option v-for="(item,index) in  readOption"
                                 :key="index"
                                 :label="item.label"
                                 :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div style="color:#999;cursor:pointer;" @click="closeSearchMore">
                  收起
                  <i class="el-icon-arrow-up"></i>
                </div>
              </div>
            </el-form>
          </div>
          <!--我发起的筛选搜索-->
          <div v-if="currentInx===1" class="filter-box flex_center fs_14">
            <el-form :inline="true" class="search_class ">
              <div class="flex_c">
                <div class="flex_c">
                  <el-form-item label="紧急程度:">
                    <el-select @change="handleQuery" v-model="params.urgentLevel" style="width: 120px" clearable>
                      <el-option v-for="(item,index) in dict.type['notice_urgent_level']"
                                 :key="index"
                                 :label="item.label"
                                 :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="发文日期:">
                    <el-date-picker
                        @change="handleStartTime"
                        v-model="time"
                        class="date-picker--class"
                        type="datetimerange"
                        start-placeholder="请选择日期"
                        end-placeholder="请选择日期"
                        :default-time="['00:00:00', '23:59:59']">
                    </el-date-picker>
                    <i class="el-icon-date c_999 date-time--icon"></i>
                  </el-form-item>
                </div>

                <el-form-item>
                  <div class="search-box">
                    <div class="input-box">
                      <input v-model="params.noticeTitle" class="input" type="text" placeholder="请输入关键字搜索"/>
                      <i style="font-size: 16px" class="el-icon-refresh c_999 pointer" @click="clearQuery"></i>&nbsp;
                    </div>
                    <div class="btn-class" @click="handleQuery">查询</div>
                  </div>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <div v-if="currentShowType==1" class="m_t_20">
            <div>
              <div v-for="(item,index) in oaList" :key="item.contentId">
                <v-oa-item @handleClickOa="handleClickOa" :info="item" :inx="index" :type="currentInx"
                           @loadSuccess="loadSuccess"></v-oa-item>
              </div>
            </div>
            <div class="paging" v-if="oaList.length!==0">
              <div style="width: 160px;">
                <!--卡片式-->
                <img @click="changeSrcList" :src="showListSrc" title="列表式" class="card-img--class"
                     style="width: 24px;height:24px;cursor:pointer;">&nbsp;
                <img @click="changeSrcCard" :src="showCardSrc" title="卡片式" class="card-list--class"
                     style="width: 24px;height:24px;cursor:pointer;">
              </div>
              <el-pagination
                  :page-size="params.pageSize"
                  background
                  :total="total"
                  layout="prev, pager, next, jumper"
                  :current-page="params.pageNum"
                  @current-change="handleCurrentChange"></el-pagination>
              <div class="page--left">
                <div style>共{{ total }}条</div>&nbsp;&nbsp;

                <el-select v-model="currentPage" style="width: 108px;" @change="changePageList">
                  <el-option v-for="(item,index) in pages"
                             :key="index"
                             :label="item.label"
                             :value="item.value"
                  ></el-option>
                </el-select>
              </div>
            </div>
          </div>
          <div v-if="currentShowType==2" class="m_t_20" style="padding:0 20px">
            <div class="itembox">
              <div v-for="item in oaList" :key="item.contentId">
                <oa-item :info="item" @transmit="transmit" :btn-type="currentInx" @loadSuccess="loadSuccess"></oa-item>
              </div>
            </div>
            <div v-if="false" class="paging">
              <!--             :page-sizes="[12, 24, 36, 48]"  -->
              <!--              @size-change="handleSizeChange"-->
              <!--              layout="total, sizes, prev, pager, next, jumper"-->
              <el-pagination
                  :page-size="params.pageSize"
                  background
                  layout="total, sizes, prev, pager, next, jumper"
                  :page-sizes="[12, 24, 36, 48]"
                  :total="total"
                  :current-page="params.pageNum"
                  @current-change="handleCurrentChange"></el-pagination>
            </div>
            <div class="paging" v-if="oaList.length!==0">
              <div style="width: 160px;">
                <!--卡片式-->
                <img @click="changeSrcList" :src="showListSrc" title="列表式" class="card-img--class"
                     style="width: 24px;height:24px;cursor:pointer;">&nbsp;
                <img @click="changeSrcCard" :src="showCardSrc" title="卡片式" class="card-list--class"
                     style="width: 24px;height:24px;cursor:pointer;">
              </div>
              <!--             :page-sizes="[12, 24, 36, 48]"  -->
              <!--              @size-change="handleSizeChange"-->
              <!--              layout="total, sizes, prev, pager, next, jumper"-->
              <el-pagination
                  :page-size="params.pageSize"
                  background
                  :total="total"
                  layout="prev, pager, next, jumper"
                  :current-page="params.pageNum"
                  @current-change="handleCurrentChange"></el-pagination>
              <div class="page--left">
                <div style>共{{ total }}条</div>&nbsp;&nbsp;
                <el-select v-model="currentPage" style="width: 108px;" @change="changePageList">
                  <el-option v-for="(item,index) in pages"
                             :key="index"
                             :label="item.label"
                             :value="item.value"
                  ></el-option>
                </el-select>
              </div>
            </div>
          </div>
          <el-empty v-if="oaList.length===0"></el-empty>
        </div>
      </div>
      <add-doc-dialog ref="addDocDialogRef" @success="addSuccess"></add-doc-dialog>
    </div>
  </div>
</template>
<script>
import {addOa, getOaList, getUnitDataSelectOptions} from "@/api/OA";
import oaItem from "@/pages/oa/components/oaItem.vue";
import {mapGetters} from 'vuex'
import AddDocDialog from "@/pages/oa/components/addDocDialog.vue";
import Header from "@/pages/layout/pageLayout/Header.vue";
import VOaItem from "@/pages/oa/components/v-oaItem.vue";
import {parseTime} from "@/utils/tools";

export default {
  components: {
    VOaItem,
    Header,
    AddDocDialog,
    oaItem
  },
  dicts: ['notice_urgent_level'],
  data() {
    return {
      currentInx: 0,
      isPointer: false,
      itemArr: [
        {
          title: '我收到的'
        },
        {
          title: '我发起的'
        },
        // {
        //   title: '我审核的'
        // }
      ],
      params: {
        signFlag: '',
        status: '',
        pageNum: 1,
        pageSize: 12,
        urgentLevel: '',
        createTime: [],
        agencyId: '',
        noticeTitle: '',
        consultFlag: '',
        sendAgencyId: ''
      },
      type: 0,
      oaList: [],
      total: 0,
      vLoading: false,
      newOa: false,
      time: [],
      showSearchMore: false,
      pages: [
        {
          label: '12条/页',
          value: 1
        },
        {
          label: '24条/页',
          value: 2
        },
        {
          label: '36条/页',
          value: 3
        },
        {
          label: '48条/页',
          value: 4
        }
      ],
      currentPage: 1,
      currentShowType: 1,
      unCardSrc: require('@/pages/assets/images/OA/card_unselected.png'),
      cardSrc: require('@/pages/assets/images/OA/card_selected.png'),
      unListSrc: require('@/pages/assets/images/OA/list_unselected.png'),
      listSrc: require('@/pages/assets/images/OA/list_selected.png'),
      signStatusOption: [
        {
          label: '全部',
          value: ''
        }, {
          label: '已签收',
          value: '1'
        }, {
          label: '未签收',
          value: '0'
        },
      ],
      readOption: [
        {
          label: '全部',
          value: ''
        }, {
          label: '已阅读',
          value: '1'
        }, {
          label: '未阅读',
          value: '0'
        },
      ],
      unitList: [],
      backTopRight: 0
    }
  },
  computed: {
    ...mapGetters(['sysType','role', 'roles']),
    backPath() {
      switch (this.role.role_key) {
        case 'workers_wor':
        case 'area_man':
        case 'dept_man':
        case 'manager_man':
        case 'admin':
        case '':
          return {path: '/manage'};
        case 'school_man':
          return {path: '/school'};
        case 'teacher_tea':
        case 'classTeacher_tea':
        case 'deputyHeadTeacher_tea':
        case 'gradeLeader_tea':
          return {path: '/teacher'};
        case 'student_stu':
          return {path: '/student'};
        case 'parents_par':
          return {path: '/parents'};
      }
    },
    backClass() {
      switch (this.role.role_key) {
        case 'workers_wor':
        case 'area_man':
        case 'dept_man':
        case 'manager_man':
        case 'admin':
        case '':
          return 'manClass'
        case 'school_man':
          return 'schClass'
        case 'teacher_tea':
        case 'classTeacher_tea':
        case 'deputyHeadTeacher_tea':
        case 'gradeLeader_tea':
          return 'teaClass'
        case 'student_stu':
          return 'stuClass'
        case 'parents_par':
          return 'famClass'
      }
    },
    showCardSrc() {
      if (this.currentShowType == 1) {
        return this.unCardSrc
      } else if (this.currentShowType == 2) {
        return this.cardSrc
      }
    },
    showListSrc() {
      if (this.currentShowType == 1) {
        return this.listSrc
      } else if (this.currentShowType == 2) {
        return this.unListSrc
      }
    },
  },
  created() {
    let temporaryInxOfOa = sessionStorage.getItem('temporaryInxOfOa');
    let temporaryPageSize = sessionStorage.getItem('temporaryPageSize');
    if (temporaryInxOfOa) {
      this.currentInx = Number(temporaryInxOfOa);
      this.type = Number(temporaryInxOfOa);
    }
    if (temporaryPageSize) {
      this.params.pageNum = Number(temporaryPageSize);
    }
    let currentShowType = sessionStorage.getItem('currentShowType');
    if (currentShowType) {
      this.currentShowType = Number(currentShowType);
    }
  },
  mounted() {
    // console.log(this.sysType)
    this.backTopRight = Math.floor((((window.innerWidth - 1200) / 2) * 2) / 3);
    this.loadData(this.type);
    this.getUnitList();
    let roleKey = this.role.role_key;
    this.itemArr = [{
      title: '我收到的'
    },
      {
        title: '我发起的'
      }
    ];
    if(this.sysType==='school'){//校级
      this.newOa = true;
    }else{
      if ('area_man' === roleKey || roleKey === 'dispatcher_wor' || roleKey === 'dept_dispatcher_wor') {
        this.newOa = true;
      } else {
        this.newOa = false
      }
    }

    // let currentScrollY = sessionStorage.getItem('currentScrollY');
    // if (currentScrollY) {
    //   console.log(currentScrollY)
    //   window.scrollTo({
    //     top: Number(currentScrollY),
    //     behavior: 'smooth'
    //   })
    //   sessionStorage.removeItem('currentScrollY');
    // }
  },
  methods: {
    // 发文单位
    getUnitList() {
      getUnitDataSelectOptions().then(res => {
        if (res.code == 200) {
          this.unitList = res.data
        }
      })
    },
    closeSearchMore() {
      this.showSearchMore = !this.showSearchMore;
    },
    // 筛选搜索
    handleQuery() {
      this.params.pageNum = 1;
      this.loadData(this.type)
    },
    // 清空选择
    clearQuery() {
      this.params = {
        signFlag: '',
        status: '',
        pageNum: 1,
        pageSize: 12,
        urgentLevel: '',
        createTime: [],
        agencyId: '',
        noticeTitle: '',
        consultFlag: null
      }
      this.time = [];
      this.handleQuery()
    },
    // 切换显示类型
    changeSrcList() {
      if (this.currentShowType == 1) {
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '切换中',
        background: 'rgba(0,0,0,0.7)'
      })
      this.params.pageSize = 12;
      this.currentPage = 1;
      let time = 1;
      let timer = setInterval(() => {
        time -= 0.5;
        if (time == 0) {
          clearInterval(timer);
          this.clearQuery()
          this.currentShowType = 1;
          sessionStorage.setItem('currentShowType', this.currentShowType);
          loading.close()
        }
      }, 200)
    },
    changeSrcCard() {
      if (this.currentShowType == 2) {
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '切换中',
        background: 'rgba(0,0,0,0.7)'
      });
      this.params.pageSize = 12;
      this.currentPage = 1;
      let time = 1;
      let timer = setInterval(() => {
        time -= 0.5;
        if (time == 0) {
          this.clearQuery()
          clearInterval(timer);
          this.currentShowType = 2;
          sessionStorage.setItem('currentShowType', this.currentShowType);
          loading.close()
        }
      }, 200)
    },
    // 筛选开始时间
    handleStartTime() {
      if (this.time && this.time.length !== 0) {
        this.params.createTime[0] = parseTime(this.time[0], '');
        this.params.createTime[1] = parseTime(this.time[1], '');
      } else {
        this.params.createTime = []
      }
      this.handleQuery()
    },
    refresh() {
      this.loadData(this.type)
      let roleKey = this.role.role_key;
      this.itemArr = [{
        title: '我收到的'
      },
        {
          title: '我发起的'
        }
      ];
      if(this.sysType==='school'){//校级
        this.newOa = true;
      }else{
        if ('area_man' === roleKey || roleKey === 'dispatcher_wor' || roleKey === 'dept_dispatcher_wor') {
          this.newOa = true;
        } else {
          this.newOa = false
        }
      }
    },
    addOa,
    changItem(inx) {
      this.currentInx = inx;
      this.type = inx;
      this.params = {
        signFlag: '',
        status: '',
        pageNum: 1,
        pageSize: 12,
        urgentLevel: '',
        createTime: [],
        agencyId: '',
        noticeTitle: '',
      }
      this.time = {
        creatTime: '',
        lastTime: ''
      };
      this.currentPage = 1;
      this.loadData(inx);
      sessionStorage.removeItem('temporaryInxOfOa')
      sessionStorage.removeItem('temporaryPageSize');
      sessionStorage.setItem('temporaryInxOfOa', this.type);
      this.getUnitList();
    },
    loadData(e) {
      this.vLoading = true
      this.oaList = [];
      this.total = 0;
      getOaList({type: e === 0 ? '' : e, ...this.params}).then(res => {
        if (res.code === 200) {
          this.oaList = res.rows
          this.total = res.total
        }
      }).finally(() => {
        this.vLoading = false
      })
    },
    handleCurrentChange(page) {
      this.params.pageNum = page
      this.loadData(this.type);
      sessionStorage.setItem('temporaryPageSize', page)
    },
    changePageList(val) {
      this.params.pageNum = 1;
      this.params.pageSize = 12;
      this.params.pageSize = this.params.pageSize * val;
      this.loadData(this.type)
      // this.params.pageNum=val;
    },
    // 转发
    transmit(id) {
    },
    loadSuccess() {
      this.loadData(this.type)
    },
    addDocument() {
      this.isForward = false;
      this.$refs.addDocDialogRef.showDialog = true
      // this.$router.push({
      //   path: '/oa/addDocument'
      // })
    },
    addSuccess() {
      this.loadData(this.type)
    },
    handleMouseEnter() {
      this.isPointer = true;
    },
    handleMouseLeave() {
      this.isPointer = false;
    },
    handleClickOa() {
      // sessionStorage.setItem('currentScrollY', String(window.scrollY));
    }
  },
  beforeDestroy() {
  }
}
</script>
<style lang="scss" scoped>
.oa-container {
  .header-top {
    font-size: 16px;

    .left_width {
      width: 1000px;
    }

    .left_width_100 {
      width: 100%;
    }

    .left_tab {
      background: #fff;
      padding: 15px;
      border-radius: 5px;

      .is-click {
        position: relative;
        color: #3888F7;
      }

      .is-click:before {
        content: "";
        position: absolute;
        left: 25%;
        bottom: 0px;
        width: 50%;
        border-bottom: 3px solid #3888F7;
        box-sizing: border-box;
        opacity: .9;
      }
    }

    .right_button {
      width: 138px;
      background: #fff;
      color: #1B77F5;
      //font-size: 18px;
      border-radius: 5px;
      display: flex;
      align-items: center;
      cursor: pointer;
      justify-content: center;
    }

    .right_button:active {
      opacity: .7;
    }
  }

  .content {
    border-radius: 5px;
    font-size: 16px;
    padding: 20px;
    //padding: 20px 0;
    background: #fff;
    margin-top: 20px;

    .filter-box {
      //padding: 0 20px;
      .filter-item {
        cursor: pointer;
        width: 80px;
        height: 32px;
        border-radius: 4px;
        line-height: 32px;
        text-align: center;
        color: #666666;
        background: #EEEEEE;
      }

      .selected {
        color: #1B77F5;
        border-color: #1B77F5;
        background: #DEECFE;
      }
    }

    .itembox {
      display: flex;
      gap: 20px;
      flex-direction: row;
      flex-wrap: wrap;
    }
  }
}

.card_content {
  border-radius: 5px;
  font-size: 16px;
  padding: 20px 0;
  background: #fff;
  margin-top: 20px;

  .filter-box {
    padding: 0 15px;

    .filter-item {
      cursor: pointer;
      width: 80px;
      height: 32px;
      border-radius: 4px;
      line-height: 32px;
      text-align: center;
      color: #666666;
      background: #EEEEEE;
    }

    .selected {
      color: #1B77F5;
      border-color: #1B77F5;
      background: #DEECFE;
    }
  }

  .itembox {
    display: flex;
    gap: 20px;
    flex-direction: row;
    flex-wrap: wrap;
  }
}

::v-deep .el-divider--vertical {
  margin: 0 15px !important;
}

.paging {
  margin-top: 20px;
  padding: 0 20px;
  //text-align: center;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .page--left {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;

    ::v-deep.el-input__inner {
      height: 32px !important;
    }
  }
}

::v-deep.el-pagination.is-background .btn-next {
  background-color: rgb(255, 255, 255, 0) !important;
}

::v-deep.el-pagination.is-background .btn-prev {
  background-color: rgb(255, 255, 255, 0) !important;
}

::v-deep.el-pagination.is-background .el-pager li {
  background-color: rgb(255, 255, 255, 0) !important;
}

::v-deep.paging {
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background: #1B77F5 !important;
  }
}

::v-deep.paging {
  .el-select-dropdown__item.selected {
    background: #1B77F5 !important;
  }
}

.search_class {
  width: 100%;

  ::v-deep.el-input__inner {
    height: 32px !important;
  }

  ::v-deep.el-form-item {
    margin-bottom: 0;
  }

  .open-icon {
    width: 22px;
    height: 32px;
    border-radius: 3px;
    opacity: 1;
    background: #DEECFE;
    cursor: pointer;
  }
}

.search-box {
  width: 280px;
  font-size: 14px !important;
  height: 32px;
  margin-top: 2px;
  line-height: 32px;
  border-radius: 4px;
  border: 1px solid #1B77F5;
  display: flex;
  position: relative;

  .input-box {
    flex: 1;
    display: flex;
    align-items: center;

    .input {
      width: 100%;
      box-sizing: border-box;
      height: 30px;
      padding: 0 6px 0 10px;
      line-height: 30px;
      font-size: 15px;
      outline: none;
      border: none;

      &::-webkit-input-placeholder {
        color: rgba(0, 0, 0, 0.4);
      }
    }
  }

  .btn-class {
    width: 80px;
    height: 32px;
    cursor: pointer;
    background: #1B77F5;
    text-align: center;
    color: #fff;
    line-height: 32px;
  }


}

::v-deep {
  .el-icon-date {
    //display: none;
  }

  .el-input__prefix {
    right: 20px;
    left: unset;
  }

  .el-input--prefix .el-input__inner {
    padding-left: 5px;
  }
}

input::-webkit-input-placeholder {
  font-size: 14px;
}

.card-img--class:hover {

}

::v-deep.el-select-dropdown__item {
  text-align: center;
}

::v-deep.el-date-editor .el-range__icon {
  display: none;
}

.date-picker--class {
  position: relative;
}

.date-time--icon {
  position: absolute;
  top: 15px;
  right: 8px
}
</style>
