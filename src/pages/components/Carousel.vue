<template>
  <div class="carousel-box">
    <el-carousel :interval="5000" height="270px" v-if="data.length!==0">
      <el-carousel-item v-for="item in data"
                        :key="item.id"
                        style="border-radius: 10px">
        <img class="img-pic"
             :src="item.imgUrl"
             @click="handleClick(item)">
      </el-carousel-item>
    </el-carousel>
    <el-carousel height="270px" v-if="data.length===0">
      <el-carousel-item style="border-radius: 10px">
        <img class="img-pic" src='@/assets/images/banner.png'>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import {getAppSpaceToken} from "@/api/home";
import {mapGetters} from 'vuex'

export default {
  props: {
    data: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['role'])
  },
  methods: {
    handleClick(item) {
      if (item.appInfoId) {
        getAppSpaceToken({
          appId: item.appId,
          appInfoId: item.appInfoId,
          thisRoleKey: this.role.role_key,
          thisAgencyId: this.role.agency_id,
          thisStageId: this.role.stage_id,
          childId: this.role.child_id
        }).then(res => {
          if (res.code == 200) {
            if (item.linkUrl.indexOf("?") !== -1) {
              let url = item.linkUrl + '&token=' + res.data
              window.open(url, '_blank')
            } else {
              window.open(item.linkUrl + '?token=' + res.data, '_blank')
            }
          }

        })
      } else {
        window.open(item.linkUrl, "_blank")
        // this.$router.push({
        //   path: item.linkUrl
        // })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.carousel-box {
  border-radius: 10px;

  .img-pic {
    object-fit: fill;
    cursor: pointer;
    width: 680px;
    height: 270px;
    border-radius: 10px
  }
}
</style>
