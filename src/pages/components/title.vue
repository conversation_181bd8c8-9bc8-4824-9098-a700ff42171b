<template>
  <div>
    <div class="title">
      <div style="display:flex;align-items: center">
        <div style="display: flex;align-items:center;">
          <div>
            <img style="width: 30px;height:30px;" :src="currentPic">
          </div>&nbsp;
          <div style="font-size: 18px;font-weight: bold">{{ title }}</div>
        </div>
        <div class="c_title" style="margin-left: 10px;font-size: 12px;font-weight: bold">
          <slot></slot>
        </div>
      </div>
      <div v-if="showMore" class="pointer fs_14" @click="$emit('clickMore')">
        <span style="color:#666666">更多</span>
        <i class="el-icon-caret-right"></i>
      </div>
      <div v-if="showManage" class="box" @click="$emit('handleManage')">{{ manageTitle }}</div>
      <div v-if="showBtn">
        <div class="title-btn" @click="clickApps">{{ btnTitle }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    showMore: {
      type: Boolean,
      default: true
    },
    showManage: {
      type: Boolean,
      default: false
    },
    showBtn: {
      type: Boolean,
      default: false
    },
    btnTitle: {
      type: String,
      default: ''
    },
    manageTitle: {
      type: String,
      default: function () {
        return '设置'
      }
    },
    showDivider: {
      type: Boolean,
      default: true
    },
    icon: {
      type: Number,
      default: 0
    },
  },
  computed: {
    currentPic() {
      if (this.icon === 1) {
        return this.appPic
      } else if (this.icon === 2) {
        return this.toolPic
      } else if (this.icon === 3) {
        return this.resPic
      } else if (this.icon === 4) {
        return this.msgPic
      } else if (this.icon === 5) {
        return this.addPic
      } else {
        return ''
      }
    }
  },
  data() {
    return {
      appPic: require('@/pages/images/img2/app_pic.png'),
      toolPic: require('@/pages/images/img2/tool_pic.png'),
      resPic: require('@/pages/images/img2/res_pic.png'),
      msgPic: require('@/pages/images/img2/msg_pic.png'),
      addPic: require('@/pages/images/img2/add.png')
    }
  },
  methods: {
    clickApps() {
      this.$emit('btnClick')
      // this.$router.push({
      //   path:"/apps"
      // })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-divider--horizontal {
  margin: 10px 0 !important;
}

.box {
  width: 80px;
  height: 30px;
  margin-bottom: 10px;
  cursor: pointer;
  font-size: 15px;
  text-align: center;
  line-height: 30px;
  background: linear-gradient(90deg, #64A3F8 0%, #1B77F5 100%);
  border-radius: 5px;
  color: #fff;
}

.box:active {
  opacity: .7;
}

.title-btn {
  //padding: 5px 15px;
  font-size: 13px;
  cursor: pointer;
  //border-radius: 15px;
  color: #4C94F7;
  //background: #3888f7;
}

.title-btn:active {
  opacity: .7;
}
</style>
