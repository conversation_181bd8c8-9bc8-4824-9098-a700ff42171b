<!--分页组件-->
<template>
  <div class="zh-pagination">
    <div class="zh-pagination-left"></div>
    <el-pagination
        :page-size="zhPageSize"
        background
        :total="total"
        layout="prev, pager, next, jumper"
        :current-page="zhPageNum"
        @current-change="handleCurrentChange"></el-pagination>
    <div class="zh-pagination-right">
      <div style>共{{ total }}条</div>&nbsp;&nbsp;
      <el-select v-model="currentPage" style="width: 108px;"
                 @change="changePageList">
        <el-option v-for="(item,index) in pages"
                   :key="index"
                   :label="item.label"
                   :value="item.value"
        ></el-option>
      </el-select>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    total: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 0
    },
    pageNum: {
      type: Number,
      default: 0
    },
    storageType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pages: [
        {
          label: '10条/页',
          value: 1
        },
        {
          label: '20条/页',
          value: 2
        },
        {
          label: '30条/页',
          value: 3
        },
        {
          label: '40条/页',
          value: 4
        }
      ],
      currentPage: 1,
      zhPageNum: 1,
      zhPageSize: 10,
      layout: 'total, sizes, prev, pager, next, jumper'
    }
  },
  computed: {},
  created() {
    this.$nextTick(function () {
      this.zhPageSize = this.pageSize;
      this.zhPageNum = this.pageNum;
    })
  },
  methods: {
    changePageList(val) {
      this.$emit('changePageList', val);
    },
    handleCurrentChange(page) {
      this.$emit('zhPageChange', page);
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep.zh-pagination {
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background: #1B77F5 !important;
  }

  .el-select-dropdown__item.selected {
    background: #1B77F5 !important;
  }

  .el-pagination.is-background .btn-next {
    background-color: rgb(255, 255, 255, 0) !important;
  }

  .el-pagination.is-background .btn-prev {
    background-color: rgb(255, 255, 255, 0) !important;
  }

  .el-pagination.is-background .el-pager li {
    background-color: rgb(255, 255, 255, 0) !important;
  }
}


.zh-pagination {
  margin-top: 20px;
  padding: 0 20px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .zh-pagination-left {
    width: 160px;
  }

  .zh-pagination-right {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;

    ::v-deep.el-input__inner {
      height: 32px !important;
    }
  }
}
</style>
