<template>
  <div class="login-dialog">
    <MyDialog
        v-if="showPhoneDialog"
        @close="(showPhoneDialog = false), closeLogin(), getCode()"
        dialogWidth="450px"
        title="填写用户信息"
        @confirm="$refs.bindInfo.getFormsDatas()">
      <MyForms
          :columns="bindInfo"
          editable
          ref="bindInfo"
          :modify="true"
          @formsDatas="bindUser"></MyForms>
    </MyDialog>
    <el-dialog
        :visible.sync="showLogin"
        @close="closeLogin()"
        @open="handleOpen"
        :close-on-click-modal="false"
        class="loginDialog"
        width="50rem"
        append-to-body>
      <div v-if="!isWarning"
           class="login-box"
           style="position: relative"
           v-loading="loading">
        <div class="warp">
          <div v-if="false" style="position: absolute;right:10px;cursor: pointer" @click="changeLoginType">
            <img style="width: 55px;height:55px;" :src="loginMode==0?code:code2">
          </div>
          <div class="title">
            <img src="../../../assets/logo.png"/>
            智汇云
            <!--            <img src="../../assets/logoTitle.png"/>-->
          </div>
          <div v-if="loginMode == 0">
            <div class="p_tb_20 fw_blod fs_20">用户登录</div>
            <el-form
                :model="userInfos"
                :rules="rules"
                ref="loginForm">
              <el-form-item label-width="70px" prop="username" label="账号">
                <el-input
                    placeholder="请输入账号"
                    clearable
                    v-model="userInfos.username"></el-input>
              </el-form-item>
              <el-form-item label-width="70px" prop="password" label="密码">
                <el-input
                    show-password
                    placeholder="请输入密码"
                    clearable
                    @keyup.enter.native="submit"
                    v-model="userInfos.password"
                    type="password"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                    type="primary"
                    style="width: 100%"
                    @click="submit">
                  登录
                </el-button>
              </el-form-item>
              <el-form-item>
                <div style="display: flex;justify-content: space-between">
                  <el-form-item prop="remember">
                    <el-checkbox
                        label="记住密码"
                        v-model="userInfos.remember"></el-checkbox>
                  </el-form-item>
                  <el-form-item>
                    <div style="color:rgb(40, 121, 255);cursor: pointer" @click="resetPwd">重置密码</div>
                  </el-form-item>
                </div>
              </el-form-item>

            </el-form>
          </div>
          <div v-else>
            <div class="p_tb_20 fw_blod fs_20 flex just_center">扫码登录</div>
            <div
                style="margin-top: -12px; text-align: center; color: #000"
                class="fs_14 opacity_4">
              请使用微信扫码二维码登录
            </div>
            <div class="wechart">
              <img
                  :src="qrdata.qrdata"
                  width="100%"/>
              <div style="text-align: center;color:rgb(40, 121, 255)"
                   @click="handleRefresh"
                   v-if="showCount">刷新
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="isWarning" class="login-box"
           style="position: relative">
        <div class="warp2">
          <div class="p_tb_20 fw_blod fs_20">重置密码</div>
          <el-form label-width="93px"
                   class="resetPwd"
                   :model="waningParams"
                   :rules="rules2"
                   ref="loginForm2">
            <el-form-item label="账号" prop="username">
              <el-input
                  placeholder="请输入账号"
                  clearable
                  v-model="waningParams.username"></el-input>
            </el-form-item>
            <el-form-item label="旧密码" prop="oldPwd">
              <el-input
                  show-password
                  clearable
                  placeholder="请输入旧密码"
                  v-model="waningParams.oldPwd"
                  type="password"></el-input>
            </el-form-item>
            <el-form-item label="新密码" prop="newPwd">
              <el-input
                  show-password
                  clearable
                  placeholder="请输入新密码"
                  v-model="waningParams.newPwd"
                  type="password"></el-input>
            </el-form-item>
            <el-form-item label="确定新密码" prop="confirmNew">
              <el-input
                  show-password
                  clearable
                  placeholder="请再次输入新密码"
                  @keyup.enter.native="submitReset"
                  v-model="waningParams.confirmNew"
                  type="password"></el-input>
            </el-form-item>
          </el-form>

          <div style="margin-bottom: 10px;display: flex;padding:0 0 0 18px;color:#F56C6C">
            <div style="width:90px;word-break: keep-all">
              密码规则:
            </div>&nbsp;
            <div>8~20个字符，需含大、小写字母、数字及特殊字符（~!@#$%^&*()[]{}<>?+），不含空格。</div>
          </div>
          <div style="text-align:center;display: flex;justify-content: space-between;
          align-items: center;margin-bottom: 20px;padding:0 20px">
            <el-button style="width:90%"
                       type="primary"
                       @click="back">
              返回
            </el-button>
            <el-button style="width:90%"
                       type="primary"
                       @click="submitReset">
              确定
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {clear, get, set} from "@/utils/local";
import {mapGetters} from 'vuex'
import {bindUser, checkLogin, getWxqr, resetPwd2} from "@/api/login";
import MyForms from "@/components/MyForms.vue";
import MyDialog from "@/components/MyDialog.vue";
import {servicesLoading} from "@/pages/utils/tools";
import code from '@/assets/images/Group2182.png';
import code2 from '@/assets/images/Group2183.png';
import {isValidPassword} from "@/utils/tools";

const validChangePwd = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('不能为空'))
  } else {
    if (isValidPassword(value)) {
      callback()
    } else {
      return callback(new Error('密码格式错误'))
    }
  }
}
export default {
  components: {MyDialog, MyForms},
  data() {
    return {
      code,
      code2,
      loginMode: 0,
      loading: false,
      showLogin: false,
      userInfos: {
        username: '',
        password: '',
        remember: '',
      },
      isWarning: false,
      waningParams: {
        username: '',
        newPwd: '',
        oldPwd: '',
        confirmNew: ''
      },
      rules2: {
        username: [
          {
            required: true,
            message: '请输入账号',
            trigger: 'blur',
          },
        ],
        oldPwd: [
          {
            required: true,
            message: '请输入旧密码',
            trigger: 'blur',
          },
        ],
        newPwd: [
          {
            required: true,
            validator: validChangePwd,
            trigger: 'blur',
          },
        ],
        confirmNew: [
          {
            required: true,
            validator: validChangePwd,
            trigger: 'blur',
          },
        ]
      },
      showPhoneDialog: false,
      qrdata: '',
      rules: {
        username: [
          {
            required: true,
            message: '请输入账号',
            trigger: 'blur',
          },
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur',
          },
        ],
      },
      bindInfo: [
        {
          prop: 'phone',
          label: '手机号',
          placeholder: '请输入手机号',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'password',
          label: '密码',
          placeholder: '请输入密码',
          type: 'password',
          default: '',
          required: true,
        },
      ],
      countTimer: '',
      countTime: '',
      showCount: false
    }
  },
  created() {
    //检查是否记住密码
    if (get('loginInfo')) {
      this.userInfos = get('loginInfo');
    }
  },
  computed: {
    ...mapGetters(['name', 'userInfo', 'avatar', 'role', 'roles', 'defRole']),
  },
  watch: {
    loginMode: {
      immediate: true,
      handler(val) {
        clearInterval(this.timer);
        //显示二维码
        if (val) {
          this.lock = true;
          this.getCode();
        }
      },
    },
  },
  methods: {
    resetPwd() {
      this.isWarning = true
      this.waningParams.username = this.userInfos.username
      this.waningParams.oldPwd = this.userInfos.password
      this.waningParams.newPwd = ''
      this.waningParams.confirmNew = ''
    },
    back() {
      this.isWarning = false
      this.waningParams.newPwd = ''
      this.waningParams.confirmNew = ''
    },
    submitReset() {
      this.$refs.loginForm2.validate(valid => {
        if (valid) {
          if (this.waningParams.newPwd != this.waningParams.confirmNew) {
            this.$message({
              message: '两次新密码不一致,请重新输入！',
              type: 'error'
            })
          } else {
            let params = {
              username: this.waningParams.username,
              oldPwd: this.waningParams.oldPwd,
              newPwd: this.waningParams.newPwd
            }
            resetPwd2(params).then(res => {
              this.$message({
                message: '修改成功，请重新登录',
                type: 'success'
              })
              this.userInfos.password = ''
              this.isWarning = false
            })
          }
        }
      })
    },
    handleOpen() {
      this.showPhoneDialog = false
      this.isWarning = false
    },
    //获取二维码
    async getCode() {
      this.times = 0;
      this.qrdata = '';
      //开始计时200秒后刷新
      this.timer = setInterval(() => {
        this.times++;
        if (this.times > 100) {
          this.getCode();
        }
        if (this.lock) {
          this.checkLogin();
        }
      }, 1000);
      const {data: result} = await getWxqr();
      this.qrdata = result;
    },
    handleCountTime() {
      const time_count = 60
      if (!this.countTimer) {
        this.countTime = time_count
        this.showCount = false
        this.countTimer = setInterval(() => {
          if (this.countTime > 0 && this.countTime <= time_count) {
            this.countTime--
          } else {
            this.showCount = true
            clearInterval(this.countTimer)
            this.countTimer = null
          }
        }, 1000)
      }
    },
    async handleRefresh() {
      const {data: result} = await getWxqr();
      this.qrdata = result;
      clearInterval(this.countTimer)
      this.showCount = false
    },
    // 登录方式切换
    changeLoginType() {
      if (this.loginMode === 0) {
        this.loginMode = 1
        this.handleCountTime()
      } else if (this.loginMode === 1) {
        this.loginMode = 0
      }
    },
    //查询用户登录信息
    async checkLogin() {
      if (this.qrdata) {
        const {data: result} = await checkLogin({sid: this.qrdata.sid});
        //扫码后
        if (result.code === 1003) {
          var loading = servicesLoading('.login-box', '正在登录', true)
        }
        if (result.code != 1001 && result.code !== 1003) {
          this.lock = false;
          clearInterval(this.timer);
          if (result.code == 1002) {
            this.$modal.msgWarning('微信号未绑定，请填写信息');
            this.showPhoneDialog = true;
          }
          if (result.code == 1000) {
            this.$store.dispatch('SetToken', result.token);
            //获取用户信息
            this.$store.dispatch('GetInfo').then(() => {
              //获取用户角色
              this.$store.dispatch('GetRole').then((res) => {
                //设置第一个角色作为默认角色
                if (res.data.length > 0) {
                  this.$modal.msgSuccess('登录成功');
                  this.$store.dispatch('SetRole', res.data[0]);
                  this.switchHandle(res.data[0]);
                } else {
                  this.switchHandle({});
                  this.$modal.msgWarning('当前用户无角色，请联系管理员');
                }
                loading.close()
              });
            })
          }
        }
      }
    },
    //切换角色
    switchHandle(item) {
      this.$store.dispatch('SetRole', item).then((res) => {
        if (item.role_type === 'wor') {
          this.$router.push({path: '/school'});
        } else if (item.role_type === 'tea') {
          this.$router.push({path: '/teacher'});
        } else if (item.role_type === 'stu') {
          this.$router.push({path: '/student'});
        } else if (item.role_type === 'par') {
          this.$router.push({path: '/parents'});
        } else {
          this.$router.push({path: '/manage'});
        }
      })
    },
    //用户普通模式登录
    login() {
      this.showLogin = true;
      if (this.loginMode) {
        this.qrdata = '';
        this.getCode();
      }
    },
    submit() {
      this.$refs['loginForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$store
              .dispatch('Login', this.userInfos)
              .then((result) => {
                if (result.code === 1006) {
                  this.isWarning = true
                  this.waningParams.username = this.userInfos.username
                  this.waningParams.oldPwd = this.userInfos.password
                  this.waningParams.newPwd = ''
                  this.waningParams.confirmNew = ''
                  this.loading = false;
                  // return Promise.reject(result.msg)
                } else if (result.code === 200) {
                  //获取用户信息
                  this.$store.dispatch('GetInfo').then(() => {
                    //获取用户角色
                    this.$store.dispatch('GetRole').then((res) => {
                      //设置第一个角色作为默认角色
                      if (res.data && res.data.length > 0) {
                        this.$modal.msgSuccess('登录成功');
                        this.$store.dispatch('SetRole', res.data[0]);
                        this.switchHandle(res.data[0]);
                      } else {
                        this.switchHandle({});
                        this.$modal.msgWarning('当前用户无角色，请联系管理员');
                      }
                    });
                  })
                  this.showLogin = false;
                  this.loading = false;
                  if (this.userInfos.remember == true) {
                    // const params={
                    //   username:Base64.encode(this.userInfos.username),
                    //   password:Base64.encode(this.userInfos.password),
                    // }
                    // set('loginInfo', params);
                    set('loginInfo', this.userInfos);
                  } else {
                    clear('loginInfo');
                  }
                }
              })
              .catch((err) => {
                this.loading = false;
              }).finally(() => {
            this.loading = false
          })
        }
      });
    },
    //关闭登录弹窗
    closeLogin() {
      setTimeout(() => {
        clearInterval(this.timer);
      }, 1000);
      clearInterval(this.countTimer)
      this.lock = true;
      this.loginMode = 0
    },
    //微信绑定用户
    bindUser(e) {
      e.sid = this.qrdata.sid;
      bindUser(e).then((res) => {
        if (res.code == 200) {
          this.$modal.msgSuccess('绑定成功');
          this.$store.dispatch('SetToken', res.data.token);
          //获取用户信息
          this.$store.dispatch('GetInfo').then(() => {
            //获取用户角色
            this.$store.dispatch('GetRole').then((result) => {
              //设置第一个角色作为默认角色
              if (result.data.length > 0) {
                this.$store.dispatch('SetRole', result.data[0]);
                this.switchHandle(result.data[0]);
              } else {
                this.switchHandle({});
                this.$modal.msgWarning('当前用户无角色，请联系管理员');
              }
            });
          })
        }
      });
    },
  }
}
</script>
<style lang="scss" scoped>
//登录框
.loginDialog {
  ::v-deep .el-dialog {
    background-color: transparent;
    box-shadow: none;
  }

  ::v-deep .el-dialog__headerbtn {
    right: 0px;
    top: 30px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 25px;
    height: 25px;
  }

  .login-box {
    //color: #0f4444;
    background-color: #fff;
    border-radius: 0.8rem;
    display: flex;
    // padding-bottom: 1px;
    .warp2 {
      width: 90%;
      // padding: 1rem 0;
      padding-top: 2.5rem;

      margin: 0 auto;

      .title {
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 2.8rem;
        letter-spacing: 0.25rem;
        display: flex;
        justify-content: center;
        align-items: center;
        color: black;

        img {
          height: 3.6rem;
          padding: 0 0.6rem;
        }
      }
    }

    .warp {
      width: 70%;
      // padding: 1rem 0;
      padding-top: 2.5rem;

      margin: 0 auto;

      .title {
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 2.8rem;
        letter-spacing: 0.25rem;
        display: flex;
        justify-content: center;
        align-items: center;
        color: black;

        img {
          height: 3.6rem;
          padding: 0 0.6rem;
        }
      }
    }
  }
}

.wechart {
  height: 210px;
  width: 210px;
  margin: 10px auto 32px auto;
}

.resetPwd {
  ::v-deep.el-form-item {
    //margin-bottom: 32px !important;
  }
}
</style>
