<template>
  <div class="v-button" id="button" :class="typeClass">
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    type: {
      type: String,
      default: 'default'
    }
  },
  computed: {
    typeClass() {
      return 'v-button--' + this.type
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.v-button {
  display: inline-block;
  font-size: 14px;
  padding: 6px 18px;
  border-radius: 5px;
  cursor: pointer;
  min-width: 45px;
  text-align: center;
  user-select: none;
}

.v-button:active {
  opacity: .7;
}

.v-button--dangerous {
  background: rgba(252, 83, 83, 0.1);
  color: #FC5353;
}

.v-button--primary {
  background: #1B77F5;
  color: #fff;
}

.v-button--active-color {
  opacity: .7;
}

.v-button--primary-plain {
  background: #DEECFE;
  color: #1B77F5;
}

.v-button--success {
  background: #67c23a;
  color: #fff;
}

.v-button--info {
  background: #F0F0F0;
  color: #333;
}
</style>
