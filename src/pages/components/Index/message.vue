<template>
  <div style="width:100%" class="pointer" @click="handleClick">
    <div class="flex">
      <div class="title fileClass" style="width: 90%">
        <my-tooltip style="width: 100%"
                    :title=" info.noticeTitle ">

        </my-tooltip>
      </div>
    </div>
    <div class="time" style="margin-right: 5px">{{ dayjs()(info.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
    <el-divider></el-divider>
  </div>
</template>
<script>
import dayjs from "dayjs";
import MyTooltip from "@/components/Tooltip.vue";

export default {
  components: {MyTooltip},
  methods: {
    dayjs() {
      return dayjs
    },
    handleClick() {
      this.$router.push({
        path: '/oa/audit',
        query: {
          type: 0,
          id: this.info.contentId
        }
      })
    }
  },
  props: {
    info: {
      type: Object,
      default: function () {
        return {}
      },
    }
  },
  data() {
    return {}
  },
}
</script>
<style lang="scss" scoped>
.title {
  position: relative;
  width: 190px;
  padding-left: 5px;
  font-size: 15px;
  font-weight: 400;
  text-align: LEFT;
  color: #666666;
  line-height: 25px;
}

.title:hover {
  color: #4C94F7;
}

.title::before {
  position: absolute;
  content: "";
  top: 5px;
  left: 0;
  height: 60%;
  border-radius: 5px;
  width: 2px;
  background: #4C94F7;
}

.time {
  text-align: right;
  color: #999999;
  text-indent: 5px;
  font-size: 12px;
}


::v-deep.el-divider--horizontal {
  margin: 5px 0 !important;
}
</style>
