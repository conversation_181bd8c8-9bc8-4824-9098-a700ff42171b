<template>
  <div class="top-title-box">
    <div class="title-box">
      <div>
        <img :src="currentPic">
      </div>&nbsp;
      <div class="title">{{ title }}</div>
    </div>
    <div class="check-box" @click="$emit('toDetail')">查看全部
      <i class="el-icon-arrow-right"></i></div>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    icon: {
      type: Number,
      default: 0
    },
    tipName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      appPic: require('@/pages/images/img2/app_pic.png'),
      toolPic: require('@/pages/images/img2/tool_pic.png'),
      resPic: require('@/pages/images/img2/res_pic.png'),
      msgPic: require('@/pages/images/img2/msg_pic.png'),
    }
  },
  computed: {
    currentPic() {
      if (this.icon === 1) {
        return this.appPic
      } else if (this.icon === 2) {
        return this.toolPic
      } else if (this.icon === 3) {
        return this.resPic
      } else if (this.icon === 4) {
        return this.msgPic
      } else {
        return ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.top-title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-box {
    display: flex;
    align-items: center;

    .title {
      //font-family: iconfont, serif;
      font-size: 18px;
      font-weight: bold
    }
  }

  .check-box {
    color: #999999;
    font-size: 12px;
    cursor: pointer;
  }

  .check-box:active {
    opacity: .7;
  }
}
</style>
