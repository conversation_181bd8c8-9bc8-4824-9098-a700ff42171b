<template>
  <div>
    <div v-if="showType===1" class="resource-item box_shadow">
      <img :src="info.coverUrl"
           class="imgPic">
      <div class="right-box">
        <div class="title">{{ info.title }}</div>
        <div class="btn" @click="toDetail">
          查看更多
        </div>
      </div>
    </div>
    <div v-if="showType===2"
         :class="showMore?'mtb_10 resource-item2':'resource-item3'"
         @click="clickResource">
      <div>
        <img :src="info.coverUrl">
      </div>
      <div class="content">
        <div>{{ info.introduce }}</div>
        <div class="tag"></div>
      </div>
    </div>
  </div>
</template>

<script>
import {getListByClass, getLoginTicket, toLogin} from "@/api/guojia";
import {getOftenApp} from "@/api/home";
import {mapGetters} from "vuex";

export default {
  props: {
    info: {
      type: Object,
      default: function () {
        return {}
      }
    },
    showType: {
      type: Number,
      default: 0
    },
    showMore: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {}
  },computed:{
    ...mapGetters(['userInfo'])
  },
  methods: {
    clickResource() {
      this.$router.push({
        path: '/detail',
        query: {
          id: this.info.id
        }
      })
    },
    toDetail() {

      if(this.info.id=="gj"){
        getListByClass().then(res => {
          // console.log(res.data);
          if(res.data.data != null){
            var bindStatus = res.data.data.bindStatus;
            if("1" == bindStatus){//已绑定
              getLoginTicket().then(ticket => {
                // console.log(ticket);
                window.location.href = "https://auth.smartedu.cn/uias/collect/toView?tab=5&clientId=3iR37M3ARO2TkTJnVEekb7ZOya1lfynY&ticket="+ticket.msg;
              });
            }else{//未绑定
              this.$confirm("智教通行证是访问国家、各级各类智慧教育平台和应用系统的通用账号，请您尽快绑定智教通行证。", '提示', {
                confirmButtonText: '绑定通行证',
                showCancelButton: false,
                type: 'success',
              }).then(() => {
                var userId = this.userInfo.userId;
                var userName = this.userInfo.userName;
                toLogin().then(param => {
                  //console.log(param);
                  //console.log(encodeURI(param));
                  //encodeURIComponent()
                  window.open('https://auth.smartedu.cn/uias/collect/third/toLogin?'+param, '_blank');
                });
              })
            }
          }
        })
      }else{
        window.open(this.info.url, '_blank');
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-item {
  display: flex;
  justify-content: space-between;
  border-radius: 10px;
  background: #fff;

  .right-box {
    padding-right: 10px;
    display: flex;
    width: 250px;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title {
      font-size: 20px;
      font-weight: bold;
    }

    border-radius: 10px;

    .btn {
      margin-top: 20px;
      width: 100px;
      text-align: center;
      padding: 10px 30px;
      color: #fff;
      cursor: pointer;
      border-radius: 8px;
      background: #3888f7;
    }
  }

}

.mtb_10 {
  margin: 10px;
}

.resource-item2 {
  background: #fff;
  cursor: pointer;
  border-radius: 10px;
  width: 250px;
  padding-bottom: 5px;
  box-shadow: 0px 6px 6px rgba(0, 0, 0, 0.05);

  .content {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    padding: 5px 10px 0 10px;
    font-size: 14px;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  img {
    object-fit: fill;
    width: 250px;
    height: 170px;
    border-radius: 10px
  }
}

.resource-item3 {
  background: #fff;
  cursor: pointer;
  border-radius: 10px;
  width: 277px;
  padding-bottom: 5px;
  box-shadow: 0px 6px 6px rgba(0, 0, 0, 0.05);

  .content {
    overflow: hidden;
    position: relative;
    text-overflow: ellipsis;
    display: -webkit-box;
    padding: 0 10px 0 10px;
    font-size: 14px;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;

    .tag {
      position: absolute;
      left: 5px;
      top: 3px;
      width: 2px;
      height: 13px;
      border-radius: 2px;
      opacity: 1;
      background: #4C94F7;
    }
  }

  img {
    object-fit: fill;
    width: 277px;
    height: 170px;
    border-radius: 10px
  }

}

.imgPic {
  object-fit: fill;
  width: 270px;
  height: 170px;
  border-radius: 10px
}
</style>
