<template>
  <!--  <div class="resource-box">-->
  <!--    <resource v-for="item in resourceList"-->
  <!--              :key="item.id"-->
  <!--              :info="item"-->
  <!--              :show-type="1"-->
  <!--    ></resource>-->
  <!--  </div>-->
  <div>
    <Header @refresh="refresh"></Header>
    <div style="padding-top: 75px"></div>
    <div class="sub-page-container">
      <div class="resource-box">
        <resource v-for="item in resourceList"
                  :key="item.id"
                  :info="item"
                  :show-type="1"
        ></resource>
      </div>
    </div>
  </div>
</template>
<script>
import resource from "@/pages/resource/resource.vue";
import Header from "@/pages/layout/pageLayout/Header.vue";

export default {
  components: {
    Header,
    resource
  },
  data() {
    return {
      resourceList: [
        {
          id: "gj",
          coverUrl: require('@/pages/images/resource_img/gjzhjy.png'),
          url: 'https://www.smartedu.cn/',
          title: '国家智慧教育公共服务平台'
        },
        {
          id: "she",
          coverUrl: require('@/pages/images/resource_img/sczhjy.png'),
          url: 'https://www.sc.smartedu.cn/',
          title: '四川智慧教育平台'
        },
        {
          id: "shi",
          coverUrl: require('@/pages/images/resource_img/cdzhjy.png'),
          url: 'https://educloud.cdedu.com/',
          title: '成都智慧教育平台'
        },
        {
          id: "qu",
          coverUrl: require('@/pages/images/resource_img/zhyzy.png'),
          url: 'https://www.wuhousmartedu.com/#/resource/region',
          title: '智慧云资源'
        }
      ]
    }
  },
  mounted() {
  },
  methods: {
    refresh() {
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-box {
  font-size: 16px;
  padding: 0 50px;
  display: grid;
  grid-template-columns: 50% 50%;
  gap: 20px;
}

.paging {
  margin-top: 10px;
  text-align: center;
}
</style>
