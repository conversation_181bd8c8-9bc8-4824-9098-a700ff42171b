<template>
  <div :class="showMore?'m10':''" class="app-box" @click="clickApp">
    <img style="" class="picImg" :src="info.coverUrl">
    <div class="title">
      <div>{{ info.title }}</div>
      <div class="tag"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    info: {
      type: Object,
      default: function () {
        return {}
      }
    },
    showMore: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      img: require('@/pages/images/Group 3377.png'),
    }
  },
  methods: {
    clickApp() {
      this.$router.push({
        path: '/detail',
        query: {
          id: this.info.id
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.app-box {
  width: 250px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  box-shadow: 0px 6px 6px rgba(0, 0, 0, 0.05);

  .picImg {
    object-fit: fill;
    width: 250px;
    height: 167px;
    border-radius: 5px
  }

  .title {
    position: relative;
    font-size: 14px;
    padding: 0 10px 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;

    .tag {
      position: absolute;
      left: 5px;
      top: 3px;
      width: 2px;
      height: 13px;
      border-radius: 2px;
      opacity: 1;
      background: #4C94F7;
    }
  }
}

.m10 {
  margin: 10px;
}

</style>
