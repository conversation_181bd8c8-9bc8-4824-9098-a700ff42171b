<template>
  <div>
    <Header @refresh="refresh"></Header>
    <div style="padding-top: 75px"></div>
    <div class="fs_16 sub-page-container apps-box " v-loading="vLoading">
      <div class="bg_fff p_20 br_5 box_shadow">
        <Title :show-more="false" :show-manage="showManage"
               :icon="5"
               :manage-title="manageTitle"
               @handleManage="handleManage"
               @clickMore="$emit('handleClick')" :title="'应用管理'">
          可拖动调整应用顺序
        </Title>
        <draggable :disabled="disabled"
                   :list="appList"
                   animation="500"
                   force-fallback="true"
                   @change="onChange"
                   class="m_t_10 app_grid_more ">
          <div v-for="item in appList" :key="item.appInfoId">
            <set-app-item :item="item" class=" ban-select"
                          v-if="toBoolean(item.myApp)"
                          @appClick="appClick(item)"
                          :is-add="toBoolean(item.myApp)"
                          :isAll="false"
                          :is-show="isShow"
                          @deleteApp="deleteApp(item)"></set-app-item>
          </div>
        </draggable>
        <div v-if="appList.length===0">
          <el-empty :image-size="80" description="暂无应用"></el-empty>
        </div>
      </div>
      <div v-if="isShow" class="m_t_20 bg_fff p_20 br_5 box_shadow">
        <div class="tab-box">
          <div class="pointer m_r_10 flex_alAndJsCenter"
               :class="showTag===1?'active':'no-active'"
               @click="changeTag(1)">
            <img src="@/pages/images/img2/app_pic.png">
            <div>我的应用</div>
          </div>
          <el-divider direction="vertical"></el-divider>
          <div class="m_l_10 pointer flex_alAndJsCenter"
               :class="showTag===2?'active':'no-active'"
               @click="changeTag(2)">
            <img src="@/pages/images/img2/tool_pic.png">&nbsp;
            <div>我的工具</div>
          </div>
        </div>
        <div v-loading="vLoadingAll">
          <div class="app_grid_more m_t_20">
            <!--      显示应用-->
            <div v-for="(item,index) in appArr" :key="index">
              <set-app-item :is-add="toBoolean(item.myApp)"
                            :is-show="isShow" class="pointer"
                            @appClick="appClick(item)"
                            :item="item"
                            :isAll="true"
                            :is-drag="true"
                            @addApp="addApp(item)"
                            @deleteApp="deleteApp(item)"
              ></set-app-item>
            </div>
          </div>
        </div>

        <div v-if="appArr.length===0">
          <el-empty :image-size="80"></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {getAppSpaceToken, getOftenApp, setAppSort, setUseApp} from "@/api/home";
import {mapGetters} from 'vuex'
import SetAppItem from "@/pages/apps/setAppItem.vue";
import Title from "@/pages/components/title.vue";
import draggable from 'vuedraggable'
import Header from "@/pages/layout/pageLayout/Header.vue";

export default {
  components: {Header, Title, SetAppItem, draggable},
  data() {
    return {
      allApp: [],
      vLoading: false,
      oftenUserApp: [],
      manageTitle: '设置',
      showManage: true,
      isShow: false,
      showTag: 1,
      appArr: [],
      disabled: true,
      draggableList: [],
      appList: [],
      params: {},
      listQuery: {},
      movedChange: false,
      vLoadingAll: false,
      tagTitle: ''
    }
  },
  computed: {
    ...mapGetters(['role'])
  },
  mounted() {
    this.showTag = Number(this.$route.query.type)
    this.loadOftenAppData()
    this.listQuery = {
      roleKey: this.role.role_key,
      agencyId: this.role.agency_id,
      stageId: this.role.stage_id ? this.role.stage_id : '',
      infoType: 0
    }
  },
  methods: {
    refresh() {
      this.showTag = Number(this.$route.query.type)
      this.loadOftenAppData()
      this.listQuery = {
        roleKey: this.role.role_key,
        agencyId: this.role.agency_id,
        stageId: this.role.stage_id ? this.role.stage_id : '',
        infoType: 0
      }
    },
    toBoolean(val) {
      return JSON.parse(val)
    },
    // 我的应用
    loadOftenAppData() {
      this.vLoading = true
      this.appList = []
      this.params = {
        roleKey: this.role.role_key,
        agencyId: this.role.agency_id,
        stageId: this.role.stage_id ? this.role.stage_id : ''
      }
      getOftenApp(this.params).then(res => {
        if (res.code == 200) {
          let data = res.data
          for (let i = 0; i < data.length; i++) {
            if ((data[i].pcTag === 0 || data[i].pcTag === '0')
                && (data[i].myApp === 'true' || data[i].myApp === true)) {
              this.appList.push(data[i])
            }
          }
        }
      }).finally(() => {
        this.vLoading = false
      })
    },
    loadAllApps() {
      this.appArr = []
      this.vLoadingAll = true
      getOftenApp(this.listQuery).then(res => {
        if (res.code == 200) {
          let data = res.data
          for (let i = 0; i < data.length; i++) {
            if ((data[i].pcTag === 0 || data[i].pcTag === '0')) {
              this.appArr.push(data[i])
            }
          }
        }
      }).finally(() => {
        this.vLoadingAll = false
      })
    },
    // 删除app
    deleteApp(item) {
      let params = {
        appId: item.appId,
        appInfoId: item.appInfoId
      }
      setUseApp(params).then(res => {
        this.$message({
          message: '设置成功',
          type: 'success'
        })
        this.$emit('success')
        this.loadOftenAppData()
        this.loadTagApp()
      })
    },
    // 添加应用
    addApp(item) {
      let params = {
        appId: item.appId,
        appInfoId: item.appInfoId
      }
      setUseApp(params).then(res => {
        this.$message({
          message: '设置成功',
          type: 'success'
        })
        this.$emit('success')
        this.loadOftenAppData()
        this.loadTagApp()
      })
    },
    // 管理应用
    handleManage() {
      this.isShow = !this.isShow
      // this.showTag = 1
      if (this.isShow) {
        this.tagTitle = '拖动调整应用顺序'
        this.manageTitle = '完成'
        this.disabled = false
        this.vLoading = true
        getOftenApp(this.params).then(res => {
          if (res.code == 200) {
            let data = res.data
            this.appList = []
            for (let i = 0; i < data.length; i++) {
              if ((data[i].pcTag === 0 || data[i].pcTag === '0') &&
                  (data[i].myApp === 'true' || data[i].myApp === true)) {
                this.appList.push(data[i])
              }
            }
          }
        }).finally(() => {
          this.vLoading = false
        })
        this.loadTagApp()
      } else {
        this.manageTitle = '设置'
        this.tagTitle = ''
        if (this.movedChange) {
          this.setSortAgain()
        } else {
          this.loadOftenAppData()
        }
        this.disabled = true
      }
    },
    setSortAgain() {
      this.draggableList = []
      for (let i = 0; i < this.appList.length; i++) {
        let obj = {
          appInfoId: this.appList[i].appInfoId,
          sort: i
        }
        this.draggableList.push(obj)
      }
      let params = {
        roleKey: this.role.role_key,
        agencyId: this.role.agency_id,
        stageId: this.role.stage_id ? this.role.stage_id : ''
      }
      setAppSort(params, this.draggableList).then(res => {
        this.draggableList = []
        this.movedChange = false
        this.loadOftenAppData()
        this.$message({
          message: '设置成功',
          type: 'success'
        })
      })
    },
    loadTagApp() {
      if (this.showTag === 1) {
        this.listQuery = {
          roleKey: this.role.role_key,
          agencyId: this.role.agency_id,
          stageId: this.role.stage_id ? this.role.stage_id : '',
          infoType: 0
        }
        this.loadAllApps()
      } else if (this.showTag === 2) {
        this.listQuery = {
          roleKey: this.role.role_key,
          agencyId: this.role.agency_id,
          stageId: this.role.stage_id ? this.role.stage_id : '',
          infoType: 1
        }
        this.loadAllApps()
      }
    },
    // tab切换
    changeTag(inx) {
      this.showTag = inx
      this.loadTagApp()
    },
    //   应用是否拖动
    onChange(e) {
      this.movedChange = true
    },
    // 常用应用点击
    appClick(item) {
      if (!this.isShow) {
        if ((item.appBeFrom === '2' || item.appBeFrom === 2) || (item.appBeFrom === '3' || item.appBeFrom === 3)) {
          window.open(item.redirectUrl, '_blank')
        } else {
          // 获取应用token
          getAppSpaceToken({
            appId: item.appId,
            appInfoId: item.appInfoId,
            thisRoleKey: this.role.role_key,
            thisAgencyId: this.role.agency_id,
            thisStageId: this.role.stage_id,
            childId: this.role.child_id
          }).then(res => {
            if (res.code == 200) {
              this.token = res.data
              // item.kjTag=0 跳转路由 当前框架下；item.kjTag=1：跳转地址，对接的应用
              if (item.kjTag == 0 || item.kjTag == '0') {
                this.$router.push({
                  path: item.redirectUrl
                })
              } else if (item.kjTag == 1 || item.kjTag == '1') {
                if (item.redirectUrl.indexOf("?") !== -1) {
                  let url = item.redirectUrl + '&token=' + this.token
                  window.open(url, '_blank')
                } else {
                  window.open(item.redirectUrl + '?token=' + this.token, '_blank')
                }
              }
            }
          })
        }
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.apps-box {
  .tab-box {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
  }
}

.app_grid_more {
  display: grid;
  grid-template-columns: repeat(6, 15%);
  gap: 20px 20px;
  justify-content: space-between;
}

.active {
  //color: #3888f7;
  border-bottom: 2px solid #4C94F7;
}

.no-active {
  border-bottom: 2px solid #fff;
}

.ban-select {
  -webkit-touch-callout: none; /*系统默认菜单被禁用*/
  -webkit-user-select: none; /*webkit浏览器*/
  -khtml-user-select: none; /*早期浏览器*/
  -moz-user-select: none; /*火狐*/
  -ms-user-select: none; /*IE10*/
  user-select: none;
}

::v-deep.el-empty {
  padding: 0 !important;
}

::v-deep.el-empty__description {
  margin-top: 0 !important;
}
</style>
