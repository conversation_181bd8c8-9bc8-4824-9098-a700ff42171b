<template>
  <div class="fs_14" :class="showMore?'app-box-more':'app-box'"
       @click="clickApp">
    <div>
      <img style="width: 40px;height:40px;"
           :src="info.iconUrl?info.iconUrl:img">
    </div>
    <div class="m_t_10" style="height:21px;line-height: 21px" :style="tagBgStyle">{{ info.appName }}</div>
  </div>
</template>
<script>
import {getAppSpaceToken} from "@/api/home";
import {mapGetters} from 'vuex'

export default {
  props: {
    info: {
      type: Object,
      default: function () {
        return {}
      }
    },
    showMore: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      img: require('@/pages/images/Group 3377.png'),
    }
  },
  computed: {
    ...mapGetters(['role']),

    bgStyle() {
      if (this.info.iconColor) {
        return {
          background: 'linear-gradient(107deg,#ffffff 0%, '
              + this.info.iconColor + ' 100%)'
        }
      } else {
        return {
          background: 'linear-gradient(107deg,#ffffff 0%, #ECF4FA 100%)'
        }
      }
    },
    tagBgStyle() {
      let charNums = this.info.appName.length;
      if (charNums > 6) {
        return {
          'font-size': '1.2rem'
        }
      }
      if (charNums > 4) {
        return {
          'font-size': '1.4rem'
        }
      } else {
        return {
          'font-size': '1.4rem'
        }
      }

    },
  },
  methods: {
    clickApp() {
      if ((this.info.appBeFrom === '2' || this.info.appBeFrom === 2) ||
          this.info.appBeFrom === '3' || this.info.appBeFrom === 3) {
        window.open(this.info.redirectUrl, '_blank')
      } else {
        // 获取应用token
        getAppSpaceToken({
          appId: this.info.appId,
          appInfoId: this.info.appInfoId,
          thisRoleKey: this.role.role_key,
          thisAgencyId: this.role.agency_id,
          thisStageId: this.role.stage_id,
          childId: this.role.child_id
        }).then(res => {
          if (res.code == 200) {
            this.token = res.data
            // item.kjTag=0 跳转路由 当前框架下；item.kjTag=1：跳转地址，对接的应用
            if (this.info.kjTag == 0 || this.info.kjTag == '0') {
              this.$router.push({
                path: this.info.redirectUrl
              })
            } else if (this.info.kjTag == 1 || this.info.kjTag == '1') {
              if (this.info.redirectUrl.indexOf("?") !== -1) {
                let url = this.info.redirectUrl + '&token=' + this.token
                window.open(url, '_blank')
              } else {
                window.open(this.info.redirectUrl + '?token=' + this.token, '_blank')
              }
            }
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.app-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  padding: 10px 0;
  cursor: pointer;
}

.app-box-more {
  width: 100px;
  display: flex;
  margin: 10px;
  flex-direction: column;
  align-items: center;
  border-radius: 10px;
  padding: 10px 0;
  cursor: pointer;
}
</style>
