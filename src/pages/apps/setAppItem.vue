<template>
  <div class="soft-box" @click="$emit('appClick')">
    <div class="right">
      <div style="position: relative"
           :class="[(isShow&&isAdd&&!isDrag)?'cursor-move':'pointer',isDrag?'cursor_text':'']">
        <img style="width: 60px;height:60px;"
             :src="item.iconUrl?item.iconUrl:img">
        <div class="icon-tag">
          <div v-if="!isAll">
            <img v-if="isShow&&isAdd===true" src="@/assets/images/delete.png"
                 @click="$emit('deleteApp')">
          </div>
          <div v-if="isAll">
            <img v-if="isShow&&isAdd===false" src="@/assets/images/add.png"
                 @click="$emit('addApp')">
            <div v-if="isAdd"
                 class="flex_alAndJsCenter"
                 style="background:#9B9B9B;width: 16px;height:16px;border-radius: 100px;cursor: text">
              <i class="el-icon-check c_fff fs_14"></i>
            </div>
          </div>
        </div>

      </div>
      <div>{{ item.appName }}</div>
    </div>
    <div v-if="isShow&&isAdd===true&&item.pcTag=='1'&&item.mobileTag==='0'"
         class="tag">移动端
    </div>
  </div>
</template>
<script>
export default {
  props: {
    imgSrc: {
      type: String,
      default: ''
    },
    appTitle: {
      type: String,
      default: ''
    },
    src: {
      type: String,
      default: ''
    },
    isShow: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: Boolean,
      default: false
    },
    add: {
      type: String,
      default: ''
    },
    isAll: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      default: function () {
        return {}
      }
    },
    isDrag: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    bgStyle() {
      if (this.item.iconColor) {
        return {
          background: 'linear-gradient(107deg,#ffffff 0%, ' + this.item.iconColor + ' 100%)'
        }
      } else {
        return {
          background: 'linear-gradient(107deg,#ffffff 0%, #ECF4FA 100%)'
        }
      }
    },
  },
  data() {
    return {
      img: require('@/pages/images/Group 3377.png'),
    }
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.soft-box {
  position: relative;
  font-size: 14px;
  padding: 10px 0;
  justify-content: space-between;
  border-radius: 10px;
  align-items: center;

  .right {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  img {
    width: 16px;
    height: 16px;
  }

  .tag {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 12px;
    padding: 4px 10px;
    color: #fff;
    border-top-right-radius: 10px;
    //border-radius: 10px;
    background: #4C94F7;
  }
}

.cursor_text {
  cursor: text;
}

.icon-tag {
  cursor: pointer;
  position: absolute;
  top: -8px;
  right: -8px;
}
</style>
