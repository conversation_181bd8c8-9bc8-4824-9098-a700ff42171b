<template>
  <div class="news-list-box" v-loading="vLoading">
    <Title title="新闻列表" class="title"></Title>
    <div>
      <News
          v-for="item in news"
          :show-pic="true"
          :key="item.id"
          :data="item"></News>
    </div>
    <div v-if="news.length==0">
      <el-empty></el-empty>
    </div>
    <div style="text-align: center">
      <el-pagination
          :page-size="listQuery.pageSize"
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="listQuery.pageNum"
          @current-change="handleCurrentChange">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import Title from "@/components/Title.vue";
import News from "@/components/News.vue";
import {getNewsList} from "@/api/home";

export default {
  components: {News, Title},
  data() {
    return {
      news: [],
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        status: '1'
      },
      vLoading:false,
      total: 0
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.vLoading=true
      getNewsList(this.listQuery).then((res) => {
        this.news = res.rows
        this.total = res.total
      }).finally(()=>{
        this.vLoading=false
      })
    },
    handleCurrentChange(page){
      this.listQuery.pageNum=page
      this.loadData()
    }
  }
}
</script>
<style lang="scss" scoped>
.news-list-box {
  background: #fff;
  padding:10px;
  border-radius: 10px;
}
</style>
