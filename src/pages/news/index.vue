<template>
  <div>
    <Header @refresh="refresh"></Header>
    <div style="padding-top: 75px"></div>
    <div class="container sub-page-container" v-loading="vLoading">
      <div class="left_box box">
        <div style="padding:0 20px;">
          <div style="text-align: center;font-size: 24px;font-weight: bold;margin-bottom: 20px">{{ info.title }}</div>
          <el-divider></el-divider>
          <div class="m_t_10 fs_14 tag-box">
            <div>{{ parseTime(info.createTime, '{y}年{m}月{d}日 {h}:{i}:{s}') }}</div>
            <el-divider direction="vertical"></el-divider>
            <div>来源：{{ info.source }}</div>
          </div>
          <el-divider></el-divider>
        </div>
        <div v-if="false" style="margin:10px auto;width:700px;height:400px;">
          <img style="width:700px;height:400px;" :src="info.coverUrl">
        </div>
        <div class="m_t_10">
          <div class="ql-container ql-snow">
            <div class="ql-editor">
              <div v-html="info.content"></div>
            </div>
          </div>
          <!--      <el-input class=" fs_16" v-html="info.content"></el-input>-->
        </div>
      </div>
      <div class="right_box">
        <div class="right-box-container">
          <top-title :icon="3" title="我的资源" @toDetail="toDetail"></top-title>
          <div style="height:300px;overflow-y: auto">
            <new-item v-for="(item,index) in resourceList"
                      :key="index"
                      :info="item"
                      @clickTo="clickTo(item)"
            ></new-item>
          </div>
        </div>
        <div class="right-box-container m_t_20">
          <top-title :icon="2" title="我的工具" @toDetail="toDetail2"></top-title>
          <div style="height:300px;overflow-y: auto">
            <new-item v-for="(item,index) in toolsList"
                      :key="index"
                      :info="item"
                      @clickTo="clickTo(item)"
            ></new-item>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>
<script>
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import {getNewsInfo, getToolsAndResource} from "@/api/home";
import {parseTime} from "@/utils/tools";
import NewItem from "@/pages/news/newItem.vue";
import TopTitle from "@/pages/components/topTitle.vue";
import Header from "@/pages/layout/pageLayout/Header.vue";

export default {
  components: {Header, TopTitle, NewItem},
  data() {
    return {
      info: '',
      vLoading: false,
      resourceList: [],
      id: '',
      toolsList: []
    }
  },
  mounted() {
    this.loadData(this.$route.query.id)
    this.loadResource()
    this.loadTools()
  },
  methods: {
    refresh() {
      this.loadData(this.$route.query.id)
      this.loadResource()
      this.loadTools()
    },
    toDetail() {
      this.$router.push({
        path: '/resources'
      })
    },
    toDetail2() {
      this.$router.push({
        path: '/apps',
        query: {
          type: 2
        }
      })
    },
    parseTime,
    loadData(id) {
      this.vLoading = true
      getNewsInfo(id).then(res => {
        this.info = res.data
      }).finally(() => {
        this.vLoading = false
      })
    },
    loadResource() {
      getToolsAndResource({type: 3}).then(res => {
        this.resourceList = res.rows
      })
    },
    loadTools() {
      getToolsAndResource({type: 2}).then(res => {
        this.toolsList = res.rows
      })
    },
    clickTo(item) {
      // this.$router.push({
      //   path: '/detail',
      //   query: {
      //     id: item.id
      //   }
      // })
      this.loadData(item.id)
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  border-radius: 10px;
}

.container {
  display: flex;
  justify-content: space-between;
  font-size: 16px;

  .left_box {
    padding: 20px 0;
    width: 73%;
    background: linear-gradient(180deg, #FFFFFF 0%, #F7FAFF 100%);
    box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.1);

    .tag-box {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #adadad
    }
  }

  .right_box {
    margin-left: 20px;
    width: 25%;

    .right-box-container {
      background: linear-gradient(180deg, #FFFFFF 0%, #F7FAFF 99%);
      box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      padding: 10px 15px;
    }
  }
}

::v-deep.ql-container.ql-snow {
  border: none !important;
}

::v-deep.el-divider--horizontal {
  margin: 5px 0 !important;

}
</style>
