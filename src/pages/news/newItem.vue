<template>
  <div class="new-item-container pointer" @click="$emit('clickTo')">
    <div class="contents" style="color:#333333;font-size: 14px;">
      {{ info.introduce }}
    </div>
    <div class="flex_c m_t_10" style="color:#999999;font-size: 12px;">
      <div>{{ info.title }}</div>
      <div>{{ parseTime(info.updateTime, '{y}-{m}-{d}') }}</div>
    </div>
    <el-divider></el-divider>
    <div class="tag"></div>
  </div>
</template>
<script>
import {parseTime} from "@/utils/tools";

export default {
  props: {
    info: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    parseTime,
    click() {
      this.$router.push({
        path: '/detail',
        query: {
          id: this.info.id
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.new-item-container {
  margin: 10px 0;
  padding: 0 10px;
  position: relative;

  .contents {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .tag {
    position: absolute;
    left: 0px;
    top: 5px;
    width: 2px;
    height: 10px;
    border-radius: 2px;
    opacity: 1;
    background: #4C94F7;
  }
}

::v-deep.el-divider--horizontal {
  margin: 5px 0 !important;
}
</style>
