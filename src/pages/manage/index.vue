<template>
  <div class="container fs_16 container-box">
    <div class="top-container flex_c">
      <div class="top-user box_shadow flex_c">
        <div class="user-box flex_column_space">
          <div class="flex user-box-top">
            <div style="height:60px;">
              <img
                  :src="avatar"
                  class="avatar"/>
            </div>
            <div class="user-box-right">
              <div class="fs_bold fs_17">{{ name }}</div>
              <div class="fs_12 m_t_5 flex_center"
                   style="border-radius:18px;width: fit-content;
                   background: linear-gradient(180deg, #949EFA 0%, #4D5EF7 99%),linear-gradient(180deg, #94BFFA 0%, #4C94F7 99%);">
                <img src="@/pages/images/img2/sch_tag.png">
                <div style="padding:0 7px 0 5px;" :class="countWidth?'role_tag':''" class="c_fff flex_alAndJsCenter">
                  <tooltip style="width: 100%" :title="role.role_name"></tooltip>
                  <!--                  {{ role.role_name }}-->
                </div>
              </div>
              <div class="fs_14 m_t_5">{{ role.full_name }}</div>
            </div>
            <!--              <img class="m_t_10" src="@/pages/images/img2/sch_man.png">-->
          </div>
          <div>
            <div class="fw_blod flex_center fs_15">
              <div>
                个人信息
              </div>&nbsp;
              <img style="width: 16px;height:16px" src="@/pages/images/img2/personal_info.png">
            </div>
            <div class="m_t_20 role_card">
              <!--              {{userInfo.sex==='1'?'女':'男'}}-->
              <div class="flex_center ">性别：
                <dict-tag
                    :options="dict.type['sys_user_sex']"
                    :value="userInfo.sex"
                    myTag="phrases"/>
              </div>
              <div>生日：{{ userInfo.birthday }}</div>

              <div class="flex_center ">民族：
                <dict-tag
                    :options="dict.type['user_nation']"
                    :value="(userInfo.nation|| '').split(';')"
                    myTag="phrases"/>
              </div>
              <div class="flex_center ">
                <div style="white-space: nowrap">政治面貌：</div>
                <dict-tag
                    :options="dict.type['politics_status']"
                    :value="(userInfo.politicalLandscape|| '').split(';')"
                    myTag="phrases"/>
              </div>
            </div>
          </div>
        </div>
        <div class="no-data">
          <img src="@/pages/images/img2/emp.png">
        </div>
        <div class="navigation">
          <div class="switch"
               v-if="roles.length > 1">
            <el-dropdown trigger="click">
              <div class="change flex_center just_center el-dropdown-link">
                <div>
                  <img src="@/pages/images/img2/ch_tag.png">
                </div>
                <div>切换身份</div>
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                    v-for="(item, index) in roles"
                    :key="index"
                    @click.native="switchHandle(item)">
                  {{ item.child_name }}{{ item.role_name }}（{{ item.full_name }}）
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
      <div v-loading="vLoadingBanner" class="top-carousel box_shadow">
        <Carousel :data="bannerList"></Carousel>
      </div>
    </div>
    <div class="center-container flex_space m_t_20">
      <div class="left-center ">
        <div class="top-center box_shadow">
          <Title :title="'我的应用'" :show-more="false"
                 :btn-title="'应用管理'"
                 :icon="1"
                 :show-btn="true"
                 @btnClick="btnClick(1)"
          >
          </Title>
          <div style="height:235px" v-loading="loadingApp">
            <div v-if="oftenUserApp.length>12" class="top-center-box flex_c">
              <div class="change-icon" @click="appPrev">
                <i style="color:#fff;font-weight: bold" class="el-icon-arrow-left"></i>
              </div>
              <vue-slick-carousel v-if="oftenUserApp.length!==0"
                                  ref="AppCarousel"
                                  class="carousel2 carousel-box flex_space"
                                  v-bind="OptionsApp"
              >
                <div v-for="item in oftenUserApp" :key="item.appId">
                  <app-item :show-more="true" :info="item"></app-item>
                </div>
              </vue-slick-carousel>
              <div class="change-icon" @click="appNext">
                <i style="color:#fff;font-weight: bold" class="el-icon-arrow-right"></i>
              </div>
            </div>
            <div v-else-if="oftenUserApp.length>0&&oftenUserApp.length<=12">
              <div class="apps-box">
                <div v-for="item in oftenUserApp"
                     :key="item.appInfoId">
                  <app-item :info="item"></app-item>
                </div>
              </div>
            </div>
            <div class="" v-else>
              <el-empty :image-size="100" description="暂无应用"></el-empty>
            </div>
          </div>
        </div>
        <div class="foot-center box_shadow">
          <Title :title="'我的工具'" :show-more="false"
                 :btn-title="'工具管理'"
                 :show-btn="true"
                 :icon="2"
                 @btnClick="btnClick(2)"
          >
          </Title>
          <div style="height:210px" v-loading="loadingTools">
            <div v-if="toolsList.length>3" class="foot-center-box flex_c">
              <div class="change-icon" @click="toolsPrev">
                <i style="color:#fff;font-weight: bold" class="el-icon-arrow-left"></i>
              </div>
              <vue-slick-carousel v-if="toolsList.length!==0"
                                  ref="toolsCarousel"
                                  style="width: 800px;"
                                  v-bind="optionTools">
                <div v-for="item in toolsList">
                  <tools :show-more="true" :info="item"></tools>
                </div>
              </vue-slick-carousel>
              <div class="change-icon" @click="toolsNext">
                <i style="color:#fff;font-weight: bold" class="el-icon-arrow-right"></i>
              </div>
            </div>
            <div class="m_20" style="padding:0 20px;margin:20px auto"
                 v-else-if="toolsList.length>0&&toolsList.length<=3">
              <div style="display: grid;align-items: center;grid-template-columns: 250px 250px 250px;
          gap: 25px;">
                <div v-for="item in toolsList">
                  <tools :info="item"></tools>
                </div>
              </div>
            </div>
            <div v-else>
              <el-empty :image-size="100" description="暂无数据"></el-empty>
            </div>
          </div>
        </div>
      </div>
      <div class="right-center box_shadow">
        <div class="message-box">
          <top-title title="通知公告"
                     :icon="4"
                     tipName="查看更多"
                     @toDetail="$router.push('/oa')"></top-title>
          <div class="m_t_10" v-if="oaList.length!==0" v-loading="vLoading1"
               style="overflow-y: auto;height: 505px">
            <div v-for="item in oaList" :key="item.contentId">
              <message :info="item"></message>
            </div>
          </div>
          <div v-if="oaList.length===0" style="text-align: center;margin-top:30px">
            <div class="fs_14">
              <img src="@/assets/images/noData.png">
              <div style="color:rgba(51,51,51,1)">暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="foot-container box_shadow">
      <top-title :title="'我的资源'" :icon="3" @toDetail="$router.push('/resources')"></top-title>
      <div v-if="resourceList.length>4"
           class="m_20"
           style="display: flex;justify-content: space-between;align-items: center">
        <div class="change-icon" @click="ResourcePrev">
          <i style="color:#fff;font-weight: bold" class="el-icon-arrow-left"></i>
        </div>
        <vue-slick-carousel v-if="resourceList.length!==0"
                            ref="ResourceCarousel"
                            style="width: 1100px"
                            v-bind="optionResource">
          <div v-for="item in resourceList" :key="item.id">
            <resource :info="item"
                      :show-type="2"
            ></resource>
          </div>
        </vue-slick-carousel>
        <div class="change-icon" @click="ResourceNext">
          <i style="color:#fff;font-weight: bold" class="el-icon-arrow-right"></i>
        </div>
      </div>
      <div class="m_10" v-else-if="resourceList.length>0&&resourceList.length<=4">
        <div class="resource-box">
          <resource v-for="item in resourceList"
                    :info="item"
                    :show-more="false"
                    :key="item.id"
                    :show-type="2"
          ></resource>
        </div>
      </div>
      <div class="m_10" v-else style="height:200px">
        <el-empty :image-size="100" description="暂无资源"></el-empty>
      </div>
    </div>
  </div>
</template>
<script>
import Title from '../components/title.vue'
import {mapGetters} from 'vuex'
import Message from "@/pages/components/Index/message.vue";
import {getOaList} from "@/api/OA";
import {getOftenApp, getToolsAndResource, setThisRole, setThisRoleNow} from "@/api/home";
import {determineShow} from "@/pages/utils/tools";
import tools from "@/pages/apps/tools.vue";
import AppItem from "@/pages/apps/AppItem.vue";
import Resource from "@/pages/resource/resource.vue";
import Carousel from "@/pages/components/Carousel.vue";
import VueSlickCarousel from 'vue-slick-carousel'
import 'vue-slick-carousel/dist/vue-slick-carousel.css'
import 'vue-slick-carousel/dist/vue-slick-carousel-theme.css'
import {getBanner} from "@/api/app";
import TopTitle from "@/pages/components/topTitle.vue";
import tooltip from "@/components/Tooltip.vue";

export default {
  name: 'manage',
  components: {
    tooltip,
    TopTitle,
    Carousel, Resource, AppItem, tools,
    Message,
    Title,
    VueSlickCarousel
  },
  data() {
    return {
      vLoading: false,
      params: {
        pageNum: 1,
        pageSize: 12,
      },
      oaList: [],
      total: '',
      vLoading1: false,
      roleAppArr: [],
      moreApp: {},
      moreAppTitle: '',
      showAppLen: '',
      isInter: '',
      oftenUserApp: [],
      resourceList: [],
      toolsList: [],
      OptionsApp: {
        infinite: true,
        slidesToShow: 6,
        rows: 2,
        slidesPerRow: 1,
        slidesToScroll: 3,
        // dots: false,
        // infinite: true,
        // initialSlide: 2,
        // speed: 500,
        // rows: 2,
        // slidesToShow: 6,
        // slidesToScroll: 2,
        // arrows: false,
        // swipeToSlide: false
      },
      slidesToScroll: 1,
      slidesToShow: 5,
      optionTools: {
        dots: false,
        infinite: true,
        initialSlide: 2,
        speed: 500,
        slidesToShow: 3,
        slidesToScroll: 2,
        arrows: false,
        swipeToSlide: false
      },
      optionResource: {
        dots: false,
        infinite: true,
        arrows: false,
        speed: 500,
        variableWidth: false,
        slidesToShow: 4, // 同时显示的项目数量
        slidesToScroll: 2, // 每次滚动的项目数量
      },
      bannerList: [],
      vLoadingBanner: false,
      loadingApp: false,
      loadingTools: false
    }
  },
  dicts: ['user_nation', 'politics_status', 'sys_user_sex'],
  computed: {
    ...mapGetters(['name', 'userInfo', 'avatar', 'role', 'roles', 'defRole']),
    countWidth() {
      let val = this.role.role_name
      if (val && val.length > 8) {
        return true
      } else {
        return ''
      }
    },
  },
  created() {
    this.loadOftenAppData()
  },
  mounted() {
    // this.setThisRole()
    this.loadData()
    this.loadResource()
    this.loadTools()
    this.loadBanner()
  },
  methods: {
    setThisRole() {
      setThisRole({
        roleKey: this.role.role_key,
        agencyId: this.role.agency_id,
        stageId: this.role.stage_id
      }).then(res => {
      });
    },
    determineShow,
    loadBanner() {
      this.vLoadingBanner = true
      getBanner({type: 'home'}).then(res => {
        this.bannerList = res.data
      }).finally(() => {
        this.vLoadingBanner = false
      })
    },
    // 常用应用
    loadOftenAppData() {
      this.loadingApp = true;
      this.oftenUserApp = []
      let params = {
        roleKey: this.role.role_key,
        agencyId: this.role.agency_id,
        stageId: this.role.stage_id ? this.role.stage_id : ''
      }
      getOftenApp(params).then(res => {
        if (res.code == 200) {
          let data = res.data
          let arr = []
          for (let i = 0; i < data.length; i++) {
            if ((data[i].pcTag === 0 || data[i].pcTag === '0')
                && (data[i].myApp === 'true' || data[i].myApp === true)) {
              arr.push(data[i])
            }
          }
          this.oftenUserApp = arr
        }
      }).finally(() => {
        this.loadingApp = false;
      })
    },
    loadResource() {
      // , pageNo: 1, pageSize: 4
      getToolsAndResource({type: 3}).then(res => {
        if (res.code == 200) {
          this.resourceList = res.rows
        }
      })
    },
    loadTools() {
      this.loadingTools = true;
      getToolsAndResource({type: 2}).then(res => {
        if (res.code == 200) {
          this.toolsList = res.rows
        }
      }).finally(() => {
        this.loadingTools = false;
      })
    },
    // 通知
    loadData() {
      this.vLoading1 = true
      getOaList({
        type: '', ...this.params,
        agencyId: this.role.agency_id
      }).then(res => {
        if (res.code === 200) {
          this.oaList = res.rows
          this.total = res.total
        }
      }).finally(() => {
        this.vLoading1 = false
      })
    },
    // app
    appPrev() {
      this.$refs.AppCarousel.prev()
    },
    appNext() {
      this.$refs.AppCarousel.next()
    },
    // 工具
    toolsPrev() {
      this.$refs.toolsCarousel.prev()
    },
    toolsNext() {
      this.$refs.toolsCarousel.next()
    },
    // 资源
    ResourceNext() {
      this.$refs.ResourceCarousel.next()
    },
    ResourcePrev() {
      this.$refs.ResourceCarousel.prev()
    },
    handleClick(type) {
      if (type === 2) {
        this.$router.push({
          path: '/oa'
        })
      }
    },
    // title 按钮
    btnClick(type) {
      this.$router.push({
        path: "/apps",
        query: {
          type: type
        }
      })
    },
    switchHandle(item) {
      let params = {
        roleKey: item.role_key,
        agencyId: item.agency_id,
        stageId: item.stage_id
      }
      setThisRoleNow(params).then(() => {
        this.$store.dispatch('SetRole', item).then((res) => {

          if (item.role_type === 'wor') {
            this.$router.push({path: '/school'});
          } else if (item.role_type === 'tea') {
            this.$router.push({path: '/teacher'});
          } else if (item.role_type === 'stu') {
            this.$router.push({path: '/student'});
          } else if (item.role_type === 'par') {
            this.$router.push({path: '/parents'});
          } else {
            this.loadOftenAppData()
            this.loadData()
            this.loadResource()
            this.loadTools()
            this.loadBanner()
          }
        })
      })
    },
  }
}
</script>
<style lang="scss" scoped>

.container-box {
  .top-container {

    .top-user {
      position: relative;
      width: 470px;
      background: url('@/pages/images/img2/bg1.png') no-repeat;
      background-size: 100% 100%;
      padding: 10px 15px;
      height: 250px;

      .user-box {
        color: #042859;
        position: relative;
        width: 210px;
        height: 180px;
        padding: 20px 15px;
        background: url('@/pages/images/img2/man_bg.png') no-repeat;
        background-size: 100% 100%;

        .avatar {
          margin-right: 5px;
          border-radius: 100px;
          width: 60px;
          height: 60px;
          object-fit: fill;
        }

        .role_card {
          display: grid;
          grid-template-columns: 45% 55%;
          gap: 10px;
          font-size: 12px;
          border-radius: 6px;
        }
      }

      .navigation {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;

        .switch {
          text-align: center;
        }

        .change {
          width: 100px;
          height: 40px;
          border-radius: 0px 10px 0px 10px;
          opacity: 1;
          background: #F7FAFF;
          box-shadow: 0px 4px 6px 0px rgba(76, 148, 247, 0.15);
          font-size: 14px;
          color: #042859;
        }
      }

    }

    .top-carousel {
      width: 680px;
      height: 270px;
      border-radius: 10px
    }
  }

}

.center-container {

  .left-center {
    width: 880px;

    .top-center {
      padding: 15px 15px 10px 15px;
      border-radius: 10px;
      background: #fff;

      .top-center-box {
        margin: 10px 0;
      }

      .carousel-box {
        width: 760px;
      }

      .apps-box {
        margin: 15px auto 10px auto;
        display: grid;
        grid-template-columns: repeat(6, 14%);
        gap: 20px 20px;
        justify-content: center;
        align-items: center;
      }
    }

    .foot-center {
      margin-top: 20px;
      padding: 10px 15px;
      border-radius: 10px;
      background: #fff;

      .foot-center-box {
        margin: 10px 0;
      }
    }
  }

  .right-center {
    width: 300px;
    border-radius: 10px;
    background: #fff;

    .message-box {
      padding: 15px;
    }
  }

}

.foot-container {
  margin-top: 20px;
  padding: 15px;
  border-radius: 10px;
  background: #fff;

  .resource-box {
    width: 1170px;
    display: grid;
    grid-template-columns: 277px 277px 277px 277px;
    gap: 20px;
  }
}

::v-deep.el-empty {
  padding: 0 !important;
}

.change-icon {
  background: rgb(204, 204, 204);
  border-radius: 100px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  text-align: center;
  line-height: 20px
}

.change-icon :hover {
  background: rgb(149, 149, 149);
  border-radius: 100px;
  width: 20px;
  height: 20px;
  line-height: 20px
}

</style>
