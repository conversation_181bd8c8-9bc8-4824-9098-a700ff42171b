<template>
  <div class="container">
    <MyDialog v-if="showPhoneDialog"
              @close="(showPhoneDialog = false), closeLogin(), getCode()"
              dialogWidth="450px"
              title="填写用户信息"
              @confirm="bindUser">
      <div style="width: 350px;margin:0 auto">
        <el-form :model="bindParams" :rules="bindRules" ref="bindRef">
          <el-form-item label="账号" prop="phone">
            <el-input v-model="bindParams.phone"
                      auto-complete="new-password"
                      style="width: 290px" placeholder="请输入账号"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input v-model="bindParams.password"
                      auto-complete="new-password"
                      type="password"
                      show-password
                      clearable
                      @keyup.enter.native="bindUser"
                      prefix-icon="el-icon-unlock"
                      style="width: 290px" placeholder="请输入密码"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </MyDialog>
    <MyDialog v-if="showBinded"
              @close="(showBinded = false)"
              dialogWidth="450px"
              title="提示"
              @confirm="bindOwnAccount">
      <div v-loading="bindLoading" style="width: 350px;margin:0 auto">
        <div style="color:#FC5353;font-size: 15px;margin-bottom: 10px">该微信已被{{
            dealWithTel(bindedPhone)
          }}绑定，是否解绑{{
            dealWithTel(bindedPhone)
          }}账号微信，并绑定您的账号，如确认请点击获取验证码填写后点击确定；如取消则会返回登录界面。
        </div>
        <el-form ref="bindRef">
          <el-form-item label="手机号：" prop="phone">
            <div style="display: flex;align-items: center">
              <div style="width: 60%">{{ dealWithTel(bindedPhone) }}</div>
              <div>
                <span v-if="canClick" style="color:#409EFF;cursor: pointer"
                      @click="getVerificationCodeOwn">点击获取</span>
                <span v-if="!canClick" style="color:#999999;">{{ '重新获取' + '(' + totalTime + 's' + ')' }}</span>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="验证码：" prop="password">
            <el-input v-model="smsCode"
                      clearable style="width: 250px"
                      @keyup.enter.native="" placeholder="请输入验证码"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </MyDialog>
    <div class="fade-in"
         style="text-align:center;font-size: 20px;color:#fff;position: absolute;top:100px;width:80%;left: 0;right: 0;margin:auto">
      <!--      -->
      {{ text }}
      <div>
      </div>
    </div>
    <div class="login-box flex">
      <div class="leftBox">
        <img src="@/pages/images/resource_img/login-2.png" style="border-radius: 15px">
      </div>
      <div class="rightBox p_20 flex_column"
           style="position: relative"
           v-loading="loading">
        <div v-show="!isWarning" class="input_box">
          <div style="position: absolute;top:-50px;right:0;cursor: pointer"
               @click="changeLoginType">
            <img style="width: 70px;height:70px;" :src="loginMode==0?code:code2">
          </div>
          <div class="mt_10 title">
            <!--            <img src="@/assets/logo.png"/>-->
<!--            武侯教育智汇云-->
            {{title}}
          </div>
          <div v-show="loginMode == 0">
            <div class="p_tb_20 fw_blod fs_20">用户登录</div>
            <el-form style="width:300px"
                     :model="userInfos"
                     :rules="rules"
                     ref="loginForm">
              <el-form-item prop="username">
                <el-input
                    prefix-icon="el-icon-user"
                    placeholder="请输入账号"
                    clearable
                    v-model="userInfos.username"></el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                    show-password
                    prefix-icon="el-icon-unlock"
                    placeholder="请输入密码"
                    clearable
                    @keyup.enter.native="submit"
                    v-model="userInfos.password"
                    type="password"></el-input>
              </el-form-item>

              <el-form-item prop="code" v-if="false">
                <!--               v-if="captchaEnabled"-->
                <el-input
                    v-model="userInfos.code"
                    prefix-icon="el-icon-document-checked"
                    auto-complete="off"
                    placeholder="验证码"
                    style="width: 63%"
                    @keyup.enter.native="submit"
                >
                  <!--                  <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />-->
                </el-input>
                <div class="login-code">
                  <img :src="codeUrl" @click="getCodeImg" class="login-code-img"/>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button
                    :loading="loginLoading"
                    type="primary" class="primary-btn"
                    style="width: 100%"
                    @click="submit">
                  <span v-if="!loginLoading">登录</span>
                  <span v-if="loginLoading">登录中</span>
                </el-button>
              </el-form-item>
              <el-form-item>
                <div style="display: flex;justify-content: space-between">
                  <el-form-item prop="remember">
                    <el-checkbox
                        label="记住密码"
                        v-model="userInfos.remember"></el-checkbox>
                  </el-form-item>
                  <el-form-item>
                    <div class="user-un-select" style="color:#409EFF;cursor: pointer" @click="findPassword">重置密码
                    </div>
                  </el-form-item>
                </div>
              </el-form-item>

            </el-form>

            <vue2-verify ref="verify" :captcha-type="'blockPuzzle'"
                         :img-size="{width:'400px',height:'200px'}"
                         @success="handleLogin"/>
          </div>
          <div v-show="loginMode==1">
            <div class="p_tb_20 fw_blod fs_20 flex just_center">扫码登录</div>
            <div
                style="margin-top: -12px; text-align: center; color: #000"
                class="fs_14 opacity_4">
              请使用微信扫码关注公众号登录
            </div>
            <div v-loading="logLoading" class="wechart" style="position: relative">
              <img style="height:100%"
                   :src="qrdata.qrdata"
                   :class="showCount?'opacity_2':''"
                   width="100%"/>
              <div v-if="showCount" class="fs_16 pointer refresh" @click="handleRefresh">
                <div style="background: #fff;border-radius: 100px;padding:5px">
                  <i style="font-size:40px" class="el-icon-refresh-left"></i>
                </div>
                <div style="color:#fff;margin-top:5px;font-size: 18px">二维码过期，点击刷新</div>
              </div>
            </div>
          </div>
        </div>
        <div v-show="isWarning" class=""
             style="position: relative" v-loading="resetLoading">
          <div v-if="resetPassword" class="warp2">
            <div class="p_tb_20 fw_blod fs_20 ">重置密码</div>
            <el-form label-width="93px"
                     class="resetPwd"
                     :model="waningParams"
                     :rules="rules2"
                     ref="loginForm2">
              <el-form-item label="账号" prop="username">
                <el-input
                    placeholder="请输入账号"
                    clearable
                    v-model="waningParams.username"></el-input>
              </el-form-item>
              <el-form-item label="旧密码" prop="oldPwd">
                <el-input
                    show-password
                    clearable
                    placeholder="请输入旧密码"
                    v-model="waningParams.oldPwd"
                    type="password"></el-input>
              </el-form-item>
              <el-form-item label="新密码" prop="newPwd">
                <el-input
                    show-password
                    clearable
                    placeholder="请输入新密码"
                    v-model="waningParams.newPwd"
                    type="password"></el-input>
              </el-form-item>
              <el-form-item label="确定新密码" prop="confirmNew">
                <el-input
                    show-password
                    clearable
                    placeholder="请再次输入新密码"
                    @keyup.enter.native="submitReset"
                    v-model="waningParams.confirmNew"
                    type="password"></el-input>
              </el-form-item>
            </el-form>

            <div style="font-size:14px;margin-bottom: 10px;display: flex;padding:0 0 0 18px;color:#F56C6C">
              <div style="width:90px;word-break: keep-all">
                密码规则:
              </div>&nbsp;
              <div>8~20个字符，需含大、小写字母、数字及特殊字符（~!@#$%^&*()[]{}<>?+），不含空格。</div>
            </div>
            <div style="text-align:center;display: flex;justify-content: space-between;
          align-items: center;margin-bottom: 20px;padding:0 20px">
              <el-button style="width:90%"
                         type="primary"
                         @click="back">
                返回
              </el-button>
              <el-button style="width:90%"
                         type="primary"
                         @click="submitReset">
                确定
              </el-button>
            </div>
          </div>


          <!--        找回密码-->
          <div v-if="findPwd" class="warp2">
            <div class="p_tb_20 fw_blod fs_20">找回密码</div>
            <el-form label-width="93px"
                     class="resetPwd"
                     :model="findParams"
                     :rules="rulesFind"
                     ref="loginForm3">
              <el-form-item label="手机号" prop="mobile">
                <el-input
                    placeholder="请输入手机号"
                    clearable
                    auto-complete="new-password"
                    v-model="findParams.mobile"></el-input>
              </el-form-item>
              <el-form-item label="验证码" prop="code">
                <el-input auto-complete="new-password"
                          v-model="findParams.code" style="width: 60%" placeholder="请输入手机验证码"></el-input>
                &nbsp;&nbsp;
                <span v-if="canClick" style="color:#409EFF;cursor: pointer" @click="getVerificationCode">点击获取</span>
                <span v-if="!canClick" style="color:#999999;">{{ '重新获取' + '(' + totalTime + 's' + ')' }}</span>
              </el-form-item>
              <el-form-item label="新密码" prop="newPassword">
                <el-input
                    show-password
                    clearable
                    auto-complete="new-password"
                    placeholder="请输入新密码"
                    v-model="findParams.newPassword"
                    type="password"></el-input>
              </el-form-item>
              <el-form-item label="确定新密码" prop="confirmNew">
                <el-input
                    show-password
                    auto-complete="new-password"
                    clearable
                    placeholder="请再次输入新密码"
                    @keyup.enter.native="confirmFind"
                    v-model="findParams.confirmNew"
                    type="password"></el-input>
              </el-form-item>
            </el-form>
            <div style="font-size:14px;margin-bottom: 10px;display: flex;padding:0 0 0 18px;color:#F56C6C">
              <div style="width:90px;word-break: keep-all">
                密码规则:
              </div>&nbsp;
              <div>8~20个字符，需含大、小写字母、数字及特殊字符（~!@#$%^&*()[]{}<>?+），不含空格。</div>
            </div>
            <div style="text-align:center;display: flex;justify-content: space-between;
          align-items: center;margin-bottom: 20px;padding:0 20px">
              <el-button style="width:90%"
                         type="primary"
                         @click="findBack">
                返回
              </el-button>
              <el-button style="width:90%"
                         type="primary"
                         @click="confirmFind">
                确定
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {isValidPassword} from "@/utils/tools";
import {mapGetters} from "vuex";
import {bindUser, checkLogin, getWxqr, isEnableCaptcha, resetPwd2} from "@/api/login";
import code from "@/pages/images/img2/phoneLogin.png";
import code2 from "@/pages/images/img2/pwdLogin.png";
import {decryptKey, encryptKey} from "@/pages/utils/tools";
import MyDialog from "@/components/MyDialog.vue";
import MyForms from "@/components/MyForms.vue";
import Cookies from 'js-cookie'
import {getSmsCode, resetPwdBySms} from "@/api/home";

import {Base64} from 'js-base64'
import Vue2Verify from "@/pages/components/Verifition/Verify.vue";

const validChangePwd = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('不能为空'))
  } else {
    if (isValidPassword(value)) {
      callback()
    } else {
      return callback(new Error('密码格式错误'))
    }
  }
}
const validatePhone = (rule, value, callback) => {
  const reg = /^[1][3-9][0-9]{9}$/;
  if (value == '' || value == undefined || value == null) {
    callback(new Error('不能为空'));
  } else {
    if ((!reg.test(value)) && value != '') {
      callback(new Error('请输入正确的电话号码'));
    } else {
      callback();
    }
  }
}


export default {
  components: {Vue2Verify, MyForms, MyDialog,},
  data() {
    return {
      code,
      code2,
      title:process.env.VUE_APP_TITLE,
      loginMode: 0,
      loading: false,
      showLogin: false,
      userInfos: {
        username: '',
        password: '',
        remember: '',
        code: '',
        captchaVerification: ''
      },
      isWarning: false,
      waningParams: {
        username: '',
        newPwd: '',
        oldPwd: '',
        confirmNew: ''
      },
      findParams: {
        mobile: '',
        code: '',
        newPassword: '',
        confirmNew: ''
      },
      rulesFind: {
        mobile: [
          {
            required: true,
            validator: validatePhone,
            trigger: 'blur',
          },
        ],
        code: [
          {
            required: true,
            message: '请输入验证码',
            trigger: 'blur',
          },
        ],
        newPassword: [
          {
            required: true,
            validator: validChangePwd,
            trigger: 'blur',
          },
        ],
        confirmNew: [
          {
            required: true,
            validator: validChangePwd,
            trigger: 'blur',
          },
        ]
      },
      activeStep: 0,
      rules2: {
        username: [
          {
            required: true,
            message: '请输入账号',
            trigger: 'blur',
          },
        ],
        oldPwd: [
          {
            required: true,
            message: '请输入旧密码',
            trigger: 'blur',
          },
        ],
        newPwd: [
          {
            required: true,
            validator: validChangePwd,
            trigger: 'blur',
          },
        ],
        confirmNew: [
          {
            required: true,
            validator: validChangePwd,
            trigger: 'blur',
          },
        ]
      },
      // text: '各位老师：武侯教育智汇云平台正在进行国产化升级改造，目前OA系统已完成，可正常使用，其它应用在升级过程中暂停使用。敬请期待全新的武侯教育智汇云平台。',
      // text: '各位老师：教育智汇云平台正在进行国产化升级改造，目前OA系统已完成，可正常使用，其它应用在升级过程中暂停使用。敬请期待全新的教育智汇云平台。',
      text: '',
      showPhoneDialog: false,
      qrdata: {},
      rules: {
        username: [
          {
            required: true,
            message: '请输入账号',
            trigger: 'blur',
          },
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur',
          },
        ],
      },
      bindParams: {
        phone: '',
        password: ''
      },
      bindRules: {
        phone: [
          {
            required: true,
            message: '请输入账号',
            trigger: 'blur',
          },
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur',
          }
        ]
      },
      bindInfo: [
        {
          prop: 'phone',
          label: '手机号',
          placeholder: '请输入手机号',
          type: 'input',
          default: '',
          required: true,
        },
        {
          prop: 'password',
          label: '密码',
          placeholder: '请输入密码',
          type: 'password',
          default: '',
          required: true,
        },
      ],
      countTimer: '',
      countTime: '',
      timer: 0,
      showCount: false,
      loginLoading: false,
      // 验证码开关
      captchaEnabled: true,
      codeUrl: "",
      resetPassword: false,
      findPwd: false,
      isFollow: false,
      canClick: true,
      totalTime: 60,
      againTxt: '',
      logLoading: '',
      bindedPhone: '',
      showBinded: false,
      smsCode: '',
      bindLoading: false,
      resetLoading: false,
      finalParams: {},//保存正确的手机号，如修改默认密码
      isEnableCaptchaObj: {
        type: ''
      }
    }
  },

  computed: {
    ...mapGetters(['name', 'userInfo', 'avatar', 'role', 'roles', 'defRole']),
  },
  created() {
    this.getUserInfo();
  },
  watch: {
    loginMode: {
      immediate: true,
      handler(val) {
        clearInterval(this.timer);
        //显示二维码
        if (val) {
          this.lock = true;
          this.getCode();
        }
      },
    },
  },
  mounted() {
    this.getCodeImg();
  },
  methods: {
    getCodeImg() {
      const newTime = new Date().getTime()
      isEnableCaptcha().then(res => {
        if (res.code == 200) {
          let timeDef = res.time - newTime;
          this.$store.commit('SET_TIME', timeDef)
          this.isEnableCaptchaObj = res
        }
      })
    },
    getUserInfo() {
      if (Cookies.get('wjpdKey')) {
        this.userInfos = {
          username: Cookies.get('whkjusername'),
          remember: Boolean(Cookies.get('whkjremember')),
          password: decryptKey(),
          code: '',
        }
      }
    },
    resetPwd() {
      this.isWarning = true
      this.resetPassword = true
      this.waningParams.username = this.userInfos.username
      this.waningParams.oldPwd = this.userInfos.password
      this.waningParams.newPwd = ''
      this.waningParams.confirmNew = ''
    },
    findPassword() {
      this.isWarning = true;
      this.findPwd = true;
      this.findParams = {
        mobile: '',
        newPwd: '',
        oldPwd: '',
        confirmNew: ''
      }
    },
    // 确定找回密码
    confirmFind() {
      let params = {
        code: this.findParams.code,
        mobile: this.findParams.mobile,
        newPassword: this.findParams.newPassword
      }
      this.$refs.loginForm3.validate(valid => {
        if (valid) {
          if (this.findParams.newPassword !== this.findParams.confirmNew) {
            this.$message({
              message: '两次新密码不一致,请重新输入！',
              type: 'error'
            })
            return
          }
          resetPwdBySms(params).then(res => {
            if (res.code === 200) {
              this.$message({
                message: '设置成功，请使用新密码登录!',
                type: 'success'
              })
            }
            this.userInfos.password = ''
            this.findBack();
            this.getCodeImg()
          })
        }
      })
    },
    findBack() {
      this.findPwd = false;
      this.isWarning = false;
      this.findParams = {
        phone: '',
        newPwd: '',
        oldPwd: '',
        confirmNew: ''
      }
    },
    // 获取验证码
    getVerificationCode() {
      if (!this.canClick) {
        return
      }
      if (this.findParams.mobile == '') {
        this.$message({
          message: '请输入手机号',
          type: 'error'
        })
        return;
      }
      let params = {
        mobile: this.findParams.mobile,
        scene: 4
      }
      const loading = this.$loading({
        lock: true,
        text: '验证码获取中',
        background: 'rgba(0,0,0,0.7)'
      })
      getSmsCode(params).then(res => {
        this.canClick = false
        this.setInterValMin()
      }).finally(() => {
        loading.close()
      })
    },
    setInterValMin() {
      let timer = setInterval(() => {
        this.totalTime--
        if (this.totalTime < 0) {
          clearInterval(timer)
          this.totalTime = 60;
          this.canClick = true;
        }
      }, 1000)
    },
    back() {
      this.isWarning = false;
      this.resetPassword = false;
      this.waningParams.newPwd = '';
      this.waningParams.confirmNew = '';
    },
    submitReset() {
      this.$refs.loginForm2.validate(valid => {
        if (valid) {
          if (this.waningParams.newPwd != this.waningParams.confirmNew) {
            this.$message({
              message: '两次新密码不一致,请重新输入！',
              type: 'error'
            })
          } else {
            let params = {
              username: this.waningParams.username,
              oldPwd: this.waningParams.oldPwd,
              newPwd: this.waningParams.newPwd
            }
            resetPwd2(params).then(res => {
              this.isWarning = false;
              this.resetPassword = false;

              this.$message({
                message: '修改成功',
                type: 'success'
              })
              let params = {
                username: this.waningParams.username,
                password: this.waningParams.newPwd,
                remember: '',
                code: '',
              }
              this.finalParams = params;
              this.userInfos.username = this.waningParams.username;
              this.userInfos.password = this.waningParams.newPwd;
              this.bindLogin(params)
            })
          }
        }
      })
    },
    //获取二维码
    async getCode() {
      this.times = 0;
      this.qrdata = '';
      //开始计时60秒后刷新
      this.timer = setInterval(() => {
        this.times++;
        if (this.times >= 60) {
          // this.getCode();
          this.times = 0
          clearInterval(this.timer)
        }
        if (this.lock) {
          this.checkLogin();
        }
      }, 1000);
      const {data: result} = await getWxqr();
      this.qrdata = result;
    },
    handleCountTime() {
      const time_count = 60
      if (!this.countTimer) {
        this.countTime = time_count
        this.showCount = false
        this.countTimer = setInterval(() => {
          if (this.countTime > 0 && this.countTime <= time_count) {
            this.countTime--
          } else {
            this.showCount = true
            clearInterval(this.countTimer)
            this.countTimer = null
          }
        }, 1000)
      }
    },
    async handleRefresh() {
      this.showCount = false
      clearInterval(this.countTimer)
      clearInterval(this.timer)
      this.qrdata = ''
      await this.getCode()
      this.handleCountTime()
    },
    // 登录方式切换
    changeLoginType() {
      if (this.isFollow) {
        return
      }
      if (this.loginMode === 0) {
        this.loginMode = 1
        this.handleCountTime()
      } else if (this.loginMode === 1) {
        this.loginMode = 0
        this.qrdata = ''
        clearInterval(this.countTimer)
        clearInterval(this.timer)
      }
    },
    //查询用户登录信息
    async checkLogin() {
      if (this.qrdata) {
        const {data: result} = await checkLogin({sid: this.qrdata.sid});
        //扫码后
        if (result.code === 1003) {
          // this.logLoading = servicesLoading('.login-box', '正在登录', true)
          this.logLoading = true;
        }
        if (result.code != 1001 && result.code !== 1003) {
          this.lock = false;
          clearInterval(this.timer);
          if (result.code == 1002) {
            //   2.用户修改密码后获取修改的新密码或者非默认密码
            // if (this.finalParams.username && this.finalParams.password) {
            //   this.bindUser(this.finalParams)
            // } else {
            // 1.如果直接扫码未绑定则需要填写账号密码
            this.showPhoneDialog = true;
            this.$modal.msgWarning('微信号未绑定，请填写信息');
            // }
          }
          if (result.code == 1000) {
            this.$modal.msgSuccess('登录成功');
            await this.$store.dispatch('SetToken', result.token)
            // this.$store.dispatch('setSessionStorage', result.token);
            //获取用户信息
            this.$store.dispatch('GetInfo').then(() => {
              //获取用户角色
              this.$store.dispatch('GetRole').then((res) => {
                //设置第一个角色作为默认角色
                if (res.data.length > 0) {
                  this.$store.dispatch('SetRole', res.data[0]).then(() => {
                    this.switchHandle(res.data[0]);
                  });
                } else {
                  this.$modal.msgWarning('当前用户无角色，请联系管理员');
                }
                this.logLoading = false;
              });

            })
          }
        }
        if (result.code === 1006) {
          // 重置密码
          this.isWarning = true;
          this.resetPassword = true;
          this.waningParams.username = this.userInfos.username
          this.waningParams.oldPwd = this.userInfos.password
          this.waningParams.newPwd = ''
          this.waningParams.confirmNew = ''
          this.loading = false;
          if (this.showPhoneDialog) {
            this.showPhoneDialog = false;
          }
        }
      }
    },
    splitNumber(val) {
      // 检查参数是否为有效的字符串且长度足够
      if (!val || typeof val !== 'string' || val.length < 8) {
        return val || ''; // 如果参数无效，返回原值或空字符串
      }
      return val.substr(0, 3) + '****' + val.substr(7)
    },
    //切换角色
    switchHandle(item) {
      this.$store.dispatch('SetRole', item).then((res) => {
        //先全部跳转oa
        this.$router.push({path: '/oa'})
        // if (item.role_type === 'wor') {
        //   location.href = '/school'
        //   // this.$router.push({path: '/school'});
        // } else if (item.role_type === 'tea') {
        //   location.href = '/teacher'
        //   // this.$router.push({path: '/teacher'});
        // } else if (item.role_type === 'stu') {
        //   location.href = '/student'
        //   // this.$router.push({path: '/student'});
        // } else if (item.role_type === 'par') {
        //   location.href = '/parents'
        //   // this.$router.push({path: '/parents'});
        // } else {
        //   location.href = '/manage'
        //   // this.$router.push({path: '/manage'});
        // }
      })
    },
    //用户普通模式登录
    login() {
      this.showLogin = true;
      if (this.loginMode) {
        this.qrdata = {};
        this.getCode();
      }
    },
    handleLogin(res) {
      this.userInfos.captchaVerification = res.captchaVerification;
      this.loginLoading = true;
      this.$store.dispatch('Login', this.userInfos).then((result) => {
        if (result.code === 1006) {
          // 修改默认密码
          this.loginLoading = false;
          this.isWarning = true;
          this.resetPassword = true;
          this.waningParams.username = this.userInfos.username
          this.waningParams.oldPwd = this.userInfos.password
          this.waningParams.newPwd = ''
          this.waningParams.confirmNew = ''
          this.loading = false;
          if (this.showPhoneDialog) {
            this.showPhoneDialog = false;
          }
        } else if (result.code === 1009) {
          this.loginLoading = false;
          this.isFollow = true;
          this.loginMode = 1;
          this.handleCountTime()
        } else if (result.code === 200) {
          //获取用户信息
          this.$store.dispatch('GetInfo').then(() => {
            //获取用户角色
            this.$store.dispatch('GetRole').then((res) => {
              //设置第一个角色作为默认角色
              if (res.data.length > 0) {
                this.$modal.msgSuccess('登录成功');
                this.$store.dispatch('SetRole', res.data[0]).then(() => {
                  this.switchHandle(res.data[0]);
                });
              } else {
                this.switchHandle({});
                this.$modal.msgWarning('当前用户无角色，请联系管理员');
              }
              this.loginLoading = false;
            })
          }).finally(() => {
            this.loginLoading = false;
          })
          this.showLogin = false;
          this.loading = false;
          if (this.userInfos.remember == true) {
            Cookies.set("whkjusername", this.userInfos.username, {expires: 30});
            encryptKey(this.userInfos.password)
            Cookies.set('whkjremember', this.userInfos.remember, {expires: 30});
          } else {
            Cookies.remove("whkjusername");
            Cookies.remove("wjpdKey");
            Cookies.remove('whkjremember');
          }
        } else {
          this.loginLoading = false
        }
      }).catch((err) => {
        this.loading = false;
        this.loginLoading = false
      }).finally(() => {
        this.loading = false
      })
    },
    submit(params) {
      this.$refs['loginForm'].validate((valid) => {
        if (valid) {
          if (this.isEnableCaptchaObj.captchaEnabled) {
            this.$refs.verify.show()
          } else {
            this.handleLogin({})
          }
        }
      })
    },
    //关闭登录弹窗
    closeLogin() {
      setTimeout(() => {
        clearInterval(this.timer);
      }, 1000);
      clearInterval(this.countTimer)
      this.lock = true;
      this.loginMode = 0
    },
    bindLogin(params) {
      this.resetLoading = true
      this.$store
          .dispatch('Login', params)
          .then((result) => {
            this.loginLoading = false
            if (result.code === 1006) {
              this.isWarning = true;
              this.resetPassword = true;
              this.waningParams.username = params.username
              this.waningParams.oldPwd = params.password
              this.waningParams.newPwd = ''
              this.waningParams.confirmNew = ''
              this.loading = false;
              if (this.showBinded) {
                this.showBinded = false;
              }
              this.showPhoneDialog = false;
            } else if (result.code === 1009) {
              // this.$message({
              //   message: '您还未关注微信公众号，请扫码关注',
              //   type: 'error'
              // })
              this.isFollow = true;
              this.loginMode = 1;
              this.handleCountTime()
            } else if (result.code === 200) {
              //获取用户信息
              this.$store.dispatch('GetInfo').then(() => {
                //获取用户角色
                this.$store.dispatch('GetRole').then((res) => {
                  //设置第一个角色作为默认角色
                  if (res.data.length > 0) {
                    this.$modal.msgSuccess('登录成功');
                    this.$store.dispatch('SetRole', res.data[0]).then(() => {
                      this.switchHandle(res.data[0]);
                    });
                  } else {
                    this.switchHandle({});
                    this.$modal.msgWarning('当前用户无角色，请联系管理员');
                  }
                  this.loginLoading = false
                })
              })
              this.showLogin = false;
              this.loading = false;
              if (this.userInfos.remember == true) {
                Cookies.set("whkjusername", params.username, {expires: 30});
                encryptKey(params.password);
                Cookies.set('whkjremember', this.userInfos.remember, {expires: 30});
              } else {
                Cookies.remove("whkjusername");
                Cookies.remove("wjpdKey");
                Cookies.remove('whkjremember');
                // clear('loginInfo');
              }
            } else {
              this.loginLoading = false
            }
          })
          .catch((err) => {
            this.loading = false;
            this.loginLoading = false
          }).finally(() => {
        this.loading = false;
        this.resetLoading = false;
      })
    },
    //微信绑定用户
    bindUser(params) {
      this.$refs.bindRef.validate(valid => {
        if (valid) {
          let bindParams = {
            sid: this.qrdata.sid,
            phone: this.bindParams.phone,
            password: this.bindParams.password
          }
          bindUser(bindParams).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '绑定成功'
              })
              let bindParams = {
                username: this.bindParams.phone,
                password: this.bindParams.password,
                remember: '',
                code: '',
              }
              this.bindLogin(bindParams)
            } else if (res.code == 1010) {
              this.$message({
                message: res.msg,
                type: 'error'
              })
              this.showBinded = true;
              this.bindedPhone = Base64.decode(res.data.p)
            }
          });
        }
      })
    },
    // 绑定自己的账号 解绑
    bindOwnAccount() {
      if (this.smsCode) {
        let params = {
          sid: this.qrdata.sid,
          smsCode: this.smsCode,
          phone: this.bindParams.phone,
          oldPhone: this.bindedPhone,
          password: this.bindParams.password
        }
        bindUser(params).then((res) => {
          if (res.code == 200) {
            let bindParams = {
              username: this.bindParams.phone,
              password: this.bindParams.password,
              remember: '',
              code: '',
            }
            this.$message({
              type: 'success',
              message: '绑定成功'
            })
            this.bindLogin(bindParams)
          } else if (res.code == 1010) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        });
      } else {
        this.$message({
          message: '请填写验证码',
          type: 'error'
        })
      }
    },
    getVerificationCodeOwn() {
      let params = {
        mobile: this.bindedPhone,
        scene: 5
      }
      const loading = this.$loading({
        lock: true,
        text: '验证码获取中',
        background: 'rgba(0,0,0,0.7)'
      })
      getSmsCode(params).then(res => {
        this.canClick = false
        this.setInterValMin()
      }).finally(() => {
        loading.close()
      })
    },
    dealWithTel(tel) {
      if (tel) {
        var reg = /^(\d{3})\d{4}(\d{4})$/;
        return tel.replace(reg, "$1****$2");
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  background: url("@/pages/images/resource_img/login_bg.png") no-repeat;
  background-size: 100% 100%;

  .login-box {
    width: 1040px;
    margin: auto;
    position: absolute;
    border-radius: 15px;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    //padding: 25px;
    height: 500px;
    //background: url("../assets/images/background_input.png") no-repeat;
    background: #fff;
    background-size: 100% 100%;
    .leftBox {
      width: 600px;
      height: 500px;
      border-radius: 10px;
      //background: orange;
      img {
        width: 600px;
        height: 500px;
        border-radius: 10px;
      }
    }

    .rightBox {
      margin-left: 10px;
      width: 400px;
      position: relative;

      .input_box {
        margin: auto;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 25px;
        height: 350px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .logo {
          width: 180px;
          height: 60px;
          //background: url("../assets/images/logo.png") no-repeat;
        }

        .title {
          //font-family: Source Han Serif CN, Source Han Serif CN-700, serif;
          font-family: YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 2.8rem;
          letter-spacing: 0.25rem;
          display: flex;
          justify-content: center;
          align-items: center;
          color: black;

          img {
            height: 3.6rem;
            padding: 0 0.6rem;
          }
        }

        .inputBox {
          margin-top: 20px;
          display: flex;
          flex-direction: column;
          align-items: center;

          .prefix_icon {
            height: 16px;
            width: 16px;
            margin-left: 4px;
          }
        }

        .login_btn {
          font-family: Source Han Serif CN, Source Han Serif CN-700, serif;
          font-weight: 700;
          width: 240px;
          height: 56px;
          line-height: 56px;
          text-align: center;
          //background: url("../assets/images/tips.png") no-repeat;
          background-size: 100% 100%;
        }

        .btn {
          width: 240px;
          height: 40px;
          font-weight: bold;
          line-height: 40px;
          text-align: center;
        }

        .tips {
          margin-top: 10px;
          font-size: 12px;
        }
      }
    }
  }
}

::v-deep.el-input .el-input__inner {
  //background: url("../assets/images/input_box.png") no-repeat;
  background: rgb(243, 245, 249);
  border: none !important;

  &::placeholder {
    color: #999 !important;
  }
}

::v-deep.el-input .el-input__prefix {
  display: flex;
}

::v-deep.el-button {
  padding: 0;
  border: 0px solid #DCDFE6;
  font-weight: 700;
  font-size: 18px;
}

.opacity_2 {
  opacity: .2;
}

.refresh {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: auto;
  background: rgb(76, 76, 76, .8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.login-code-img {
  height: 38px;
}

.fade-in {
  opacity: 0;
  animation: fadeInAnimation 1s ease-in forwards;
}

@keyframes fadeInAnimation {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
