<template>
  <div class="fs_16">
    <Header @refresh="refresh"></Header>
    <div style="padding-top: 75px"></div>
    <div class="sub-page-container home-container">1</div>
  </div>
</template>
<script>
import Header from "@/pages/layout/pageLayout/Header.vue";

export default {
  components: {Header},
  data() {
    return {}
  },
  methods: {
    refresh() {
    }
  }
}
</script>
<style lang="scss" scoped>
.home-container {

}
</style>
