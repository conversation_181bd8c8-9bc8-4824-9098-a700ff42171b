<template>
  <div v-if="role.role_type==='tea'" class="tea_echarts" style="position: relative">
    <div class="click-box" @click="click"></div>
    <div v-loading="loading" class="box" ref="echarts" style="height:220px;cursor:pointer;padding-top:20px;"
         id="echarts"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import {mapGetters} from 'vuex'
import {getAppSpaceToken} from "@/api/home";

export default {
  data() {
    return {
      EchartsData: [],
      loading: false
    }
  },
  computed: {
    ...mapGetters(['role'])
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      if (this.role.role_type === 'tea') {
        this.loadData()
      }
    })
  },
  methods: {
    loadData() {
      // this.loading = true
      // getTeacherEcharts().then(res => {
      //   let data = res.data
      //   let arr = Object.keys(data)
      //   let Arr = []
      //   for (let i = 0; i < arr.length; i++) {
      //     let key = arr[i]
      //     if (key === 'honorNum') {
      //       Arr[0] = {
      //         name: '教师' + '\n' + '荣誉',
      //         value: data[key]
      //       }
      //     }
      //     if (key === 'paperNum') {
      //       Arr[1] = {
      //         name: '论文' + '\n' + '期刊',
      //         value: data[key]
      //       }
      //     }
      //     if (key === 'patentSoftwareNum') {
      //       Arr[2] = {
      //         name: '专业或' + '\n' + '软著',
      //         value: data[key]
      //       }
      //     }
      //     if (key === 'projectNum') {
      //       Arr[3] = {
      //         name: '科研成果' + '\n' + '及课题',
      //         value: data[key]
      //       }
      //     }
      //     if (key === 'winningNum') {
      //       Arr[4] = {
      //         name: '竞赛' + '\n' + '获奖',
      //         value: data[key]
      //       }
      //     }
      //   }
      //   this.EchartsData = Arr
      //   this.drawEcharts()
      // }).finally(() => {
      //   this.loading = false
      // })
      this.EchartsData = [
        {
              name: '论文' + '\n' + '期刊',
          value: 0
        },
        {
              name: '专业或' + '\n' + '软著',
          value: 0
        },
        {
              name: '科研成果' + '\n' + '及课题',
          value: 0
        },
        {
          name: '竞赛' + '\n' + '获奖',
          value: 0
        },
        {
          name: '教师' + '\n' + '荣誉',
          value: 0
        },
      ]
      this.drawEcharts();
    },
    drawEcharts() {
      let chartColumn = echarts.init(this.$refs.echarts)
      let option = {
        color: ['#fd9719'],
        radar: {
          // nameGap:5,
          // shape: 'circle',
          indicator: this.EchartsData.map(item => {
            return {
              text: item.name + '\n' + '(' + item.value + ')'
            }
          }),
          textStyle: {
            fontSize: 44, //外圈标签字体大小
            color: '#5b81cb', //外圈标签字体颜色
          },
          axisName: {
            color: '#09246B',
            borderRadius: 3,
            fontSize: '12px',
          },
          radius: ['10%', '50%'],
          // center: ['50%', '59%']
        },
        tooltip: {
          trigger: 'none'
        },
        series: [
          {
            name: 'Budget vs spending',
            type: 'radar',
            data: [
              {
                value: this.EchartsData.map(item => {
                  return item.value
                }),
                name: 'Allocated Budget',
                areaStyle: {
                  color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
                    {
                      color: 'rgba(234,195,142)',
                      offset: 0
                    },
                    {
                      color: 'rgba(237,215,143, 1)',
                      offset: 1
                    }
                  ]),
                }
              }
            ],
          }
        ]
      };
      chartColumn.setOption(option);
    },
    click() {
      getAppSpaceToken({
        appId: '836f12ff0eb7e970046f2c79c0d8e049',
        appInfoId: '1BADBF6ECB3F4763A3C2585456795B69',
        thisRoleKey: this.role.role_key,
        thisAgencyId: this.role.agency_id,
        thisStageId: this.role.stage_id,
        childId: this.role.child_id
      }).then(res => {
        if (res.code == 200) {
          this.token = res.data
          let url = 'http://wh.21spt.com:8000/teacherlogin.aspx' + '?token=' + res.data + '&touser=1'
          window.open(url, '_blank')
        }
      })
    }
  }
}
</script>
<style lang="scss">
.click-box {
  margin: auto;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 170px;
  width: 170px;
  cursor: pointer;
  position: absolute;
  background: transparent;
  z-index: 999;
}

::v-deep {
  .el-loading-mask {
    //background-color: rgb(255, 255, 255, 0.1) !important;
  }
}
</style>
