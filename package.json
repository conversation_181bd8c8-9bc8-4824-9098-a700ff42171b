{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.2", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build-test": "vue-cli-service build --mode test", "build-prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.1", "@vue-office/docx": "^1.6.2", "@vue/composition-api": "^1.7.2", "animation.css": "^0.1.0", "axios": "^1.4.0", "caniuse-lite": "^1.0.30001628", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "docx-preview": "^0.3.0", "echarts": "^5.5.0", "element-ui": "^2.15.13", "file-saver": "^2.0.5", "fingerprintjs": "^0.5.3", "gm-crypt": "^0.0.2", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "node-polyfill-webpack-plugin": "^2.0.1", "nprogress": "^0.2.0", "sm4js": "^0.0.6", "vue": "^2.6.14", "vue-cropper": "^0.6.2", "vue-demi": "^0.14.10", "vue-quill-editor": "^3.0.6", "vue-router": "^3.6.5", "vue-slick-carousel": "^1.0.6", "vue-template-compiler": "^2.6.14", "vue-watermark": "^0.1.1", "vue2-scale-box": "^0.1.7", "vue2-water-marker": "^0.0.2", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "compression-webpack-plugin": "^11.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "html-webpack-plugin": "^5.5.3", "javascript-obfuscator": "^4.1.1", "js-base64": "^3.7.7", "less": "^4.1.3", "less-loader": "^11.1.3", "sass": "^1.63.3", "sass-loader": "^13.3.2", "script-ext-html-webpack-plugin": "2.1.5", "terser-webpack-plugin": "^5.3.11", "vue-template-compiler": "^2.6.14", "webpack-obfuscator": "^3.5.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"no-unused-vars": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}