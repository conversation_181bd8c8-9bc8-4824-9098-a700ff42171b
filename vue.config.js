'use strict';
const path = require('path');
const Obfuscator = require('webpack-obfuscator');
const TerserPlugin = require('terser-webpack-plugin');

function resolve(dir) {
    return path.join(__dirname, dir);
}

// const CompressionPlugin = require('compression-webpack-plugin');
const name = process.env.VUE_APP_TITLE; // 网页标题
const port = process.env.port || process.env.npm_config_port || 80; // 端口
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
module.exports = {
    // 例如 https://www.tools.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.tools.vip/admin/，则设置 baseUrl 为 /admin/。
    publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
    outputDir: 'dist',
    assetsDir: 'static',
    lintOnSave: process.env.NODE_ENV === 'development',
    // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
    productionSourceMap: false,
    // webpack-dev-server 相关配置
    devServer: {
        host: '0.0.0.0',
        port: port,
        open: false,
        proxy: {
            // detail: https://cli.vuejs.org/config/#devserver-proxy
            '/aouth': {
                //target: 'http://***************:7080/api',
                // target: 'https://whkj.wuhousmartedu.com/api',
                target: 'http://localhost:8086',
                // pathRewrite: {
                //     '^/api': '',
                // },
            },
            ["^/api"]: {
                target: process.env.VUE_APP_BASE_API,
                changeOrigin: true,
                // pathRewrite: {
                //     ['^' + process.env.VUE_APP_BASE_API]: '',
                // },
            },
            // '/api':{
            //   target: 'http://***************:7080/api',
            //   changeOrigin:true,
            //   pathRewrite:{
            //     '^/api':'/api'
            //   }
            // }
        },
        client: {
            overlay: false,
        },
        historyApiFallback: true,
        allowedHosts: 'all',
    },
    css: {
        extract: true,
        loaderOptions: {
            css: {
                importLoaders: 1
            },
            sass: {
                sassOptions: {outputStyle: 'expanded'},
            },
        },
    },
    configureWebpack: {
        // optimization: {
        //     minimizer: [
        //         new TerserPlugin({
        //             terserOptions: {mangle: true},
        //         }),
        //     ],
        // },
        name: name,
        resolve: {
            alias: {
                '@': resolve('src'),
            },
        },
        externals: {
            BMap: 'BMap',
        },
        plugins: [
            // new Obfuscator({
            //     rotateUnicodeArray: true, // 打乱Unicode数组顺序
            // }),
            // new Obfuscator(
            //     {
            //         compact: true,
            //         controlFlowFlattening: true,
            //         controlFlowFlatteningThreshold: 0.75,
            //         controlFlowFlatteningMaxSize: 100,
            //         controlFlowFlatteningMinSize: 50,
            //         controlFlowFlatteningPreserveImportedVariables: true,
            //         splitStrings: true,
            //         splitStringsChunkSize: 100,
            //         shuffleStringArray: true,
            //         shuffleStringArraySeed: 12345,
            //         renameGlobals: true,
            //         transformObjectProperties: true,
            //         transformObjectPropertiesBlacklist: [],
            //         transformArrayIndices: true,
            //         transformArrayIndicesThreshold: 0.75,
            //         transformArrayIndicesMaxSize: 100,
            //         transformArrayIndicesMinSize: 50,
            //     }, [
            //         'js/!(app.**.js)'
            //     ]),
        ]
    },
    chainWebpack(config) {
        config.devtool(process.env.NODE_ENV === "production" ? false : 'source-map');
        config.plugins.delete('preload'); // TODO: need test
        config.plugins.delete('prefetch'); // TODO: need test
        // set svg-sprite-loader
        config.module
            .rule('file')
            .test(/\.xlsx$/)
            .use('file-loader')
            .loader('file-loader')
            .options({
                limit: 10000,
                outputPath: 'file',
            })
            .end();
        config.module.rule('svg').exclude.add(resolve('src/assets/icons')).end();
        config.module
            .rule('icons')
            .test(/\.svg$/)
            .include.add(resolve('src/assets/icons'))
            .end()
            .use('svg-sprite-loader')
            .loader('svg-sprite-loader')
            .options({
                symbolId: 'icon-[name]',
            })
            .end();

        config.when(process.env.NODE_ENV !== 'development', (config) => {
            config
                .plugin('ScriptExtHtmlWebpackPlugin')
                .after('html')
                .use('script-ext-html-webpack-plugin', [
                    {
                        // `runtime` must same as runtimeChunk name. default is `runtime`
                        inline: /runtime\..*\.js$/,
                    },
                ])
                .end();
            config.optimization.splitChunks({
                chunks: 'all',
                cacheGroups: {
                    libs: {
                        name: 'chunk-libs',
                        test: /[\\/]node_modules[\\/]/,
                        priority: 10,
                        chunks: 'initial', // only package third parties that are initially dependent
                    },
                    elementUI: {
                        name: 'chunk-elementUI', // split elementUI into a single package
                        priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                        test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
                    },
                    commons: {
                        name: 'chunk-commons',
                        test: resolve('src/components'), // can customize your rules
                        minChunks: 3, //  minimum common number
                        priority: 5,
                        reuseExistingChunk: true,
                    },
                },
            });
            config.optimization.runtimeChunk('single'),
                {
                    from: path.resolve(__dirname, './public/robots.txt'), //防爬虫文件
                    to: './', //到根目录下
                };
        });

        // const types = ['vue-modules', 'vue', 'normal-modules', 'normal'];
        // types.forEach((type) => {
        //   let rule = config.module.rule('less').oneOf(type);
        //   rule
        //     .use('style-resource')
        //     .loader('style-resources-loader')
        //     .options({
        //       patterns: [path.resolve(__dirname, './src/assets/less/global.less')],
        //     });
        // });

        config.resolve.alias
            .set('@', resolve('src'))
            .set('api', resolve('src/apis'));
    },
};
